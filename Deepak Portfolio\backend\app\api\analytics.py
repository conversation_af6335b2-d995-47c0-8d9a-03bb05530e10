"""
Analytics API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.analytics_service import get_analytics_service

router = APIRouter()

class SessionCreateRequest(BaseModel):
    screen_resolution: Optional[str] = None
    language: Optional[str] = None
    referrer: Optional[str] = None
    utm_source: Optional[str] = None
    utm_medium: Optional[str] = None
    utm_campaign: Optional[str] = None

class PageViewRequest(BaseModel):
    session_id: str
    url: str
    title: str
    time_on_page: Optional[int] = 0
    scroll_depth: Optional[float] = 0.0
    interactions: Optional[Dict[str, Any]] = {}
    exit_page: Optional[bool] = False

class InteractionRequest(BaseModel):
    session_id: str
    type: str
    element_id: Optional[str] = None
    element_class: Optional[str] = None
    element_text: Optional[str] = None
    page_url: str
    x: Optional[int] = None
    y: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = {}

class ContactSubmissionRequest(BaseModel):
    session_id: str
    name: str
    email: str
    subject: str
    message: str
    form_type: Optional[str] = 'contact'

class DownloadRequest(BaseModel):
    session_id: str
    file_name: str
    file_type: str
    file_size: Optional[int] = None
    source: str

class ChatbotInteractionRequest(BaseModel):
    session_id: str
    user_message: str
    bot_response: str
    response_time: Optional[float] = None
    conversation_length: Optional[int] = 1
    resolved: Optional[bool] = False

class PerformanceMetricsRequest(BaseModel):
    session_id: str
    page_url: str
    load_time: float
    first_contentful_paint: Optional[float] = None
    largest_contentful_paint: Optional[float] = None
    cumulative_layout_shift: Optional[float] = None
    first_input_delay: Optional[float] = None
    connection_type: Optional[str] = None

class ErrorLogRequest(BaseModel):
    session_id: str
    error_type: str
    error_message: str
    stack_trace: Optional[str] = None
    page_url: str
    browser_info: Optional[Dict[str, Any]] = {}
    severity: Optional[str] = 'medium'

def get_client_ip(request: Request) -> str:
    """Extract client IP address from request"""
    forwarded = request.headers.get('X-Forwarded-For')
    if forwarded:
        return forwarded.split(',')[0].strip()
    return request.client.host if request.client else 'unknown'

@router.post("/session/create")
async def create_session(
    request: Request,
    session_data: SessionCreateRequest,
    db: Session = Depends(get_db)
):
    """Create a new visitor session"""
    try:
        analytics_service = get_analytics_service(db)
        
        request_data = {
            'ip_address': get_client_ip(request),
            'user_agent': request.headers.get('User-Agent', ''),
            'screen_resolution': session_data.screen_resolution,
            'language': session_data.language,
            'referrer': session_data.referrer,
            'utm_source': session_data.utm_source,
            'utm_medium': session_data.utm_medium,
            'utm_campaign': session_data.utm_campaign
        }
        
        session_id = analytics_service.create_session(request_data)
        
        return {
            'session_id': session_id,
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")

@router.post("/track/page-view")
async def track_page_view(
    page_data: PageViewRequest,
    db: Session = Depends(get_db)
):
    """Track a page view"""
    try:
        analytics_service = get_analytics_service(db)
        analytics_service.track_page_view(page_data.session_id, page_data.dict())
        
        return {
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to track page view: {str(e)}")

@router.post("/track/interaction")
async def track_interaction(
    interaction_data: InteractionRequest,
    db: Session = Depends(get_db)
):
    """Track user interaction"""
    try:
        analytics_service = get_analytics_service(db)
        analytics_service.track_interaction(interaction_data.session_id, interaction_data.dict())
        
        return {
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to track interaction: {str(e)}")

@router.post("/track/contact-submission")
async def track_contact_submission(
    request: Request,
    form_data: ContactSubmissionRequest,
    db: Session = Depends(get_db)
):
    """Track contact form submission"""
    try:
        analytics_service = get_analytics_service(db)
        
        submission_data = form_data.dict()
        submission_data.update({
            'ip_address': get_client_ip(request),
            'user_agent': request.headers.get('User-Agent', '')
        })
        
        analytics_service.track_contact_submission(form_data.session_id, submission_data)
        
        return {
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to track contact submission: {str(e)}")

@router.post("/track/download")
async def track_download(
    request: Request,
    download_data: DownloadRequest,
    db: Session = Depends(get_db)
):
    """Track file download"""
    try:
        analytics_service = get_analytics_service(db)
        
        download_info = download_data.dict()
        download_info.update({
            'ip_address': get_client_ip(request),
            'user_agent': request.headers.get('User-Agent', '')
        })
        
        analytics_service.track_download(download_data.session_id, download_info)
        
        return {
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to track download: {str(e)}")

@router.post("/track/chatbot-interaction")
async def track_chatbot_interaction(
    chat_data: ChatbotInteractionRequest,
    db: Session = Depends(get_db)
):
    """Track chatbot interaction"""
    try:
        analytics_service = get_analytics_service(db)
        analytics_service.track_chatbot_interaction(chat_data.session_id, chat_data.dict())
        
        return {
            'status': 'success',
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to track chatbot interaction: {str(e)}")

@router.get("/dashboard")
async def get_analytics_dashboard(db: Session = Depends(get_db)):
    """Get analytics dashboard data"""
    try:
        analytics_service = get_analytics_service(db)
        dashboard_data = analytics_service.get_analytics_dashboard()
        
        return dashboard_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard data: {str(e)}")

@router.get("/health")
async def analytics_health():
    """Health check for analytics service"""
    return {
        "status": "healthy",
        "service": "Analytics Tracking",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }
