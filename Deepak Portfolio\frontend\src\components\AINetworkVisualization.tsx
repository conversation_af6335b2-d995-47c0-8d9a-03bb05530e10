import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface Neuron {
  id: number;
  x: number;
  y: number;
  z: number;
  layer: number;
  activation: number;
  connections: number[];
  pulsePhase: number;
}

interface Connection {
  from: number;
  to: number;
  weight: number;
  active: boolean;
  signal: number;
}

const AINetworkVisualization: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [neurons, setNeurons] = useState<Neuron[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const animationRef = useRef<number>();

  // Initialize neural network
  useEffect(() => {
    const layers = [8, 12, 16, 12, 6]; // Input -> Hidden -> Output layers
    const newNeurons: Neuron[] = [];
    const newConnections: Connection[] = [];
    let neuronId = 0;

    // Create neurons for each layer
    layers.forEach((layerSize, layerIndex) => {
      const layerX = (layerIndex / (layers.length - 1)) * 800 + 100;
      
      for (let i = 0; i < layerSize; i++) {
        const neuron: Neuron = {
          id: neuronId,
          x: layerX,
          y: (i / (layerSize - 1)) * 400 + 100 + Math.sin(neuronId * 0.5) * 20,
          z: Math.cos(neuronId * 0.3) * 50,
          layer: layerIndex,
          activation: Math.random(),
          connections: [],
          pulsePhase: Math.random() * Math.PI * 2
        };
        newNeurons.push(neuron);
        neuronId++;
      }
    });

    // Create connections between adjacent layers
    let currentNeuronIndex = 0;
    for (let layerIndex = 0; layerIndex < layers.length - 1; layerIndex++) {
      const currentLayerSize = layers[layerIndex];
      const nextLayerSize = layers[layerIndex + 1];
      
      for (let i = 0; i < currentLayerSize; i++) {
        const currentNeuron = newNeurons[currentNeuronIndex + i];
        
        // Connect to neurons in next layer
        for (let j = 0; j < nextLayerSize; j++) {
          const targetNeuronId = currentNeuronIndex + currentLayerSize + j;
          
          if (Math.random() > 0.3) { // 70% connection probability
            const connection: Connection = {
              from: currentNeuron.id,
              to: targetNeuronId,
              weight: (Math.random() - 0.5) * 2,
              active: false,
              signal: 0
            };
            newConnections.push(connection);
            currentNeuron.connections.push(targetNeuronId);
          }
        }
      }
      currentNeuronIndex += currentLayerSize;
    }

    setNeurons(newNeurons);
    setConnections(newConnections);
  }, []);

  // Animation loop
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const animate = (timestamp: number) => {
      // Clear canvas
      ctx.fillStyle = 'rgba(15, 23, 42, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update neuron activations with wave patterns
      setNeurons(prevNeurons => 
        prevNeurons.map(neuron => ({
          ...neuron,
          activation: 0.3 + 0.7 * Math.sin(timestamp * 0.002 + neuron.pulsePhase),
          pulsePhase: neuron.pulsePhase + 0.01
        }))
      );

      // Update connections with signal propagation
      setConnections(prevConnections =>
        prevConnections.map(conn => {
          const fromNeuron = neurons.find(n => n.id === conn.from);
          const active = fromNeuron ? fromNeuron.activation > 0.6 : false;
          return {
            ...conn,
            active,
            signal: active ? Math.sin(timestamp * 0.005) * 0.5 + 0.5 : 0
          };
        })
      );

      // Draw connections
      connections.forEach(conn => {
        const fromNeuron = neurons.find(n => n.id === conn.from);
        const toNeuron = neurons.find(n => n.id === conn.to);
        
        if (fromNeuron && toNeuron) {
          const opacity = conn.active ? 0.8 : 0.2;
          const thickness = conn.active ? 2 : 1;
          
          ctx.strokeStyle = conn.weight > 0 
            ? `rgba(59, 130, 246, ${opacity})` 
            : `rgba(239, 68, 68, ${opacity})`;
          ctx.lineWidth = thickness;
          
          ctx.beginPath();
          ctx.moveTo(fromNeuron.x, fromNeuron.y);
          
          // Add curve for 3D effect
          const midX = (fromNeuron.x + toNeuron.x) / 2;
          const midY = (fromNeuron.y + toNeuron.y) / 2 + fromNeuron.z * 0.1;
          
          ctx.quadraticCurveTo(midX, midY, toNeuron.x, toNeuron.y);
          ctx.stroke();

          // Draw signal pulse
          if (conn.active && conn.signal > 0.3) {
            const pulseX = fromNeuron.x + (toNeuron.x - fromNeuron.x) * conn.signal;
            const pulseY = fromNeuron.y + (toNeuron.y - fromNeuron.y) * conn.signal;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.arc(pulseX, pulseY, 3, 0, Math.PI * 2);
            ctx.fill();
          }
        }
      });

      // Draw neurons
      neurons.forEach(neuron => {
        const size = 8 + neuron.activation * 12;
        const brightness = neuron.activation;
        
        // Glow effect
        const gradient = ctx.createRadialGradient(
          neuron.x, neuron.y, 0,
          neuron.x, neuron.y, size * 2
        );
        
        if (neuron.layer === 0) {
          gradient.addColorStop(0, `rgba(16, 185, 129, ${brightness})`);
          gradient.addColorStop(1, 'rgba(16, 185, 129, 0)');
        } else if (neuron.layer === neurons[neurons.length - 1]?.layer) {
          gradient.addColorStop(0, `rgba(139, 92, 246, ${brightness})`);
          gradient.addColorStop(1, 'rgba(139, 92, 246, 0)');
        } else {
          gradient.addColorStop(0, `rgba(59, 130, 246, ${brightness})`);
          gradient.addColorStop(1, 'rgba(59, 130, 246, 0)');
        }
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(neuron.x, neuron.y, size * 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Core neuron
        ctx.fillStyle = neuron.layer === 0 
          ? `rgba(16, 185, 129, ${0.8 + brightness * 0.2})`
          : neuron.layer === neurons[neurons.length - 1]?.layer
          ? `rgba(139, 92, 246, ${0.8 + brightness * 0.2})`
          : `rgba(59, 130, 246, ${0.8 + brightness * 0.2})`;
        
        ctx.beginPath();
        ctx.arc(neuron.x, neuron.y, size, 0, Math.PI * 2);
        ctx.fill();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [neurons, connections]);

  // Mouse interaction
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    setMousePos({ x, y });

    // Activate nearby neurons
    setNeurons(prevNeurons =>
      prevNeurons.map(neuron => {
        const distance = Math.sqrt((neuron.x - x) ** 2 + (neuron.y - y) ** 2);
        if (distance < 100) {
          return { ...neuron, activation: Math.min(1, neuron.activation + 0.3) };
        }
        return neuron;
      })
    );
  };

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <canvas
        ref={canvasRef}
        width={1000}
        height={600}
        className="w-full h-full pointer-events-auto opacity-30 dark:opacity-50"
        onMouseMove={handleMouseMove}
        style={{
          background: 'transparent',
          mixBlendMode: 'screen'
        }}
      />
      
      {/* AI Processing Labels */}
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1 }}
        className="absolute top-20 left-10 text-green-400 text-sm font-mono"
      >
        INPUT LAYER
        <div className="text-xs text-gray-400">Data Processing</div>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5 }}
        className="absolute top-10 left-1/2 transform -translate-x-1/2 text-blue-400 text-sm font-mono"
      >
        HIDDEN LAYERS
        <div className="text-xs text-gray-400">Feature Extraction</div>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 2 }}
        className="absolute top-20 right-10 text-purple-400 text-sm font-mono"
      >
        OUTPUT LAYER
        <div className="text-xs text-gray-400">Predictions</div>
      </motion.div>

      {/* Live Processing Indicator */}
      <motion.div
        animate={{ 
          scale: [1, 1.1, 1],
          opacity: [0.7, 1, 0.7]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-black/20 backdrop-blur-sm rounded-full px-4 py-2 border border-white/10"
      >
        <div className="flex items-center space-x-2 text-white text-sm">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="font-mono">AI NEURAL NETWORK ACTIVE</span>
        </div>
      </motion.div>
    </div>
  );
};

export default AINetworkVisualization;
