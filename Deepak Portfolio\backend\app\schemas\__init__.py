from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime

# Base schemas
class BaseSchema(BaseModel):
    class Config:
        from_attributes = True

# Contact schemas
class ContactMessageCreate(BaseSchema):
    name: str
    email: EmailStr
    subject: str
    message: str

class ContactMessageResponse(BaseSchema):
    id: int
    name: str
    email: str
    subject: str
    message: str
    read: bool
    created_at: datetime

# Project schemas
class ProjectCreate(BaseSchema):
    title: str
    description: str
    technologies: str
    github_url: Optional[str] = None
    live_url: Optional[str] = None
    image_url: Optional[str] = None
    category: str
    featured: bool = False

class ProjectResponse(BaseSchema):
    id: int
    title: str
    description: str
    technologies: str
    github_url: Optional[str]
    live_url: Optional[str]
    image_url: Optional[str]
    category: str
    featured: bool
    created_at: datetime

# Blog schemas
class BlogPostCreate(BaseSchema):
    title: str
    content: str
    excerpt: str
    slug: str
    image_url: Optional[str] = None
    published: bool = False

class BlogPostResponse(BaseSchema):
    id: int
    title: str
    content: str
    excerpt: str
    slug: str
    image_url: Optional[str]
    published: bool
    created_at: datetime

# Skill schemas
class SkillCreate(BaseSchema):
    name: str
    level: int
    category: str
    icon: Optional[str] = None

class SkillResponse(BaseSchema):
    id: int
    name: str
    level: int
    category: str
    icon: Optional[str]

# Experience schemas
class ExperienceCreate(BaseSchema):
    company: str
    position: str
    description: str
    start_date: datetime
    end_date: Optional[datetime] = None
    current: bool = False
    location: str

class ExperienceResponse(BaseSchema):
    id: int
    company: str
    position: str
    description: str
    start_date: datetime
    end_date: Optional[datetime]
    current: bool
    location: str

# Education schemas
class EducationCreate(BaseSchema):
    institution: str
    degree: str
    field: str
    start_date: datetime
    end_date: Optional[datetime] = None
    current: bool = False
    description: Optional[str] = None

class EducationResponse(BaseSchema):
    id: int
    institution: str
    degree: str
    field: str
    start_date: datetime
    end_date: Optional[datetime]
    current: bool
    description: Optional[str]

# Service schemas
class ServiceCreate(BaseSchema):
    title: str
    description: str
    icon: Optional[str] = None
    featured: bool = False

class ServiceResponse(BaseSchema):
    id: int
    title: str
    description: str
    icon: Optional[str]
    featured: bool

# Testimonial schemas
class TestimonialCreate(BaseSchema):
    name: str
    position: str
    company: str
    content: str
    image_url: Optional[str] = None
    rating: int
    featured: bool = False

class TestimonialResponse(BaseSchema):
    id: int
    name: str
    position: str
    company: str
    content: str
    image_url: Optional[str]
    rating: int
    featured: bool

# Generic response schemas
class SuccessResponse(BaseSchema):
    message: str
    success: bool = True

class ErrorResponse(BaseSchema):
    message: str
    success: bool = False
    error_code: Optional[str] = None
