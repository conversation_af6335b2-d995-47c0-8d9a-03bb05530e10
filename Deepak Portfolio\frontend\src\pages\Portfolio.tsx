import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Project } from '../types/index.ts';
import { projectsAPI } from '../services/api.ts';
import { ArrowTopRightOnSquareIcon, CodeBracketIcon } from '@heroicons/react/24/outline';

const Portfolio: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  // const [categories, setCategories] = useState<string[]>([]); // TODO: Use categories from API
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProjects();
    fetchCategories();
  }, []);

  const fetchProjects = async () => {
    try {
      const response = await projectsAPI.getAll();
      setProjects(response.data);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      // const response = await projectsAPI.getCategories(); // TODO: Use categories from API
      // setCategories(['All', ...response.data]); // TODO: Use categories from API
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const filteredProjects = selectedCategory === 'All' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  // Deepak's Projects
  const sampleProjects: Project[] = [
    {
      id: 1,
      title: 'Semantic Video Search Engine',
      description: 'Scalable semantic video search system combining transcription, embeddings, and vector search using AWS services',
      technologies: 'AWS Transcribe, SageMaker, OpenSearch, Bedrock, Python',
      github_url: 'https://github.com/mrgarg-g1/semantic-video-search',
      live_url: '',
      image_url: '/images/projects/semantic-video-search.jpg',
      category: 'AI/ML',
      featured: true,
      created_at: '2025-01-15'
    },
    {
      id: 2,
      title: 'Mentor Policy Chatbot',
      description: 'Fine-tuned LLaMA model with RAG pipeline for government schemes and education policy assistance',
      technologies: 'Meta LLaMA 3.1-8B, Transformers, LangChain, LoRA',
      github_url: 'https://github.com/mrgarg-g1/policy-chatbot',
      live_url: '',
      image_url: '/images/projects/policy-chatbot.jpg',
      category: 'AI/ML',
      featured: true,
      created_at: '2025-01-10'
    },
    {
      id: 3,
      title: 'Facial Recognition Attendance System',
      description: 'Real-time facial recognition system for employee attendance with 95% accuracy using CNN models',
      technologies: 'OpenCV, TensorFlow, Flask, MySQL, Power BI',
      github_url: 'https://github.com/mrgarg-g1/face-recognition-attendance',
      live_url: '',
      image_url: '/images/projects/facial-recognition.jpg',
      category: 'AI/ML',
      featured: false,
      created_at: '2023-06-15'
    },
    {
      id: 4,
      title: 'Document Classification Pipeline',
      description: 'BERT-based document classification system for legal and insurance documents with 92% accuracy',
      technologies: 'BERT, SpaCy, NER, Flask APIs, Python',
      github_url: 'https://github.com/mrgarg-g1/document-classifier',
      live_url: '',
      image_url: '/images/projects/document-classification.jpg',
      category: 'AI/ML',
      featured: false,
      created_at: '2024-03-20'
    },
    {
      id: 5,
      title: 'Inventory Optimization Dashboard',
      description: 'Dynamic dashboard for inventory optimization with time-series forecasting and procurement planning',
      technologies: 'Power BI, Python, ARIMA, Prophet, Streamlit',
      github_url: 'https://github.com/mrgarg-g1/inventory-optimization',
      live_url: '',
      image_url: '/images/projects/inventory-dashboard.jpg',
      category: 'Data Science',
      featured: false,
      created_at: '2024-01-10'
    },
    {
      id: 6,
      title: 'Automated Web Scraping Framework',
      description: 'ETL pipelines with automated data validation and incremental processing using Airflow',
      technologies: 'Selenium, BeautifulSoup, Pandas, SQLAlchemy, Airflow',
      github_url: 'https://github.com/mrgarg-g1/web-scraping-framework',
      live_url: '',
      image_url: '/images/projects/web-scraping.jpg',
      category: 'Data Engineering',
      featured: false,
      created_at: '2024-08-15'
    }
  ];

  const displayProjects = projects.length > 0 ? filteredProjects : sampleProjects;

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              My <span className="text-gradient">AI & Data Science Portfolio</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Explore my AI/ML projects, data science solutions, and real-world implementations that drive business value
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-white dark:bg-dark-900">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center"
          >
            <div className="flex flex-wrap justify-center gap-4">
              {['All', 'AI/ML', 'Data Science', 'Data Engineering'].map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-primary-600 text-white shadow-lg'
                      : 'bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-dark-600'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="section-padding">
        <div className="container-custom">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {displayProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card overflow-hidden group"
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={project.image_url || '/api/placeholder/600/400'}
                      alt={project.title}
                      className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-4 left-4 right-4 flex space-x-2">
                        {project.github_url && (
                          <a
                            href={project.github_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-white/20 backdrop-blur-sm p-2 rounded-full text-white hover:bg-white/30 transition-all duration-300"
                          >
                            <CodeBracketIcon className="h-5 w-5" />
                          </a>
                        )}
                        {project.live_url && (
                          <a
                            href={project.live_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-white/20 backdrop-blur-sm p-2 rounded-full text-white hover:bg-white/30 transition-all duration-300"
                          >
                            <ArrowTopRightOnSquareIcon className="h-5 w-5" />
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-6 space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                        {project.title}
                      </h3>
                      {project.featured && (
                        <span className="bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 px-2 py-1 rounded-full text-xs font-medium">
                          Featured
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      {project.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.split(', ').map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full text-xs"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Portfolio;
