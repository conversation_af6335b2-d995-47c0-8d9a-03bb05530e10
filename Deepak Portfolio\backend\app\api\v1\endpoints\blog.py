from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import BlogPost
from app.schemas import BlogPostCreate, BlogPostResponse, SuccessResponse
from typing import List, Optional

router = APIRouter()

@router.get("/", response_model=List[BlogPostResponse])
async def get_blog_posts(
    skip: int = 0,
    limit: int = 10,
    published: Optional[bool] = True,
    db: Session = Depends(get_db)
):
    """Get all blog posts"""
    query = db.query(BlogPost)
    
    if published is not None:
        query = query.filter(BlogPost.published == published)
    
    posts = query.offset(skip).limit(limit).all()
    return posts

@router.get("/{slug}", response_model=BlogPostResponse)
async def get_blog_post(slug: str, db: Session = Depends(get_db)):
    """Get a specific blog post by slug"""
    post = db.query(BlogPost).filter(BlogPost.slug == slug).first()
    if not post:
        raise HTTPException(status_code=404, detail="Blog post not found")
    return post
