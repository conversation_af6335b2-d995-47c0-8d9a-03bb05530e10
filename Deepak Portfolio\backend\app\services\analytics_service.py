"""
Analytics service for tracking and analyzing website data
"""

import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
import requests
from user_agents import parse

from app.models.analytics import (
    VisitorSession, PageView, UserInteraction, ContactFormSubmission,
    DownloadTracking, SearchQuery, ChatbotInteraction, PerformanceMetrics, ErrorLog
)

class AnalyticsService:
    def __init__(self, db: Session):
        self.db = db

    def create_session(self, request_data: Dict[str, Any]) -> str:
        """Create a new visitor session"""
        session_id = str(uuid.uuid4())
        
        # Parse user agent
        user_agent = parse(request_data.get('user_agent', ''))
        
        # Get location data (you can integrate with IP geolocation service)
        location_data = self._get_location_data(request_data.get('ip_address'))
        
        session = VisitorSession(
            session_id=session_id,
            ip_address=request_data.get('ip_address'),
            user_agent=request_data.get('user_agent'),
            country=location_data.get('country'),
            city=location_data.get('city'),
            region=location_data.get('region'),
            timezone=location_data.get('timezone'),
            device_type=self._get_device_type(user_agent),
            browser=f"{user_agent.browser.family} {user_agent.browser.version_string}",
            os=f"{user_agent.os.family} {user_agent.os.version_string}",
            screen_resolution=request_data.get('screen_resolution'),
            language=request_data.get('language'),
            referrer=request_data.get('referrer'),
            utm_source=request_data.get('utm_source'),
            utm_medium=request_data.get('utm_medium'),
            utm_campaign=request_data.get('utm_campaign'),
            is_bot=user_agent.is_bot
        )
        
        self.db.add(session)
        self.db.commit()
        return session_id

    def track_page_view(self, session_id: str, page_data: Dict[str, Any]):
        """Track a page view"""
        page_view = PageView(
            session_id=session_id,
            page_url=page_data.get('url'),
            page_title=page_data.get('title'),
            time_on_page=page_data.get('time_on_page', 0),
            scroll_depth=page_data.get('scroll_depth', 0.0),
            interactions=page_data.get('interactions', {}),
            exit_page=page_data.get('exit_page', False)
        )
        
        self.db.add(page_view)
        
        # Update session stats
        session = self.db.query(VisitorSession).filter(
            VisitorSession.session_id == session_id
        ).first()
        if session:
            session.total_page_views += 1
            session.last_activity = datetime.utcnow()
        
        self.db.commit()

    def track_interaction(self, session_id: str, interaction_data: Dict[str, Any]):
        """Track user interaction"""
        interaction = UserInteraction(
            session_id=session_id,
            interaction_type=interaction_data.get('type'),
            element_id=interaction_data.get('element_id'),
            element_class=interaction_data.get('element_class'),
            element_text=interaction_data.get('element_text'),
            page_url=interaction_data.get('page_url'),
            x_coordinate=interaction_data.get('x'),
            y_coordinate=interaction_data.get('y'),
            additional_data=interaction_data.get('additional_data', {})
        )
        
        self.db.add(interaction)
        self.db.commit()

    def track_contact_submission(self, session_id: str, form_data: Dict[str, Any]):
        """Track contact form submission"""
        submission = ContactFormSubmission(
            session_id=session_id,
            name=form_data.get('name'),
            email=form_data.get('email'),
            subject=form_data.get('subject'),
            message=form_data.get('message'),
            form_type=form_data.get('form_type', 'contact'),
            ip_address=form_data.get('ip_address'),
            user_agent=form_data.get('user_agent'),
            spam_score=self._calculate_spam_score(form_data)
        )
        
        self.db.add(submission)
        self.db.commit()

    def track_download(self, session_id: str, download_data: Dict[str, Any]):
        """Track file download"""
        download = DownloadTracking(
            session_id=session_id,
            file_name=download_data.get('file_name'),
            file_type=download_data.get('file_type'),
            file_size=download_data.get('file_size'),
            download_source=download_data.get('source'),
            ip_address=download_data.get('ip_address'),
            user_agent=download_data.get('user_agent')
        )
        
        self.db.add(download)
        self.db.commit()

    def track_chatbot_interaction(self, session_id: str, chat_data: Dict[str, Any]):
        """Track chatbot interaction"""
        interaction = ChatbotInteraction(
            session_id=session_id,
            user_message=chat_data.get('user_message'),
            bot_response=chat_data.get('bot_response'),
            response_time=chat_data.get('response_time'),
            conversation_length=chat_data.get('conversation_length'),
            resolved=chat_data.get('resolved', False)
        )
        
        self.db.add(interaction)
        self.db.commit()

    def get_analytics_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive analytics data for dashboard"""
        now = datetime.utcnow()
        last_30_days = now - timedelta(days=30)
        last_7_days = now - timedelta(days=7)
        today = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Basic metrics
        total_sessions = self.db.query(VisitorSession).count()
        sessions_30_days = self.db.query(VisitorSession).filter(
            VisitorSession.created_at >= last_30_days
        ).count()
        sessions_7_days = self.db.query(VisitorSession).filter(
            VisitorSession.created_at >= last_7_days
        ).count()
        sessions_today = self.db.query(VisitorSession).filter(
            VisitorSession.created_at >= today
        ).count()

        # Page views
        total_page_views = self.db.query(PageView).count()
        page_views_30_days = self.db.query(PageView).filter(
            PageView.timestamp >= last_30_days
        ).count()

        # Top pages
        top_pages = self.db.query(
            PageView.page_url,
            func.count(PageView.id).label('views')
        ).filter(
            PageView.timestamp >= last_30_days
        ).group_by(PageView.page_url).order_by(desc('views')).limit(10).all()

        # Device types
        device_stats = self.db.query(
            VisitorSession.device_type,
            func.count(VisitorSession.id).label('count')
        ).filter(
            VisitorSession.created_at >= last_30_days
        ).group_by(VisitorSession.device_type).all()

        # Geographic data
        country_stats = self.db.query(
            VisitorSession.country,
            func.count(VisitorSession.id).label('count')
        ).filter(
            VisitorSession.created_at >= last_30_days
        ).group_by(VisitorSession.country).order_by(desc('count')).limit(10).all()

        # Contact form submissions
        contact_submissions = self.db.query(ContactFormSubmission).filter(
            ContactFormSubmission.created_at >= last_30_days
        ).count()

        # Downloads
        downloads = self.db.query(DownloadTracking).filter(
            DownloadTracking.timestamp >= last_30_days
        ).count()

        # Chatbot interactions
        chatbot_interactions = self.db.query(ChatbotInteraction).filter(
            ChatbotInteraction.timestamp >= last_30_days
        ).count()

        return {
            'overview': {
                'total_sessions': total_sessions,
                'sessions_30_days': sessions_30_days,
                'sessions_7_days': sessions_7_days,
                'sessions_today': sessions_today,
                'total_page_views': total_page_views,
                'page_views_30_days': page_views_30_days,
                'contact_submissions': contact_submissions,
                'downloads': downloads,
                'chatbot_interactions': chatbot_interactions
            },
            'top_pages': [{'url': page.page_url, 'views': page.views} for page in top_pages],
            'device_stats': [{'device': device.device_type, 'count': device.count} for device in device_stats],
            'country_stats': [{'country': country.country, 'count': country.count} for country in country_stats],
            'generated_at': now.isoformat()
        }

    def _get_location_data(self, ip_address: str) -> Dict[str, str]:
        """Get location data from IP address (placeholder - integrate with real service)"""
        # You can integrate with services like ipapi.co, ipgeolocation.io, etc.
        try:
            # Example with ipapi.co (free tier)
            response = requests.get(f'https://ipapi.co/{ip_address}/json/', timeout=5)
            if response.status_code == 200:
                data = response.json()
                return {
                    'country': data.get('country_name'),
                    'city': data.get('city'),
                    'region': data.get('region'),
                    'timezone': data.get('timezone')
                }
        except:
            pass
        
        return {
            'country': 'Unknown',
            'city': 'Unknown',
            'region': 'Unknown',
            'timezone': 'Unknown'
        }

    def _get_device_type(self, user_agent) -> str:
        """Determine device type from user agent"""
        if user_agent.is_mobile:
            return 'mobile'
        elif user_agent.is_tablet:
            return 'tablet'
        else:
            return 'desktop'

    def _calculate_spam_score(self, form_data: Dict[str, Any]) -> float:
        """Calculate spam score for form submission"""
        score = 0.0
        
        # Simple spam detection rules
        message = form_data.get('message', '').lower()
        
        # Check for spam keywords
        spam_keywords = ['viagra', 'casino', 'loan', 'bitcoin', 'investment']
        for keyword in spam_keywords:
            if keyword in message:
                score += 0.3
        
        # Check for excessive links
        if message.count('http') > 2:
            score += 0.4
        
        # Check for excessive caps
        if len([c for c in message if c.isupper()]) / max(len(message), 1) > 0.5:
            score += 0.2
        
        return min(score, 1.0)

# Global instance
def get_analytics_service(db: Session) -> AnalyticsService:
    return AnalyticsService(db)
