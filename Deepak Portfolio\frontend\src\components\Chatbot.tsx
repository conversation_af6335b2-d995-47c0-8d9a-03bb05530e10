import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatBubbleLeftRightIcon, XMarkIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

interface Message {
  id: number;
  text: string;
  isBot: boolean;
  timestamp: Date;
}

const Chatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const greetingMessages = [
    "Hi! I'm <PERSON><PERSON>'s AI assistant. I can help you explore his expertise in Data Science, AI/ML, and Agentic AI systems.",
    "Hello! I'm here to answer questions about <PERSON><PERSON>'s projects, skills, and experience in cutting-edge AI technologies.",
    "Welcome! I can provide insights into <PERSON><PERSON>'s work with LLMs, Neural Networks, Multi-Cloud AI, and Automation.",
    "Hi there! Ask me about <PERSON><PERSON>'s expertise in N8n, LangChain, Computer Vision, NLP, or any AI/ML topics.",
  ];

  const responses = {
    skills: "Deepak specializes in AI & Machine Learning, Agentic AI & Automation (N8n, LangChain, AutoGen), Large Language Models (GPT-4, Claude, LLaMA), Deep Learning & Neural Networks, Computer Vision & NLP, Multi-Cloud AI Platforms (AWS, Azure, GCP), and advanced problem-solving capabilities.",
    projects: "Deepak has built 16+ AI/ML projects including WAVES Media Recommendation System, Advanced OCR Document Processing, Real-time Anomaly Detection, Intelligent Autofill Systems, Speech Analysis & Emotion Recognition, Bhashini TTS Integration, and Automated Content Categorization. Each project showcases cutting-edge AI technologies and measurable business impact.",
    experience: "Deepak has 3+ years of experience in Data Science and AI Engineering. Currently at Appsquadz Technologies as Data Scientist & AI Engineer, previously at Areness as Data Analyst, and extensive freelance consulting experience building end-to-end ML solutions.",
    education: "Deepak holds a Bachelor's degree in Computer Science Engineering with specialization in Data Science and Machine Learning. He has 10+ certifications including AWS ML Specialty, Google Cloud Data Engineer, Azure AI Engineer, and advanced AI certifications from Stanford and MIT.",
    contact: "You can reach <NAME_EMAIL> or through the contact form. He's available for AI/ML consulting, project collaboration, and technical discussions.",
    agentic: "Deepak is an expert in Agentic AI systems using N8n for workflow automation, LangChain for LLM applications, AutoGen for multi-agent systems, and CrewAI for collaborative AI agents. He builds intelligent automation solutions that can reason, plan, and execute complex tasks.",
    cloud: "Deepak has multi-cloud expertise across AWS SageMaker, Azure OpenAI, Google Cloud AI, Amazon Bedrock, and Vertex AI. He designs scalable AI architectures that leverage the best of each cloud platform for maximum reliability and performance.",
    default: "Sorry, I can only answer questions related to Deepak's AI/ML expertise, data science projects, technical skills, and professional experience. Please ask about his work in artificial intelligence, machine learning, or data analytics."
  };

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const randomGreeting = greetingMessages[Math.floor(Math.random() * greetingMessages.length)];
      setMessages([{
        id: 1,
        text: randomGreeting,
        isBot: true,
        timestamp: new Date()
      }]);
    }
  }, [isOpen]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if (!isOpen) {
      const timer = setTimeout(() => {
        setIsOpen(true);
      }, 5000); // Show after 5 seconds

      return () => clearTimeout(timer);
    }
  }, []);

  const getBotResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase();

    if (message.includes('skill') || message.includes('technology') || message.includes('tool') || message.includes('tech stack')) {
      return responses.skills;
    } else if (message.includes('project') || message.includes('work') || message.includes('portfolio') || message.includes('built')) {
      return responses.projects;
    } else if (message.includes('experience') || message.includes('career') || message.includes('job') || message.includes('background')) {
      return responses.experience;
    } else if (message.includes('education') || message.includes('degree') || message.includes('study') || message.includes('certification')) {
      return responses.education;
    } else if (message.includes('contact') || message.includes('email') || message.includes('reach') || message.includes('hire')) {
      return responses.contact;
    } else if (message.includes('agentic') || message.includes('n8n') || message.includes('langchain') || message.includes('automation') || message.includes('agent')) {
      return responses.agentic;
    } else if (message.includes('cloud') || message.includes('aws') || message.includes('azure') || message.includes('gcp') || message.includes('multi-cloud')) {
      return responses.cloud;
    } else if (message.includes('hello') || message.includes('hi') || message.includes('hey') || message.includes('greet')) {
      return "Hello! I'm here to help you explore Deepak's cutting-edge AI/ML expertise. Ask me about his skills, projects, agentic AI work, or cloud experience!";
    } else {
      return responses.default;
    }
  };

  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: messages.length + 1,
      text: inputText,
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse: Message = {
        id: messages.length + 2,
        text: getBotResponse(inputText),
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.button
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            onClick={() => setIsOpen(true)}
            className="fixed bottom-6 right-6 z-50 bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-110"
          >
            <ChatBubbleLeftRightIcon className="h-6 w-6" />
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
              1
            </div>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 100, scale: 0.8 }}
            className="fixed bottom-6 right-6 z-50 w-80 h-96 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <ChatBubbleLeftRightIcon className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-semibold">Deepak's AI Assistant</h3>
                  <p className="text-xs text-blue-100">Ask about data science expertise</p>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white/80 hover:text-white"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.isBot ? 'justify-start' : 'justify-end'}`}
                >
                  <div
                    className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                      message.isBot
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        : 'bg-blue-500 text-white'
                    }`}
                  >
                    {message.text}
                  </div>
                </motion.div>
              ))}
              
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start"
                >
                  <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </motion.div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask about Deepak's expertise..."
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim()}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
                >
                  <PaperAirplaneIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Chatbot;
