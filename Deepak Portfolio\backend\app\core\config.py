from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = "mysql+pymysql://root:root@db:3306/portfolio_db"
    
    # Security
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS
    CORS_ORIGINS: List[str] = ["http://localhost:3000"]
    
    # Email Configuration
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = ""
    SMTP_PASSWORD: str = ""
    EMAIL_FROM: str = ""
    EMAIL_TO: str = ""
    
    # Personal Information
    FULL_NAME: str = "Deepak Garg"
    PROFESSION: str = "Data Scientist & AI Engineer"
    LOCATION: str = "Noida, India"
    PHONE: str = "+91 7838630502"
    EMAIL: str = "<EMAIL>"
    
    # Social Media
    LINKEDIN_URL: str = "https://linkedin.com/in/deepak-garg-in"
    GITHUB_URL: str = "https://github.com/mrgarg-g1"
    TWITTER_URL: str = "https://twitter.com/your-handle"
    MEDIUM_URL: str = "https://medium.com/@your-username"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
