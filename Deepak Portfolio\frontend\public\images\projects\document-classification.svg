<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="doc" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bg)"/>
  
  <!-- Document Input -->
  <rect x="50" y="150" width="200" height="280" rx="10" fill="white" opacity="0.95"/>
  <rect x="60" y="160" width="180" height="20" rx="5" fill="#ddd"/>
  <rect x="60" y="190" width="150" height="8" rx="4" fill="#bbb"/>
  <rect x="60" y="210" width="170" height="8" rx="4" fill="#bbb"/>
  <rect x="60" y="230" width="140" height="8" rx="4" fill="#bbb"/>
  <rect x="60" y="250" width="160" height="8" rx="4" fill="#bbb"/>
  
  <!-- BERT Model -->
  <rect x="300" y="200" width="200" height="150" rx="20" fill="url(#doc)" opacity="0.9"/>
  <text x="400" y="230" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="18" font-weight="bold">BERT Model</text>
  <text x="400" y="250" text-anchor="middle" fill="#666" font-family="Arial, sans-serif" font-size="12">Natural Language</text>
  <text x="400" y="270" text-anchor="middle" fill="#666" font-family="Arial, sans-serif" font-size="12">Processing</text>
  
  <!-- Neural Network Visualization -->
  <circle cx="350" cy="300" r="8" fill="#4facfe"/>
  <circle cx="380" cy="290" r="8" fill="#4facfe"/>
  <circle cx="380" cy="310" r="8" fill="#4facfe"/>
  <circle cx="420" cy="290" r="8" fill="#4facfe"/>
  <circle cx="420" cy="310" r="8" fill="#4facfe"/>
  <circle cx="450" cy="300" r="8" fill="#4facfe"/>
  
  <!-- Connections -->
  <line x1="358" y1="300" x2="372" y2="295" stroke="#4facfe" stroke-width="2"/>
  <line x1="358" y1="300" x2="372" y2="305" stroke="#4facfe" stroke-width="2"/>
  <line x1="388" y1="290" x2="412" y2="295" stroke="#4facfe" stroke-width="2"/>
  <line x1="388" y1="310" x2="412" y2="305" stroke="#4facfe" stroke-width="2"/>
  <line x1="428" y1="290" x2="442" y2="295" stroke="#4facfe" stroke-width="2"/>
  <line x1="428" y1="310" x2="442" y2="305" stroke="#4facfe" stroke-width="2"/>
  
  <!-- Classification Results -->
  <rect x="550" y="150" width="200" height="280" rx="10" fill="white" opacity="0.95"/>
  <text x="650" y="180" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Classification</text>
  
  <!-- Legal Document -->
  <rect x="570" y="200" width="160" height="40" rx="5" fill="#4CAF50" opacity="0.8"/>
  <text x="650" y="225" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Legal Document</text>
  
  <!-- Insurance Document -->
  <rect x="570" y="250" width="160" height="40" rx="5" fill="#2196F3" opacity="0.8"/>
  <text x="650" y="275" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Insurance Document</text>
  
  <!-- Accuracy -->
  <rect x="570" y="320" width="160" height="30" rx="15" fill="#FF9800" opacity="0.8"/>
  <text x="650" y="340" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">92% Accuracy</text>
  
  <!-- Arrows -->
  <path d="M 260 275 L 290 275" stroke="#fff" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M 510 275 L 540 275" stroke="#fff" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#fff"/>
    </marker>
  </defs>
  
  <!-- Tech Stack -->
  <text x="400" y="500" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">BERT • SpaCy • NER • Flask APIs • 92% Accuracy</text>
</svg>
