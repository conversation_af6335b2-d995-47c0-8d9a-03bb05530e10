import React, { useRef, useMemo, useEffect, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Sphere, Line } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';

interface NeuralNetworkProps {
  isDarkMode: boolean;
  className?: string;
}

interface Neuron {
  id: string;
  position: [number, number, number];
  layer: number;
  activation: number;
  connections: string[];
}

interface Connection {
  id: string;
  from: string;
  to: string;
  weight: number;
  signal: number;
}

// Animated Neuron Component
const AnimatedNeuron: React.FC<{
  neuron: Neuron;
  isDarkMode: boolean;
  isActive: boolean;
}> = ({ neuron, isDarkMode, isActive }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [pulsePhase, setPulsePhase] = useState(Math.random() * Math.PI * 2);
  
  useFrame((state) => {
    if (meshRef.current) {
      // Pulsing effect based on activation
      const pulse = Math.sin(state.clock.elapsedTime * 2 + pulsePhase) * 0.1 + 1;
      const activationScale = 0.8 + neuron.activation * 0.4;
      meshRef.current.scale.setScalar(activationScale * pulse);
      
      // Subtle floating animation
      meshRef.current.position.y = neuron.position[1] + Math.sin(state.clock.elapsedTime + pulsePhase) * 0.1;
    }
  });

  const neuronColor = useMemo(() => {
    if (isDarkMode) {
      return isActive 
        ? new THREE.Color().setHSL(0.5 + neuron.activation * 0.3, 0.8, 0.6)
        : new THREE.Color().setHSL(0.6, 0.4, 0.3);
    } else {
      return isActive
        ? new THREE.Color().setHSL(0.2 + neuron.activation * 0.2, 0.7, 0.5)
        : new THREE.Color().setHSL(0.25, 0.5, 0.4);
    }
  }, [neuron.activation, isDarkMode, isActive]);

  return (
    <Sphere
      ref={meshRef}
      position={neuron.position}
      args={[0.15, 16, 16]}
    >
      <meshStandardMaterial
        color={neuronColor}
        emissive={neuronColor}
        emissiveIntensity={isActive ? 0.4 : 0.1}
        roughness={0.3}
        metalness={0.7}
      />
    </Sphere>
  );
};

// Animated Connection Component
const AnimatedConnection: React.FC<{
  connection: Connection;
  neurons: Map<string, Neuron>;
  isDarkMode: boolean;
}> = ({ connection, neurons, isDarkMode }) => {
  const lineRef = useRef<THREE.Line>(null);
  const [signalPosition, setSignalPosition] = useState(0);

  const fromNeuron = neurons.get(connection.from);
  const toNeuron = neurons.get(connection.to);

  const connectionColor = useMemo(() => {
    const intensity = Math.abs(connection.weight);
    if (isDarkMode) {
      return connection.weight > 0
        ? new THREE.Color().setHSL(0.3, 0.8, 0.4 + intensity * 0.3)
        : new THREE.Color().setHSL(0.0, 0.8, 0.4 + intensity * 0.3);
    } else {
      return connection.weight > 0
        ? new THREE.Color().setHSL(0.25, 0.6, 0.3 + intensity * 0.2)
        : new THREE.Color().setHSL(0.05, 0.6, 0.3 + intensity * 0.2);
    }
  }, [connection.weight, isDarkMode]);

  useFrame((state) => {
    // Animate signal traveling along connection
    setSignalPosition((prev) => (prev + 0.02) % 1);
  });

  if (!fromNeuron || !toNeuron) return null;

  const points = [
    new THREE.Vector3(...fromNeuron.position),
    new THREE.Vector3(...toNeuron.position)
  ];

  return (
    <group>
      {/* Connection line */}
      <Line
        points={points}
        color={connectionColor}
        lineWidth={Math.abs(connection.weight) * 3 + 0.5}
        transparent
        opacity={0.6}
      />
      
      {/* Signal particle */}
      <Sphere
        position={[
          fromNeuron.position[0] + (toNeuron.position[0] - fromNeuron.position[0]) * signalPosition,
          fromNeuron.position[1] + (toNeuron.position[1] - fromNeuron.position[1]) * signalPosition,
          fromNeuron.position[2] + (toNeuron.position[2] - fromNeuron.position[2]) * signalPosition
        ]}
        args={[0.03, 8, 8]}
      >
        <meshBasicMaterial
          color={connectionColor}
          transparent
          opacity={0.8}
        />
      </Sphere>
    </group>
  );
};

// Neural Network Scene
const NeuralNetworkScene: React.FC<{ isDarkMode: boolean }> = ({ isDarkMode }) => {
  const groupRef = useRef<THREE.Group>(null);
  const [activeNeurons, setActiveNeurons] = useState<Set<string>>(new Set());
  
  // Generate neural network architecture
  const { neurons, connections } = useMemo(() => {
    const layers = [6, 10, 12, 8, 4, 2]; // Network architecture
    const neuronsMap = new Map<string, Neuron>();
    const connectionsArray: Connection[] = [];
    
    // Create neurons
    layers.forEach((nodeCount, layerIndex) => {
      for (let i = 0; i < nodeCount; i++) {
        const neuronId = `${layerIndex}-${i}`;
        const y = (i - (nodeCount - 1) / 2) * 0.8;
        const x = (layerIndex - (layers.length - 1) / 2) * 2.5;
        const z = (Math.random() - 0.5) * 1.5;
        
        neuronsMap.set(neuronId, {
          id: neuronId,
          position: [x, y, z],
          layer: layerIndex,
          activation: Math.random(),
          connections: []
        });
      }
    });
    
    // Create connections between adjacent layers
    for (let layer = 0; layer < layers.length - 1; layer++) {
      const currentLayerNeurons = Array.from(neuronsMap.values()).filter(n => n.layer === layer);
      const nextLayerNeurons = Array.from(neuronsMap.values()).filter(n => n.layer === layer + 1);
      
      currentLayerNeurons.forEach(fromNeuron => {
        nextLayerNeurons.forEach(toNeuron => {
          if (Math.random() > 0.2) { // 80% connection probability
            const connectionId = `${fromNeuron.id}-${toNeuron.id}`;
            const weight = (Math.random() - 0.5) * 2; // Weight between -1 and 1
            
            connectionsArray.push({
              id: connectionId,
              from: fromNeuron.id,
              to: toNeuron.id,
              weight,
              signal: 0
            });
            
            fromNeuron.connections.push(toNeuron.id);
          }
        });
      });
    }
    
    return { neurons: neuronsMap, connections: connectionsArray };
  }, []);

  // Simulate neural network activity
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly activate neurons to simulate network activity
      const neuronIds = Array.from(neurons.keys());
      const activeCount = Math.floor(neuronIds.length * 0.3); // 30% active
      const newActiveNeurons = new Set<string>();
      
      for (let i = 0; i < activeCount; i++) {
        const randomNeuron = neuronIds[Math.floor(Math.random() * neuronIds.length)];
        newActiveNeurons.add(randomNeuron);
      }
      
      setActiveNeurons(newActiveNeurons);
      
      // Update neuron activations
      neurons.forEach(neuron => {
        neuron.activation = newActiveNeurons.has(neuron.id) ? Math.random() * 0.8 + 0.2 : Math.random() * 0.3;
      });
    }, 2000);

    return () => clearInterval(interval);
  }, [neurons]);

  useFrame((state) => {
    if (groupRef.current) {
      // Gentle rotation
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.2;
      groupRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.05) * 0.1;
    }
  });

  return (
    <group ref={groupRef}>
      {/* Render neurons */}
      {Array.from(neurons.values()).map(neuron => (
        <AnimatedNeuron
          key={neuron.id}
          neuron={neuron}
          isDarkMode={isDarkMode}
          isActive={activeNeurons.has(neuron.id)}
        />
      ))}
      
      {/* Render connections */}
      {connections.map(connection => (
        <AnimatedConnection
          key={connection.id}
          connection={connection}
          neurons={neurons}
          isDarkMode={isDarkMode}
        />
      ))}
      
      {/* Background grid */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -3, 0]}>
        <planeGeometry args={[15, 10]} />
        <meshBasicMaterial
          color={isDarkMode ? '#001122' : '#f0f8ff'}
          transparent
          opacity={0.1}
          wireframe
        />
      </mesh>
    </group>
  );
};

// Main Realistic Neural Network Component
const RealisticNeuralNetwork: React.FC<NeuralNetworkProps> = ({ isDarkMode, className = '' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8 }}
      className={`w-full h-96 rounded-2xl overflow-hidden ${className}`}
      style={{
        background: isDarkMode
          ? 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)'
          : 'linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 50%, #ddeeff 100%)'
      }}
    >
      <Canvas
        camera={{ position: [8, 4, 8], fov: 60 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting setup */}
        <ambientLight intensity={isDarkMode ? 0.3 : 0.5} />
        <pointLight 
          position={[10, 10, 10]} 
          intensity={1} 
          color={isDarkMode ? '#4a9eff' : '#ffffff'} 
        />
        <pointLight 
          position={[-10, 5, -10]} 
          intensity={0.5} 
          color={isDarkMode ? '#ff6b9d' : '#ffaa88'} 
        />
        <pointLight 
          position={[0, -5, 5]} 
          intensity={0.3} 
          color={isDarkMode ? '#4ecdc4' : '#88ccff'} 
        />
        
        <NeuralNetworkScene isDarkMode={isDarkMode} />
        
        <OrbitControls
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          autoRotate={true}
          autoRotateSpeed={0.5}
          maxPolarAngle={Math.PI / 1.5}
          minDistance={6}
          maxDistance={20}
        />
      </Canvas>
      
      {/* Network info overlay */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-3 rounded-lg backdrop-blur-sm">
        <div className="text-sm font-mono">
          <div>Neural Network Active</div>
          <div className="text-xs opacity-75">6-Layer Deep Architecture</div>
          <div className="text-xs opacity-75">Real-time Signal Processing</div>
        </div>
      </div>
    </motion.div>
  );
};

export default RealisticNeuralNetwork;
