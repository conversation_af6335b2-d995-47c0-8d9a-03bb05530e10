import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CalendarDaysIcon, ClockIcon, UserIcon } from '@heroicons/react/24/outline';

const Blog: React.FC = () => {
  const [email, setEmail] = useState('');
  const [subscribeStatus, setSubscribeStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  };

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setSubscribeStatus('loading');

    // Simulate API call
    setTimeout(() => {
      setSubscribeStatus('success');
      setEmail('');
      setTimeout(() => setSubscribeStatus('idle'), 3000);
    }, 1000);
  };

  // AI/ML focused blog posts
  const blogPosts = [
    {
      id: 1,
      title: 'Fine-tuning LLaMA 3.1 for Government Policy Q&A: A Complete Guide',
      excerpt: 'Deep dive into fine-tuning Meta\'s LLaMA 3.1-8B model for government policy understanding. Learn about LoRA techniques, RAG pipelines, and achieving 92% accuracy in policy Q&A systems.',
      slug: 'fine-tuning-llama-government-policy-qa',
      image_url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80',
      published: true,
      created_at: '2024-01-20T10:00:00Z',
      read_time: '12 min read',
      category: 'AI/ML',
      author: 'Deepak Garg'
    },
    {
      id: 2,
      title: 'Building Semantic Video Search with AWS: From Transcription to Discovery',
      excerpt: 'Learn how to architect a scalable semantic video search system using AWS Transcribe, SageMaker embeddings, and OpenSearch. Achieve 95% accuracy in timestamp-based video discovery.',
      slug: 'semantic-video-search-aws-architecture',
      image_url: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80',
      published: true,
      created_at: '2024-01-15T14:30:00Z',
      read_time: '15 min read',
      category: 'Cloud AI',
      author: 'Deepak Garg'
    },
    {
      id: 3,
      title: 'Multi-Cloud ML: Deploying Models Across AWS and Azure for Maximum Reliability',
      excerpt: 'Explore strategies for deploying machine learning models across multiple cloud platforms. Real-world case study of a churn prediction system that saves $2M+ annually.',
      slug: 'multi-cloud-ml-deployment-strategy',
      image_url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&q=80',
      published: true,
      created_at: '2024-01-10T09:15:00Z',
      read_time: '18 min read',
      category: 'MLOps',
      author: 'Deepak Garg'
    },
    {
      id: 4,
      title: 'From ETL to Intelligence: Building Self-Healing Data Pipelines',
      excerpt: 'Discover how to build robust ETL pipelines with automated validation using Great Expectations and Airflow. Reduce manual intervention by 90% and processing time by 40%.',
      slug: 'self-healing-data-pipelines-airflow',
      image_url: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&q=80',
      published: true,
      created_at: '2024-01-05T16:45:00Z',
      read_time: '14 min read',
      category: 'Data Engineering',
      author: 'Deepak Garg'
    },
    {
      id: 5,
      title: 'Computer Vision in Production: Lessons from Real-time Facial Recognition',
      excerpt: 'Building production-ready computer vision systems requires more than just model accuracy. Learn from real-world deployment experiences and optimization strategies.',
      slug: 'computer-vision-production-lessons',
      image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&q=80',
      published: true,
      created_at: '2023-12-28T11:20:00Z',
      read_time: '16 min read',
      category: 'Computer Vision',
      author: 'Deepak Garg'
    },
    {
      id: 6,
      title: 'Document AI: From OCR to Intelligent Classification',
      excerpt: 'Transform unstructured documents into actionable data using advanced OCR, NLP, and classification techniques. Real-world implementations and best practices.',
      slug: 'document-ai-ocr-classification',
      image_url: 'https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80',
      published: true,
      created_at: '2023-12-20T13:00:00Z',
      read_time: '11 min read',
      category: 'NLP',
      author: 'Deepak Garg'
    }
  ];

  const categories = ['All', 'AI/ML', 'Cloud AI', 'MLOps', 'Data Engineering', 'Computer Vision', 'NLP'];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              AI/ML <span className="text-gradient">Insights</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Deep dives into artificial intelligence, machine learning, and data science - from cutting-edge research to real-world implementations
            </p>
          </motion.div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-white dark:bg-dark-900">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center"
          >
            <div className="flex flex-wrap justify-center gap-4">
              {categories.map((category) => (
                <button
                  key={category}
                  className="px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:bg-primary-600 hover:text-white"
                >
                  {category}
                </button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">Featured Post</h2>
            <div className="card overflow-hidden lg:flex">
              <div className="lg:w-1/2">
                <img
                  src={blogPosts[0].image_url}
                  alt={blogPosts[0].title}
                  className="w-full h-64 lg:h-full object-cover"
                />
              </div>
              <div className="lg:w-1/2 p-8 flex flex-col justify-center">
                <div className="flex items-center gap-4 mb-4">
                  <span className="bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 px-3 py-1 rounded-full text-sm font-medium">
                    {blogPosts[0].category}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(blogPosts[0].created_at)}
                  </span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {blogPosts[0].title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {blogPosts[0].excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <UserIcon className="h-4 w-4" />
                      {blogPosts[0].author}
                    </div>
                    <div className="flex items-center gap-1">
                      <ClockIcon className="h-4 w-4" />
                      {blogPosts[0].read_time}
                    </div>
                  </div>
                  <Link
                    to={`/blog/${blogPosts[0].slug}`}
                    className="btn-primary"
                  >
                    Read More
                  </Link>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Latest Posts</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.slice(1).map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card overflow-hidden group"
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={post.image_url}
                      alt={post.title}
                      className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="bg-white/90 dark:bg-dark-900/90 text-gray-900 dark:text-white px-3 py-1 rounded-full text-sm font-medium">
                        {post.category}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6 space-y-4">
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <CalendarDaysIcon className="h-4 w-4" />
                        {formatDate(post.created_at)}
                      </div>
                      <div className="flex items-center gap-1">
                        <ClockIcon className="h-4 w-4" />
                        {post.read_time}
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                      {post.title}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                          <UserIcon className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {post.author}
                        </span>
                      </div>
                      <Link
                        to={`/blog/${post.slug}`}
                        className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm transition-colors duration-300"
                      >
                        Read More →
                      </Link>
                    </div>
                  </div>
                </motion.article>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="section-padding bg-primary-600 dark:bg-primary-700">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center space-y-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Stay Updated
            </h2>
            <p className="text-xl text-primary-100 max-w-2xl mx-auto">
              Subscribe to my newsletter to get the latest articles and insights delivered to your inbox.
            </p>
            <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Your email address"
                className="flex-1 px-4 py-3 rounded-lg border-none focus:ring-2 focus:ring-primary-300 bg-white text-gray-900"
                required
              />
              <button
                type="submit"
                disabled={subscribeStatus === 'loading'}
                className="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-all duration-300 disabled:opacity-50"
              >
                {subscribeStatus === 'loading' ? 'Subscribing...' : 'Subscribe'}
              </button>
            </form>
            {subscribeStatus === 'success' && (
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-green-300 text-center mt-4"
              >
                Successfully subscribed! Thank you for joining.
              </motion.p>
            )}
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
