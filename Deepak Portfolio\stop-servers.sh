#!/bin/bash
# Stop Development Servers Script
# Author: <PERSON><PERSON>ar<PERSON>
# Description: Script to gracefully stop all development servers

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

echo ""
print_status "🛑 Stopping Portfolio Development Servers..."
print_header "======================================="

# Stop backend server
if [ -f "backend/backend.pid" ]; then
    BACKEND_PID=$(cat backend/backend.pid)
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        print_info "   Stopping backend server (PID: $BACKEND_PID)..."
        kill -9 $BACKEND_PID 2>/dev/null
        rm -f backend/backend.pid
        print_status "✅ Backend server stopped"
    else
        print_warning "   Backend server not running"
        rm -f backend/backend.pid
    fi
else
    print_warning "   No backend PID file found"
fi

# Stop frontend server
if [ -f "frontend/frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend/frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        print_info "   Stopping frontend server (PID: $FRONTEND_PID)..."
        kill -9 $FRONTEND_PID 2>/dev/null
        rm -f frontend/frontend.pid
        print_status "✅ Frontend server stopped"
    else
        print_warning "   Frontend server not running"
        rm -f frontend/frontend.pid
    fi
else
    print_warning "   No frontend PID file found"
fi

# Kill any remaining processes on common ports
print_info "   Checking for remaining processes on common ports..."
for port in 3000 3001 5000 5173 8000 8080 4000; do
    pids=$(lsof -t -i:$port 2>/dev/null)
    if [ ! -z "$pids" ]; then
        print_warning "   Killing remaining processes on port $port..."
        echo $pids | xargs kill -9 2>/dev/null
    fi
done

# Kill any remaining Node.js processes
pkill -f "node" 2>/dev/null
pkill -f "npm" 2>/dev/null
pkill -f "yarn" 2>/dev/null
pkill -f "uvicorn" 2>/dev/null

echo ""
print_status "✅ All development servers stopped successfully!"
print_info "   You can now restart using ./restart-clean.sh"
echo ""
