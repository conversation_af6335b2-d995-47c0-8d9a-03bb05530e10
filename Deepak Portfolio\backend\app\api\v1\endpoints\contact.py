from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import ContactMessage
from app.schemas import ContactMessageCreate, ContactMessageResponse, SuccessResponse
from app.services.email_service import send_contact_email
from typing import List

router = APIRouter()

@router.post("/send", response_model=SuccessResponse)
async def send_contact_message(
    contact_data: ContactMessageCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Send a contact message"""
    # Save to database
    db_message = ContactMessage(**contact_data.dict())
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    
    # Send email in background
    background_tasks.add_task(
        send_contact_email,
        name=contact_data.name,
        email=contact_data.email,
        subject=contact_data.subject,
        message=contact_data.message
    )
    
    return SuccessResponse(message="Message sent successfully!")

@router.get("/messages", response_model=List[ContactMessageResponse])
async def get_contact_messages(
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get all contact messages (admin only)"""
    messages = db.query(ContactMessage).offset(skip).limit(limit).all()
    return messages

@router.put("/messages/{message_id}/read", response_model=SuccessResponse)
async def mark_message_as_read(
    message_id: int,
    db: Session = Depends(get_db)
):
    """Mark a contact message as read"""
    message = db.query(ContactMessage).filter(ContactMessage.id == message_id).first()
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")
    
    message.read = True
    db.commit()
    
    return SuccessResponse(message="Message marked as read")
