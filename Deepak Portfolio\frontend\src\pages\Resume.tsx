import React from 'react';
import { motion } from 'framer-motion';
import { 
  CalendarDaysIcon, 
  MapPinIcon, 
  BuildingOfficeIcon,
  AcademicCapIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const Resume: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  const experiences = [
    {
      company: 'Tech Solutions Inc.',
      position: 'Senior Full Stack Developer',
      location: 'Mumbai, India',
      duration: 'Jan 2022 - Present',
      current: true,
      description: [
        'Led development of scalable web applications using React, Node.js, and Python',
        'Implemented microservices architecture reducing system response time by 40%',
        'Mentored junior developers and conducted code reviews',
        'Collaborated with cross-functional teams to deliver high-quality products'
      ]
    },
    {
      company: 'Digital Innovations Ltd.',
      position: 'Full Stack Developer',
      location: 'Mumbai, India',
      duration: 'Jun 2020 - Dec 2021',
      current: false,
      description: [
        'Developed and maintained multiple client-facing web applications',
        'Integrated third-party APIs and payment gateways',
        'Optimized database queries improving application performance by 30%',
        'Participated in agile development processes and sprint planning'
      ]
    },
    {
      company: 'StartupXYZ',
      position: 'Frontend Developer',
      location: 'Mumbai, India',
      duration: 'Jan 2019 - May 2020',
      current: false,
      description: [
        'Built responsive web applications using React and modern CSS',
        'Implemented state management solutions with Redux',
        'Collaborated with designers to create pixel-perfect UI components',
        'Wrote comprehensive unit tests achieving 90% code coverage'
      ]
    }
  ];

  const education = [
    {
      institution: 'Mumbai University',
      degree: 'Bachelor of Technology',
      field: 'Computer Science Engineering',
      duration: '2015 - 2019',
      description: 'Specialized in software engineering and web technologies',
      achievements: [
        'First Class with Distinction',
        'Best Final Year Project Award',
        'Active member of Computer Society'
      ]
    },
    {
      institution: 'ABC Junior College',
      degree: 'Higher Secondary Certificate',
      field: 'Science (PCM)',
      duration: '2013 - 2015',
      description: 'Science stream with Mathematics, Physics, and Chemistry',
      achievements: [
        '85% in HSC examination',
        'Mathematics Olympiad participant',
        'Science club member'
      ]
    }
  ];

  const skills = [
    { category: 'Frontend', items: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS', 'Vue.js'] },
    { category: 'Backend', items: ['Node.js', 'Python', 'FastAPI', 'Express.js', 'Django'] },
    { category: 'Database', items: ['PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'MySQL'] },
    { category: 'Cloud & DevOps', items: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Terraform'] },
    { category: 'Tools & Others', items: ['Git', 'Jest', 'Webpack', 'Figma', 'Linux'] }
  ];

  const certifications = [
    {
      name: 'AWS Certified Solutions Architect',
      issuer: 'Amazon Web Services',
      date: '2023',
      badge: 'aws'
    },
    {
      name: 'Google Cloud Professional Developer',
      issuer: 'Google Cloud',
      date: '2022',
      badge: 'gcp'
    },
    {
      name: 'React Developer Certification',
      issuer: 'Meta',
      date: '2021',
      badge: 'react'
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              My <span className="text-gradient">Resume</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              A comprehensive overview of my professional journey and technical expertise
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/resume/Deepak-Garg-Resume.pdf" 
                download="Deepak-Garg-Resume.pdf"
                className="btn-primary"
              >
                Download PDF
              </a>
              <button 
                onClick={() => {
                  // Open the PDF in a new window for better printing
                  const printWindow = window.open('/resume/Deepak-Garg-Resume.pdf', '_blank');
                  if (printWindow) {
                    printWindow.onload = () => {
                      printWindow.print();
                    };
                  } else {
                    // Fallback: just open the PDF
                    window.open('/resume/Deepak-Garg-Resume.pdf', '_blank');
                  }
                }}
                className="btn-outline"
              >
                Print Resume
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Work Experience
            </h2>
            
            <div className="space-y-8">
              {experiences.map((exp, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8 relative"
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                        <BuildingOfficeIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                            {exp.position}
                          </h3>
                          <p className="text-primary-600 dark:text-primary-400 font-semibold">
                            {exp.company}
                          </p>
                        </div>
                        {exp.current && (
                          <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                            Current
                          </span>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <CalendarDaysIcon className="h-4 w-4" />
                          {exp.duration}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPinIcon className="h-4 w-4" />
                          {exp.location}
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {exp.description.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-2 text-gray-600 dark:text-gray-400">
                            <CheckCircleIcon className="h-5 w-5 text-primary-600 dark:text-primary-400 flex-shrink-0 mt-0.5" />
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Education Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Education
            </h2>
            
            <div className="space-y-8">
              {education.map((edu, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8"
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center">
                        <AcademicCapIcon className="h-8 w-8 text-secondary-600 dark:text-secondary-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {edu.degree}
                        </h3>
                        <p className="text-secondary-600 dark:text-secondary-400 font-semibold">
                          {edu.institution}
                        </p>
                        <p className="text-gray-600 dark:text-gray-400">
                          {edu.field}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                        <CalendarDaysIcon className="h-4 w-4" />
                        {edu.duration}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400">
                        {edu.description}
                      </p>
                      
                      <ul className="space-y-1">
                        {edu.achievements.map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start gap-2 text-gray-600 dark:text-gray-400">
                            <CheckCircleIcon className="h-4 w-4 text-secondary-600 dark:text-secondary-400 flex-shrink-0 mt-0.5" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Technical Skills
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {skills.map((skillGroup, index) => (
                <motion.div
                  key={skillGroup.category}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-6"
                >
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                    {skillGroup.category}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {skillGroup.items.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Certifications Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Certifications
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-6 text-center"
                >
                  <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircleIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    {cert.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-1">
                    {cert.issuer}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {cert.date}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Resume;
