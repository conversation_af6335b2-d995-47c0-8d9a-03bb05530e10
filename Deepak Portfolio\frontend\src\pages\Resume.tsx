import React from 'react';
import { motion } from 'framer-motion';
import { 
  CalendarDaysIcon, 
  MapPinIcon, 
  BuildingOfficeIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  BookOpenIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';

const Resume: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  };

  const experiences = [
    {
      company: 'Appsquadz Technologies',
      position: 'Data Scientist & AI Engineer',
      location: 'Noida, Uttar Pradesh',
      duration: 'Jan 2025 - Present',
      current: true,
      description: [
        'Developing machine learning models and data analytics solutions for business optimization',
        'Built recommendation systems, fraud detection models, and automated data pipelines',
        'Working on predictive analytics projects that improve business decision-making',
        'Implementing advanced AI/ML solutions using Python, TensorFlow, and cloud platforms'
      ]
    },
    {
      company: 'Areness',
      position: 'Data Engineer',
      location: 'Gurugram, Haryana',
      duration: 'Jun 2024 - Dec 2024',
      current: false,
      description: [
        'Analyzed large datasets to extract business insights and built automated reporting systems',
        'Developed data visualization dashboards and implemented ETL processes',
        'Created predictive models for customer behavior analysis and business forecasting',
        'Collaborated with cross-functional teams to deliver data-driven solutions'
      ]
    },
    {
      company: 'Freelance Data Science Projects',
      position: 'Data Science Consultant',
      location: 'Remote',
      duration: 'Jul 2023 - May 2024',
      current: false,
      description: [
        'Worked on various data science projects including recommendation systems and sentiment analysis',
        'Helped small businesses implement data-driven solutions and automated workflows',
        'Built OCR systems, anomaly detection models, and speech analysis applications',
        'Delivered end-to-end ML solutions from data collection to model deployment'
      ]
    },
    {
      company: 'D.S. Projects Pvt. Ltd.',
      position: 'Data Analyst',
      location: 'On-site',
      duration: 'Jun 2022 - Jun 2023',
      current: false,
      description: [
        'Started data science journey by working on data collection, cleaning, and basic analysis',
        'Learned machine learning fundamentals and built first predictive models',
        'Gained experience in data visualization and statistical analysis',
        'Developed foundational skills in Python, SQL, and data processing techniques'
      ]
    }
  ];

  const education = [
    {
      institution: 'Bennett University',
      degree: 'Master of Computer Application (MCA)',
      field: 'Data Science (9.1 CGPA)',
      duration: 'Aug 2023 – May 2025',
      description: 'Focused on advanced topics in data science, machine learning, and AI. Engaged in research and practical projects.',
      achievements: [
        'Consistent top performer (9.1 CGPA)',
        'Led data science research initiatives',
        'Active member of university AI club'
      ],
      location: 'Greater Noida'
    },
    {
      institution: 'J.C. Bose University of Science and Technology',
      degree: 'Bachelor of Computer Application (BCA)',
      field: 'Data Science (8.5 CGPA)',
      duration: 'Jul 2020 – Sep 2023',
      description: 'Studied core computer science and data science concepts. Participated in technical events and projects.',
      achievements: [
        'Graduated with 8.5 CGPA',
        'Data Science project finalist',
        'Organized coding workshops'
      ],
      location: 'Faridabad'
    }
  ];

  const skills = [
    { category: 'AI & Machine Learning', items: ['Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'XGBoost', 'LightGBM'] },
    { category: 'Agentic AI & Automation', items: ['N8n', 'LangChain', 'AutoGen', 'CrewAI', 'Agentic Workflows', 'AI Orchestration'] },
    { category: 'Large Language Models', items: ['GPT-4', 'Claude', 'LLaMA', 'BERT', 'Fine-tuning', 'RAG Systems'] },
    { category: 'Deep Learning & Neural Networks', items: ['CNN', 'RNN', 'LSTM', 'Transformers', 'GAN', 'Attention Mechanisms'] },
    { category: 'Computer Vision & NLP', items: ['OpenCV', 'YOLO', 'OCR', 'NLTK', 'SpaCy', 'Hugging Face'] },
    { category: 'Data Science & Analytics', items: ['Pandas', 'NumPy', 'Matplotlib', 'Seaborn', 'Plotly', 'Statistical Analysis'] },
    { category: 'Multi-Cloud AI Platforms', items: ['AWS SageMaker', 'Azure OpenAI', 'Google Cloud AI', 'Bedrock', 'Vertex AI'] },
    { category: 'MLOps & Deployment', items: ['Docker', 'Kubernetes', 'MLflow', 'Airflow', 'CI/CD', 'Model Monitoring'] },
    { category: 'Databases & Big Data', items: ['PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch', 'Apache Spark', 'Vector DBs'] },
    { category: 'Advanced AI Capabilities', items: ['Logical Reasoning', 'Deep Thinking', 'Problem Solving', 'System Design', 'Research'] }
  ];

  const certifications = [
    {
      name: 'Python-AI',
      issuer: 'DUCAT',
      date: '2024',
      badge: 'ducat'
    },
    {
      name: 'C and C++',
      issuer: 'Haryana State Council ITEC',
      date: '2024',
      badge: 'hscitec'
    },
    {
      name: 'Crash Course on Python',
      issuer: 'Google',
      date: '2024',
      badge: 'google'
    },
    {
      name: 'Data Analysis with Python',
      issuer: 'IBM',
      date: '2024',
      badge: 'ibm'
    },
    {
      name: 'Harnessing the Power of Data with Power BI',
      issuer: 'IBM',
      date: '2024',
      badge: 'ibm'
    },
    {
      name: 'Data Analytics with Excel Pivot Tables',
      issuer: 'Microsoft',
      date: '2024',
      badge: 'microsoft'
    },
    {
      name: 'Extract, Transform, and Load Data in Power BI',
      issuer: 'Microsoft',
      date: '2024',
      badge: 'microsoft'
    },
    {
      name: 'Linux Fundamentals',
      issuer: 'LearnQuest',
      date: '2024',
      badge: 'learnquest'
    },
    {
      name: 'Data Mining Methods',
      issuer: 'University of Colorado Boulder',
      date: '2024',
      badge: 'colorado'
    },
    {
      name: 'Data Analytics Methods for Marketing',
      issuer: 'META',
      date: '2024',
      badge: 'meta'
    }
  ];

  const publications = [
    {
      title: 'Tuberculosis Prediction Using CNN and Transfer Learning',
      authors: 'Deepak Garg',
      publication: 'IC3ECSBHI – Int\'l Conf. on Cognitive Computing in Healthcare',
      year: '2025',
      type: 'Conference Paper',
      url: 'https://www.researchgate.net/publication/391934465_Hybrid_Approach_of_Deep_Convolution_Neural_Network_and_Transfer_Learning_for_Tuberculosis_Prediction',
      description: 'This paper presents a hybrid approach using deep convolutional neural networks and transfer learning techniques for accurate tuberculosis prediction from medical imaging data.'
    },
    {
      title: 'Evaluating the Severity of Parkinson\'s Disease Using Machine Learning Classifiers',
      authors: 'Deepak Garg',
      publication: 'IC3ECSBHI – Int\'l Conf. on Cognitive Computing in Healthcare',
      year: '2025',
      type: 'Conference Paper',
      url: 'https://www.researchgate.net/publication/391934828_Evaluating_the_Severity_of_Parkinson%27s_Disease_Using_Machine_Learning_Classifiers',
      description: 'This research evaluates various machine learning classifiers for assessing Parkinson\'s disease severity, providing insights into early detection and treatment planning.'
    },
    {
      title: 'Generative Artificial Intelligence in Healthcare',
      authors: 'Rinki Singh, Deepak Garg, Rajendra Kumar, Sudan Jha',
      publication: 'Taylor & Francis (ISBN: 9781032784847)',
      year: '2025',
      type: 'Book Chapter',
      url: 'https://www.taylorfrancis.com/chapters/edit/10.1201/*************-16/reassessment-generative-ai-healthcare-future-work-environments-rinki-singh-deepak-garg-rajendra-kumar-sudan-jha?context=ubx&refId=a37cbae0-b7a3-4057-8bd8-be0dbf4593a9',
      description: 'This chapter explores the transformative impact of generative AI technologies in healthcare environments, examining future work paradigms and their implications for healthcare professionals.'
    },
    {
      title: 'Leveraging Financial Data and Risk Management in the Banking Sector using Machine Learning',
      authors: 'Deepak Garg',
      publication: 'IEEE BOMBAY 2023 9th International Conference for Convergence in Technology (I2CT)',
      year: '2024',
      type: 'IEEE Conference',
      url: 'https://ieeexplore.ieee.org/document/********',
      description: 'This paper explores the application of machine learning techniques in financial data analysis and risk management within the banking sector, demonstrating improved decision-making processes.'
    },
    {
      title: 'Comparative Analysis of Machine Learning Algorithms in Parkinson Disease Diagnosis',
      authors: 'Deepak Garg',
      publication: 'IEEE 2023 2nd International Conference on Automation, Computing and Renewable Systems (ICACRS)',
      year: '2024',
      type: 'IEEE Conference',
      url: 'https://ieeexplore.ieee.org/document/********',
      description: 'This study provides a comprehensive comparison of various machine learning algorithms for Parkinson\'s disease diagnosis, analyzing their effectiveness and accuracy in clinical applications.'
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              My <span className="text-gradient">Resume</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              A comprehensive overview of my professional journey and technical expertise
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/resume/Deepak-Garg-Resume.pdf" 
                download="Deepak-Garg-Resume.pdf"
                className="btn-primary"
              >
                Download PDF
              </a>
              <button 
                onClick={() => {
                  // Open the PDF in a new window for better printing
                  const printWindow = window.open('/resume/Deepak-Garg-Resume.pdf', '_blank');
                  if (printWindow) {
                    printWindow.onload = () => {
                      printWindow.print();
                    };
                  } else {
                    // Fallback: just open the PDF
                    window.open('/resume/Deepak-Garg-Resume.pdf', '_blank');
                  }
                }}
                className="btn-outline"
              >
                Print Resume
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Work Experience
            </h2>
            
            <div className="space-y-8">
              {experiences.map((exp, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8 relative"
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                        <BuildingOfficeIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                            {exp.position}
                          </h3>
                          <p className="text-primary-600 dark:text-primary-400 font-semibold">
                            {exp.company}
                          </p>
                        </div>
                        {exp.current && (
                          <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                            Current
                          </span>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <CalendarDaysIcon className="h-4 w-4" />
                          {exp.duration}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPinIcon className="h-4 w-4" />
                          {exp.location}
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {exp.description.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-2 text-gray-600 dark:text-gray-400">
                            <CheckCircleIcon className="h-5 w-5 text-primary-600 dark:text-primary-400 flex-shrink-0 mt-0.5" />
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Education Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Education
            </h2>
            
            <div className="space-y-8">
              {education.map((edu, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8"
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center">
                        <AcademicCapIcon className="h-8 w-8 text-secondary-600 dark:text-secondary-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {edu.degree}
                        </h3>
                        <p className="text-secondary-600 dark:text-secondary-400 font-semibold">
                          {edu.institution}
                        </p>
                        <p className="text-gray-600 dark:text-gray-400">
                          {edu.field}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                        <CalendarDaysIcon className="h-4 w-4" />
                        {edu.duration}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400">
                        {edu.description}
                      </p>
                      
                      <ul className="space-y-1">
                        {edu.achievements.map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start gap-2 text-gray-600 dark:text-gray-400">
                            <CheckCircleIcon className="h-4 w-4 text-secondary-600 dark:text-secondary-400 flex-shrink-0 mt-0.5" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Publications Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Publications
            </h2>
            
            <div className="space-y-6">
              {publications.map((pub, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8 cursor-pointer hover:shadow-lg transition-all duration-300 group"
                  onClick={() => window.open(pub.url, '_blank')}
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <BookOpenIcon className="h-8 w-8 text-accent-600 dark:text-accent-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-300 flex items-center gap-2">
                            {pub.title}
                            <ArrowTopRightOnSquareIcon className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 font-medium">
                            {pub.authors}
                          </p>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <span className="bg-accent-100 dark:bg-accent-900 text-accent-800 dark:text-accent-200 px-3 py-1 rounded-full text-sm font-medium">
                            {pub.type}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {pub.year}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                        <BuildingOfficeIcon className="h-4 w-4" />
                        {pub.publication}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400">
                        {pub.description}
                      </p>
                      
                      <div className="flex items-center gap-2 text-accent-600 dark:text-accent-400 font-medium">
                        <span>Click to view publication</span>
                        <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Publications Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Publications
            </h2>
            
            <div className="space-y-6">
              {publications.map((pub, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8 cursor-pointer hover:shadow-lg transition-all duration-300 group"
                  onClick={() => window.open(pub.url, '_blank')}
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <BookOpenIcon className="h-8 w-8 text-accent-600 dark:text-accent-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-300 flex items-center gap-2">
                            {pub.title}
                            <ArrowTopRightOnSquareIcon className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 font-medium">
                            {pub.authors}
                          </p>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <span className="bg-accent-100 dark:bg-accent-900 text-accent-800 dark:text-accent-200 px-3 py-1 rounded-full text-sm font-medium">
                            {pub.type}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {pub.year}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                        <BuildingOfficeIcon className="h-4 w-4" />
                        {pub.publication}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400">
                        {pub.description}
                      </p>
                      
                      <div className="flex items-center gap-2 text-accent-600 dark:text-accent-400 font-medium">
                        <span>Click to view publication</span>
                        <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Technical Skills
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {skills.map((skillGroup, index) => (
                <motion.div
                  key={skillGroup.category}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-6"
                >
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                    {skillGroup.category}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {skillGroup.items.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Certifications Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Certifications
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-6 text-center"
                >
                  <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircleIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    {cert.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-1">
                    {cert.issuer}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {cert.date}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Resume;
