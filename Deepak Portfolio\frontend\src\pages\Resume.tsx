import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CalendarDaysIcon,
  MapPinIcon,
  BuildingOfficeIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  BookOpenIcon,
  ArrowTopRightOnSquareIcon,
  ChartBarIcon,
  CpuChipIcon,
  BeakerIcon,
  StarIcon,
  TrophyIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { useSpring, animated, config } from '@react-spring/web';
import AIChat from '../components/AIChat';
import AdvancedSkillsVisualization from '../components/AdvancedSkillsVisualization';
import Advanced3DCharts from '../components/Advanced3DCharts';
import RealisticNeuralNetwork from '../components/RealisticNeuralNetwork';

const Resume: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  };

  const experiences = [
    {
      company: 'Appsquadz Technologies',
      companyHighlight: 'Leading AI/ML Solutions Provider',
      position: 'Data Scientist & AI Engineer',
      location: 'Noida, Uttar Pradesh',
      duration: 'Jan 2025 - Present',
      current: true,
      logo: '/images/companies/appsquadz-logo.svg',
      website: 'https://appsquadz.com',
      companySize: '500+ employees',
      industry: 'Technology & AI Solutions',
      description: [
        'Developing machine learning models and data analytics solutions for business optimization',
        'Built recommendation systems, fraud detection models, and automated data pipelines',
        'Working on predictive analytics projects that improve business decision-making',
        'Implementing advanced AI/ML solutions using Python, TensorFlow, and cloud platforms'
      ],
      achievements: [
        'Improved model accuracy by 25% using advanced ensemble techniques',
        'Reduced data processing time by 60% through optimized pipelines',
        'Led a team of 3 junior data scientists on critical projects'
      ]
    },
    {
      company: 'Areness',
      companyHighlight: 'Data-Driven Marketing Intelligence',
      position: 'Data Engineer',
      location: 'Gurugram, Haryana',
      duration: 'Jun 2024 - Dec 2024',
      current: false,
      logo: '/images/companies/areness-logo.svg',
      website: 'https://www.arenesslaw.com/',
      companySize: '200+ employees',
      industry: 'Marketing Technology',
      description: [
        'Analyzed large datasets to extract business insights and built automated reporting systems',
        'Developed data visualization dashboards and implemented ETL processes',
        'Created predictive models for customer behavior analysis and business forecasting',
        'Collaborated with cross-functional teams to deliver data-driven solutions'
      ],
      achievements: [
        'Built real-time analytics dashboard serving 10K+ daily users',
        'Automated 80% of manual reporting processes',
        'Increased customer retention prediction accuracy to 92%'
      ]
    },
    {
      company: 'Freelance Data Science Projects',
      companyHighlight: 'Independent AI Consultant',
      position: 'Data Science Consultant',
      location: 'Remote',
      duration: 'Jul 2023 - May 2024',
      current: false,
      logo: '/images/companies/freelance-logo.svg',
      website: '',
      companySize: 'Independent',
      industry: 'Consulting',
      description: [
        'Worked on various data science projects including recommendation systems and sentiment analysis',
        'Helped small businesses implement data-driven solutions and automated workflows',
        'Built OCR systems, anomaly detection models, and speech analysis applications',
        'Delivered end-to-end ML solutions from data collection to model deployment'
      ],
      achievements: [
        'Completed 15+ successful AI/ML projects',
        'Generated $500K+ revenue for clients through optimization',
        'Maintained 100% client satisfaction rate'
      ]
    },
    {
      company: 'D.S. Projects Pvt. Ltd.',
      companyHighlight: 'Data Solutions Specialist',
      position: 'Data Analyst',
      location: 'On-site',
      duration: 'Jun 2022 - Jun 2023',
      current: false,
      logo: '/images/companies/ds-projects-logo.svg',
      website: '',
      companySize: '50+ employees',
      industry: 'Data Analytics',
      description: [
        'Started data science journey by working on data collection, cleaning, and basic analysis',
        'Learned machine learning fundamentals and built first predictive models',
        'Gained experience in data visualization and statistical analysis',
        'Developed foundational skills in Python, SQL, and data processing techniques'
      ],
      achievements: [
        'Processed and analyzed 1M+ data records',
        'Created first production ML model with 85% accuracy',
        'Reduced data processing errors by 90%'
      ]
    }
  ];

  const education = [
    {
      institution: 'Bennett University',
      degree: 'Master of Computer Application (MCA)',
      field: 'Data Science (9.1 CGPA)',
      duration: 'Aug 2023 – May 2025',
      description: 'Focused on advanced topics in data science, machine learning, and AI. Engaged in research and practical projects.',
      achievements: [
        'Consistent top performer (9.1 CGPA)',
        'Led data science research initiatives',
        'Active member of university AI club'
      ],
      location: 'Greater Noida'
    },
    {
      institution: 'J.C. Bose University of Science and Technology',
      degree: 'Bachelor of Computer Application (BCA)',
      field: 'Data Science (8.5 CGPA)',
      duration: 'Jul 2020 – Sep 2023',
      description: 'Studied core computer science and data science concepts. Participated in technical events and projects.',
      achievements: [
        'Graduated with 8.5 CGPA',
        'Data Science project finalist',
        'Organized coding workshops'
      ],
      location: 'Faridabad'
    }
  ];

  // Enhanced skills data with proficiency levels and analytics
  const skills = [
    {
      category: 'AI & Machine Learning',
      items: ['Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'XGBoost', 'LightGBM'],
      proficiency: 95,
      experience: '3+ years',
      projects: 25,
      color: '#3B82F6'
    },
    {
      category: 'Agentic AI & Automation',
      items: ['N8n', 'LangChain', 'AutoGen', 'CrewAI', 'Agentic Workflows', 'AI Orchestration'],
      proficiency: 90,
      experience: '2+ years',
      projects: 12,
      color: '#8B5CF6'
    },
    {
      category: 'Large Language Models',
      items: ['GPT-4', 'Claude', 'LLaMA', 'BERT', 'Fine-tuning', 'RAG Systems'],
      proficiency: 93,
      experience: '2+ years',
      projects: 18,
      color: '#10B981'
    },
    {
      category: 'Deep Learning & Neural Networks',
      items: ['CNN', 'RNN', 'LSTM', 'Transformers', 'GAN', 'Attention Mechanisms'],
      proficiency: 88,
      experience: '3+ years',
      projects: 20,
      color: '#F59E0B'
    },
    {
      category: 'Computer Vision & NLP',
      items: ['OpenCV', 'YOLO', 'OCR', 'NLTK', 'SpaCy', 'Hugging Face'],
      proficiency: 92,
      experience: '3+ years',
      projects: 22,
      color: '#EF4444'
    },
    {
      category: 'Data Science & Analytics',
      items: ['Pandas', 'NumPy', 'Matplotlib', 'Seaborn', 'Plotly', 'Statistical Analysis'],
      proficiency: 96,
      experience: '4+ years',
      projects: 30,
      color: '#06B6D4'
    },
    {
      category: 'Multi-Cloud AI Platforms',
      items: ['AWS SageMaker', 'Azure OpenAI', 'Google Cloud AI', 'Bedrock', 'Vertex AI'],
      proficiency: 92,
      experience: '2+ years',
      projects: 15,
      color: '#84CC16'
    },
    {
      category: 'MLOps & Deployment',
      items: ['Docker', 'Kubernetes', 'MLflow', 'Airflow', 'CI/CD', 'Model Monitoring'],
      proficiency: 87,
      experience: '2+ years',
      projects: 16,
      color: '#F97316'
    },
    {
      category: 'Databases & Big Data',
      items: ['PostgreSQL', 'MongoDB','Mysql', 'Redis', 'Elasticsearch', 'Apache Spark', 'Vector DBs'],
      proficiency: 92,
      experience: '3+ years',
      projects: 24,
      color: '#EC4899'
    },
    {
      category: 'Advanced AI Capabilities',
      items: ['Logical Reasoning', 'Deep Thinking', 'Problem Solving', 'System Design', 'Research'],
      proficiency: 91,
      experience: '3+ years',
      projects: 28,
      color: '#6366F1'
    }
  ];

  // Analytics data for visualizations
  const skillsAnalytics = skills.map(skill => ({
    name: skill.category.replace(' & ', '\n& '),
    proficiency: skill.proficiency,
    projects: skill.projects,
    color: skill.color
  }));

  const experienceTimeline = [
    { year: '2022', role: 'Data Analyst', company: 'D.S. Projects', level: 1 },
    { year: '2023', role: 'Freelance Consultant', company: 'Independent', level: 2 },
    { year: '2024', role: 'Data Engineer', company: 'Areness', level: 3 },
    { year: '2025', role: 'Data Scientist & AI Engineer', company: 'Appsquadz', level: 4 }
  ];

  const projectStats = [
    { name: 'AI/ML Projects', value: 25, color: '#3B82F6' },
    { name: 'Data Analytics', value: 30, color: '#10B981' },
    { name: 'Cloud Solutions', value: 15, color: '#F59E0B' },
    { name: 'Research Papers', value: 5, color: '#EF4444' }
  ];

  // Animation states
  const [activeChart, setActiveChart] = useState('skills');
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Check for dark mode
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    checkDarkMode();

    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

    return () => observer.disconnect();
  }, []);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <p className="text-gray-900 dark:text-white font-semibold">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.dataKey}: {entry.value}{entry.dataKey === 'proficiency' ? '%' : ''}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const certifications = [
    {
      name: 'C and C++',
      issuer: 'Haryana State Council ITEC',
      date: '2024',
      badge: 'hscitec',
      logo: '/images/certifications/hscitec-logo.svg',
      verificationUrl: '#',
      skills: ['C++', 'Programming', 'Data Structures'],
      level: 'Intermediate'
    },
    {
      name: 'Crash Course on Python',
      issuer: 'Google',
      date: '2024',
      badge: 'google',
      logo: '/images/certifications/google-logo.svg',
      verificationUrl: '#',
      skills: ['Python', 'Programming Fundamentals'],
      level: 'Beginner'
    },
    {
      name: 'Data Analysis with Python',
      issuer: 'IBM',
      date: '2024',
      badge: 'ibm',
      logo: '/images/certifications/ibm-logo.svg',
      verificationUrl: '#',
      skills: ['Python', 'Data Analysis', 'Pandas', 'NumPy'],
      level: 'Advanced'
    },
    {
      name: 'Harnessing the Power of Data with Power BI',
      issuer: 'IBM',
      date: '2024',
      badge: 'ibm',
      logo: '/images/certifications/ibm-logo.svg',
      verificationUrl: '#',
      skills: ['Power BI', 'Data Visualization', 'Business Intelligence'],
      level: 'Advanced'
    },
    {
      name: 'Data Analytics with Excel Pivot Tables',
      issuer: 'Microsoft',
      date: '2024',
      badge: 'microsoft',
      logo: '/images/certifications/microsoft-logo.svg',
      verificationUrl: '#',
      skills: ['Excel', 'Pivot Tables', 'Data Analysis'],
      level: 'Intermediate'
    },
    {
      name: 'Extract, Transform, and Load Data in Power BI',
      issuer: 'Microsoft',
      date: '2024',
      badge: 'microsoft',
      logo: '/images/certifications/microsoft-logo.svg',
      verificationUrl: '#',
      skills: ['Power BI', 'ETL', 'Data Engineering'],
      level: 'Advanced'
    },
    {
      name: 'Linux Fundamentals',
      issuer: 'LearnQuest',
      date: '2024',
      badge: 'learnquest',
      logo: '/images/certifications/learnquest-logo.svg',
      verificationUrl: '#',
      skills: ['Linux', 'Command Line', 'System Administration'],
      level: 'Intermediate'
    },
    {
      name: 'Data Mining Methods',
      issuer: 'University of Colorado Boulder',
      date: '2024',
      badge: 'colorado',
      logo: '/images/certifications/colorado-logo.svg',
      verificationUrl: '#',
      skills: ['Data Mining', 'Machine Learning', 'Statistics'],
      level: 'Advanced'
    },
    {
      name: 'Data Analytics Methods for Marketing',
      issuer: 'META',
      date: '2024',
      badge: 'meta',
      logo: '/images/certifications/meta-logo.svg',
      verificationUrl: '#',
      skills: ['Marketing Analytics', 'Data Science', 'Customer Insights'],
      level: 'Advanced'
    }
  ];

  const publications = [
    {
      title: 'Tuberculosis Prediction Using CNN and Transfer Learning',
      authors: 'Deepak Garg',
      publication: 'IC3ECSBHI – Int\'l Conf. on Cognitive Computing in Healthcare',
      year: '2025',
      type: 'Conference Paper',
      url: 'https://www.researchgate.net/publication/391934465_Hybrid_Approach_of_Deep_Convolution_Neural_Network_and_Transfer_Learning_for_Tuberculosis_Prediction',
      description: 'This paper presents a hybrid approach using deep convolutional neural networks and transfer learning techniques for accurate tuberculosis prediction from medical imaging data.'
    },
    {
      title: 'Evaluating the Severity of Parkinson\'s Disease Using Machine Learning Classifiers',
      authors: 'Deepak Garg',
      publication: 'IC3ECSBHI – Int\'l Conf. on Cognitive Computing in Healthcare',
      year: '2025',
      type: 'Conference Paper',
      url: 'https://www.researchgate.net/publication/391934828_Evaluating_the_Severity_of_Parkinson%27s_Disease_Using_Machine_Learning_Classifiers',
      description: 'This research evaluates various machine learning classifiers for assessing Parkinson\'s disease severity, providing insights into early detection and treatment planning.'
    },
    {
      title: 'Generative Artificial Intelligence in Healthcare',
      authors: 'Rinki Singh, Deepak Garg, Rajendra Kumar, Sudan Jha',
      publication: 'Taylor & Francis (ISBN: 9781032784847)',
      year: '2025',
      type: 'Book Chapter',
      url: 'https://www.taylorfrancis.com/chapters/edit/10.1201/*************-16/reassessment-generative-ai-healthcare-future-work-environments-rinki-singh-deepak-garg-rajendra-kumar-sudan-jha?context=ubx&refId=a37cbae0-b7a3-4057-8bd8-be0dbf4593a9',
      description: 'This chapter explores the transformative impact of generative AI technologies in healthcare environments, examining future work paradigms and their implications for healthcare professionals.'
    },
    {
      title: 'Leveraging Financial Data and Risk Management in the Banking Sector using Machine Learning',
      authors: 'Deepak Garg',
      publication: 'IEEE BOMBAY 2023 9th International Conference for Convergence in Technology (I2CT)',
      year: '2024',
      type: 'IEEE Conference',
      url: 'https://ieeexplore.ieee.org/document/********',
      description: 'This paper explores the application of machine learning techniques in financial data analysis and risk management within the banking sector, demonstrating improved decision-making processes.'
    },
    {
      title: 'Comparative Analysis of Machine Learning Algorithms in Parkinson Disease Diagnosis',
      authors: 'Deepak Garg',
      publication: 'IEEE 2023 2nd International Conference on Automation, Computing and Renewable Systems (ICACRS)',
      year: '2024',
      type: 'IEEE Conference',
      url: 'https://ieeexplore.ieee.org/document/********',
      description: 'This study provides a comprehensive comparison of various machine learning algorithms for Parkinson\'s disease diagnosis, analyzing their effectiveness and accuracy in clinical applications.'
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              My <span className="text-gradient">Resume</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              A comprehensive overview of my professional journey and technical expertise
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/resume/Deepak-Garg-Resume.pdf" 
                download="Deepak-Garg-Resume.pdf"
                className="btn-primary"
              >
                Download PDF
              </a>
              <button 
                onClick={() => {
                  // Open the PDF in a new window for better printing
                  const printWindow = window.open('/resume/Deepak-Garg-Resume.pdf', '_blank');
                  if (printWindow) {
                    printWindow.onload = () => {
                      printWindow.print();
                    };
                  } else {
                    // Fallback: just open the PDF
                    window.open('/resume/Deepak-Garg-Resume.pdf', '_blank');
                  }
                }}
                className="btn-outline"
              >
                Print Resume
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Professional Experience
            </h2>

            <div className="space-y-8">
              {experiences.map((exp, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8 relative hover:shadow-xl transition-all duration-300 group"
                >
                  <div className="flex flex-col lg:flex-row lg:items-start gap-6">
                    {/* Company Logo/Icon */}
                    <div className="flex-shrink-0">
                      <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900 dark:to-primary-800 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        {exp.logo ? (
                          <img
                            src={exp.logo}
                            alt={`${exp.company} logo`}
                            className="w-12 h-12 object-contain"
                            onError={(e) => {
                              // Fallback to icon if image fails to load
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <BuildingOfficeIcon className={`h-10 w-10 text-primary-600 dark:text-primary-400 ${exp.logo ? 'hidden' : ''}`} />
                      </div>
                    </div>

                    <div className="flex-1 space-y-6">
                      {/* Header */}
                      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                        <div className="space-y-2">
                          <h3 className="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                            {exp.position}
                          </h3>
                          <div className="space-y-1">
                            <p className="text-xl font-bold text-primary-600 dark:text-primary-400">
                              {exp.company}
                            </p>
                            <p className="text-sm font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full inline-block">
                              {exp.companyHighlight}
                            </p>
                          </div>
                        </div>

                        <div className="flex flex-col items-start lg:items-end gap-2">
                          {exp.current && (
                            <span className="bg-gradient-to-r from-green-400 to-green-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                              Current Position
                            </span>
                          )}
                          <div className="text-right space-y-1">
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                              {exp.companySize} • {exp.industry}
                            </p>
                            {exp.website && (
                              <a
                                href={exp.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1"
                              >
                                Visit Website
                                <ArrowTopRightOnSquareIcon className="h-3 w-3" />
                              </a>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Duration and Location */}
                      <div className="flex flex-wrap gap-6 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 px-3 py-2 rounded-lg">
                          <CalendarDaysIcon className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                          <span className="font-medium">{exp.duration}</span>
                        </div>
                        <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 px-3 py-2 rounded-lg">
                          <MapPinIcon className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                          <span className="font-medium">{exp.location}</span>
                        </div>
                      </div>

                      {/* Responsibilities */}
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Key Responsibilities</h4>
                        <ul className="space-y-3">
                          {exp.description.map((item, itemIndex) => (
                            <li key={itemIndex} className="flex items-start gap-3 text-gray-600 dark:text-gray-400">
                              <CheckCircleIcon className="h-5 w-5 text-primary-600 dark:text-primary-400 flex-shrink-0 mt-0.5" />
                              <span className="leading-relaxed">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Achievements */}
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <TrophyIcon className="h-5 w-5 text-yellow-500" />
                          Key Achievements
                        </h4>
                        <ul className="space-y-3">
                          {exp.achievements.map((achievement, achievementIndex) => (
                            <li key={achievementIndex} className="flex items-start gap-3 text-gray-600 dark:text-gray-400">
                              <StarIcon className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                              <span className="leading-relaxed font-medium">{achievement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Education Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Education
            </h2>
            
            <div className="space-y-8">
              {education.map((edu, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8"
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center">
                        <AcademicCapIcon className="h-8 w-8 text-secondary-600 dark:text-secondary-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {edu.degree}
                        </h3>
                        <p className="text-secondary-600 dark:text-secondary-400 font-semibold">
                          {edu.institution}
                        </p>
                        <p className="text-gray-600 dark:text-gray-400">
                          {edu.field}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                        <CalendarDaysIcon className="h-4 w-4" />
                        {edu.duration}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400">
                        {edu.description}
                      </p>
                      
                      <ul className="space-y-1">
                        {edu.achievements.map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start gap-2 text-gray-600 dark:text-gray-400">
                            <CheckCircleIcon className="h-4 w-4 text-secondary-600 dark:text-secondary-400 flex-shrink-0 mt-0.5" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Publications Section */}
      <section className="section-padding">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center">
              Publications
            </h2>
            
            <div className="space-y-6">
              {publications.map((pub, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-8 cursor-pointer hover:shadow-lg transition-all duration-300 group"
                  onClick={() => window.open(pub.url, '_blank')}
                >
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-accent-100 dark:bg-accent-900 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <BookOpenIcon className="h-8 w-8 text-accent-600 dark:text-accent-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 space-y-4">
                      <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-300 flex items-center gap-2">
                            {pub.title}
                            <ArrowTopRightOnSquareIcon className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 font-medium">
                            {pub.authors}
                          </p>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <span className="bg-accent-100 dark:bg-accent-900 text-accent-800 dark:text-accent-200 px-3 py-1 rounded-full text-sm font-medium">
                            {pub.type}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {pub.year}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                        <BuildingOfficeIcon className="h-4 w-4" />
                        {pub.publication}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400">
                        {pub.description}
                      </p>
                      
                      <div className="flex items-center gap-2 text-accent-600 dark:text-accent-400 font-medium">
                        <span>Click to view publication</span>
                        <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>



      {/* Advanced Skills Analytics Section */}
      <section className="section-padding bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-12"
          >
            <div className="text-center space-y-4">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white">
                Technical Expertise <span className="text-gradient">Analytics</span>
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Interactive visualization of my technical skills, proficiency levels, and project experience
              </p>
            </div>

            {/* Advanced 3D Visualizations */}
            <div className="space-y-12">
              {/* Neural Network Visualization */}
              <motion.div
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    🧠 AI Neural Network Architecture
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Real-time visualization of deep learning neural network with signal propagation
                  </p>
                </div>
                <RealisticNeuralNetwork isDarkMode={isDarkMode} />
              </motion.div>

              {/* Advanced Skills Galaxy */}
              <motion.div
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    🌌 Interactive Skills Universe
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    3D interactive visualization of technical expertise with proficiency levels
                  </p>
                </div>
                <AdvancedSkillsVisualization
                  skills={skills}
                  isDarkMode={isDarkMode}
                  activeChart={activeChart}
                  onChartChange={setActiveChart}
                />
              </motion.div>

              {/* 3D Project Distribution */}
              <motion.div
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    📊 3D Project Analytics
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Holographic visualization of project distribution and impact metrics
                  </p>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <Advanced3DCharts
                    data={projectStats}
                    isDarkMode={isDarkMode}
                    type="3d-pie"
                  />
                  <Advanced3DCharts
                    data={projectStats}
                    isDarkMode={isDarkMode}
                    type="3d-bar"
                  />
                  <Advanced3DCharts
                    data={projectStats}
                    isDarkMode={isDarkMode}
                    type="holographic"
                  />
                </div>
              </motion.div>
            </div>

            {/* Skills Grid with Enhanced Animations */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {skills.map((skillGroup, index) => (
                <motion.div
                  key={skillGroup.category}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="card p-6 hover:shadow-xl transition-all duration-300 group relative overflow-hidden"
                >
                  {/* Background gradient */}
                  <div
                    className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300"
                    style={{ backgroundColor: skillGroup.color }}
                  />

                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                        {skillGroup.category}
                      </h3>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: skillGroup.color }}
                        />
                        <span className="text-sm font-bold text-gray-600 dark:text-gray-400">
                          {skillGroup.proficiency}%
                        </span>
                      </div>
                    </div>

                    {/* Proficiency Bar */}
                    <div className="mb-4">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <motion.div
                          className="h-2 rounded-full"
                          style={{ backgroundColor: skillGroup.color }}
                          initial={{ width: 0 }}
                          whileInView={{ width: `${skillGroup.proficiency}%` }}
                          viewport={{ once: true }}
                          transition={{ duration: 1.5, delay: index * 0.1 }}
                        />
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
                      <span>{skillGroup.experience} experience</span>
                      <span>{skillGroup.projects} projects</span>
                    </div>

                    {/* Skills Tags */}
                    <div className="flex flex-wrap gap-2">
                      {skillGroup.items.map((skill, skillIndex) => (
                        <motion.span
                          key={skillIndex}
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.3, delay: (index * 0.1) + (skillIndex * 0.05) }}
                          className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 cursor-default"
                          style={{
                            borderLeft: `3px solid ${skillGroup.color}`
                          }}
                        >
                          {skill}
                        </motion.span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Certifications Section */}
      <section className="section-padding bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="space-y-12"
          >
            <div className="text-center space-y-4">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white">
                Professional <span className="text-gradient">Certifications</span>
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Industry-recognized certifications from leading technology companies and institutions
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 60 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="group relative"
                >
                  <div className="card p-8 h-full hover:shadow-2xl transition-all duration-500 group-hover:scale-105 relative overflow-hidden">
                    {/* Background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                    <div className="relative z-10 text-center space-y-6">
                      {/* Company Logo */}
                      <div className="flex justify-center">
                        <div className="w-20 h-20 bg-white dark:bg-gray-800 rounded-2xl shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          {cert.logo ? (
                            <img
                              src={cert.logo}
                              alt={`${cert.issuer} logo`}
                              className="w-12 h-12 object-contain"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          <CheckCircleIcon className={`h-12 w-12 text-primary-600 dark:text-primary-400 ${cert.logo ? 'hidden' : ''}`} />
                        </div>
                      </div>

                      {/* Certification Info */}
                      <div className="space-y-3">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                          {cert.name}
                        </h3>

                        <div className="space-y-2">
                          <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                            {cert.issuer}
                          </p>
                          <div className="flex items-center justify-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <span className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
                              {cert.date}
                            </span>
                            <span className={`px-3 py-1 rounded-full font-medium ${
                              cert.level === 'Advanced' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                              cert.level === 'Intermediate' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                              'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                            }`}>
                              {cert.level}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Skills Tags */}
                      <div className="flex flex-wrap gap-2 justify-center">
                        {cert.skills.map((skill, skillIndex) => (
                          <span
                            key={skillIndex}
                            className="bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-xs font-medium"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>

                      {/* Verification Link */}
                      <div className="pt-4">
                        <a
                          href={cert.verificationUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-200"
                        >
                          <span>Verify Certificate</span>
                          <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                        </a>
                      </div>
                    </div>

                    {/* Floating particles effect */}
                    <div className="absolute inset-0 pointer-events-none">
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 bg-primary-400 rounded-full opacity-0 group-hover:opacity-60"
                          style={{
                            left: `${20 + i * 15}%`,
                            top: `${30 + i * 10}%`,
                          }}
                          animate={{
                            y: [-10, -20, -10],
                            opacity: [0, 0.6, 0],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            delay: i * 0.2,
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Certification Stats */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg"
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div>
                  <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                    {certifications.length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Total Certifications
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {certifications.filter(c => c.level === 'Advanced').length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Advanced Level
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    {new Set(certifications.map(c => c.issuer)).size}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Unique Issuers
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    2024
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Latest Year
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* AI Chat Assistant */}
      <AIChat />
    </div>
  );
};

export default Resume;
