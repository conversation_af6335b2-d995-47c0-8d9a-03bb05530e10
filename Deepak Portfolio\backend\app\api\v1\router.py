from fastapi import APIRouter

from app.api.v1.endpoints import contact, projects, blog, skills, experience, education, services, testimonials
from app.api.v1 import admin
from app.api import chatbot, analytics

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(contact.router, prefix="/contact", tags=["contact"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(blog.router, prefix="/blog", tags=["blog"])
api_router.include_router(skills.router, prefix="/skills", tags=["skills"])
api_router.include_router(experience.router, prefix="/experience", tags=["experience"])
api_router.include_router(education.router, prefix="/education", tags=["education"])
api_router.include_router(services.router, prefix="/services", tags=["services"])
api_router.include_router(testimonials.router, prefix="/testimonials", tags=["testimonials"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(chatbot.router, prefix="/chatbot", tags=["chatbot"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])

@api_router.get("/")
async def api_root():
    return {"message": "Portfolio API v1", "version": "1.0.0"}
