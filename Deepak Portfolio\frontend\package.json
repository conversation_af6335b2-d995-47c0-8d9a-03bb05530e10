{"name": "deepak-portfolio-frontend", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@types/node": "^20.8.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "axios": "^1.6.0", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^3.5.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/jest": "^29.5.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}