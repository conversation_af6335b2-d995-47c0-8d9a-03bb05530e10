{"name": "deepak-portfolio-frontend", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@react-spring/three": "^10.0.1", "@react-spring/web": "^10.0.1", "@react-three/drei": "^9.88.13", "@react-three/fiber": "^9.2.0", "@react-three/postprocessing": "^3.0.4", "@types/node": "^20.8.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/three": "^0.178.0", "axios": "^1.6.0", "chart.js": "^4.5.0", "d3": "^7.9.0", "d3-ease": "^3.0.1", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "framer-motion": "^10.16.4", "framer-motion-3d": "^12.4.13", "gsap": "^3.13.0", "leva": "^0.10.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "react-spring": "^10.0.1", "recharts": "^3.0.2", "three": "^0.178.0", "typescript": "^4.9.5", "victory": "^37.3.6", "web-vitals": "^3.5.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/jest": "^29.5.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "overrides": {"@react-three/drei": {"react": "^18.2.0", "react-dom": "^18.2.0"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}