#!/usr/bin/env python3
"""
Service Health Check Script for Portfolio Project
Verifies that all services (MySQL, Backend, Frontend) are running correctly
"""

import requests
import pymysql
import time
import sys
from typing import Dict, bool

class ServiceHealthChecker:
    def __init__(self):
        self.services = {
            'mysql': {'host': 'localhost', 'port': 3306, 'status': False},
            'backend': {'url': 'http://localhost:8000', 'status': False},
            'frontend': {'url': 'http://localhost:3000', 'status': False}
        }
    
    def check_mysql(self) -> bool:
        """Check MySQL database connection"""
        try:
            connection = pymysql.connect(
                host='localhost',
                port=3306,
                user='root',
                password='root',
                database='portfolio_db',
                connect_timeout=5
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"✓ MySQL is running (version: {version[0]})")
                
            connection.close()
            return True
            
        except Exception as e:
            print(f"✗ MySQL connection failed: {e}")
            return False
    
    def check_backend(self) -> bool:
        """Check backend API service"""
        try:
            response = requests.get(
                f"{self.services['backend']['url']}/health",
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"✓ Backend API is running (status: {response.status_code})")
                return True
            else:
                print(f"✗ Backend API returned status: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("✗ Backend API is not responding (connection refused)")
            return False
        except requests.exceptions.Timeout:
            print("✗ Backend API timeout")
            return False
        except Exception as e:
            print(f"✗ Backend API check failed: {e}")
            return False
    
    def check_frontend(self) -> bool:
        """Check frontend React service"""
        try:
            response = requests.get(
                self.services['frontend']['url'],
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"✓ Frontend is running (status: {response.status_code})")
                return True
            else:
                print(f"✗ Frontend returned status: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("✗ Frontend is not responding (connection refused)")
            return False
        except requests.exceptions.Timeout:
            print("✗ Frontend timeout")
            return False
        except Exception as e:
            print(f"✗ Frontend check failed: {e}")
            return False
    
    def check_all_services(self) -> Dict[str, bool]:
        """Check all services"""
        results = {}
        
        print("=" * 60)
        print("PORTFOLIO PROJECT - SERVICE HEALTH CHECK")
        print("=" * 60)
        
        print("\n1. Checking MySQL Database...")
        results['mysql'] = self.check_mysql()
        
        print("\n2. Checking Backend API...")
        results['backend'] = self.check_backend()
        
        print("\n3. Checking Frontend...")
        results['frontend'] = self.check_frontend()
        
        print("\n" + "=" * 60)
        print("HEALTH CHECK SUMMARY")
        print("=" * 60)
        
        for service, status in results.items():
            status_text = "✓ HEALTHY" if status else "✗ UNHEALTHY"
            print(f"{service.capitalize()}: {status_text}")
        
        all_healthy = all(results.values())
        print(f"\nOverall Status: {'✓ ALL SERVICES HEALTHY' if all_healthy else '✗ SOME SERVICES UNHEALTHY'}")
        
        if not all_healthy:
            print("\nTroubleshooting tips:")
            if not results['mysql']:
                print("- Check if MySQL container is running: docker ps")
                print("- Check MySQL logs: docker logs portfolio_db")
            if not results['backend']:
                print("- Check if backend container is running: docker ps")
                print("- Check backend logs: docker logs portfolio_backend")
            if not results['frontend']:
                print("- Check if frontend container is running: docker ps")
                print("- Check frontend logs: docker logs portfolio_frontend")
        
        return results

def main():
    """Main health check function"""
    try:
        checker = ServiceHealthChecker()
        results = checker.check_all_services()
        
        # Exit with appropriate code
        sys.exit(0 if all(results.values()) else 1)
        
    except Exception as e:
        print(f"✗ Health check failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
