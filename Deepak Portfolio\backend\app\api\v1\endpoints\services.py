from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import Service
from app.schemas import ServiceResponse
from typing import List

router = APIRouter()

@router.get("/", response_model=List[ServiceResponse])
async def get_services(db: Session = Depends(get_db)):
    """Get all services"""
    services = db.query(Service).all()
    return services
