import React from 'react';
import { motion } from 'framer-motion';
import { 
  CpuChipIcon, 
  CloudIcon, 
  DocumentTextIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  AcademicCapIcon,
  BeakerIcon,
  CogIcon,
  PaintBrushIcon,
  CodeBracketIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

const Services: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const services = [
    {
      icon: <CpuChipIcon className="h-8 w-8" />,
      title: 'AI/ML Model Development',
      description: 'Custom machine learning solutions from concept to deployment. Specializing in LLM fine-tuning, computer vision, NLP, and predictive analytics.',
      features: ['LLM Fine-tuning (LLaMA, GPT)', 'Computer Vision & OCR', 'NLP & Sentiment Analysis', 'Predictive Analytics']
    },
    {
      icon: <CloudIcon className="h-8 w-8" />,
      title: 'Multi-Cloud ML Architecture',
      description: 'Vendor-agnostic ML systems across AWS, Azure, and Google Cloud with automated failover and cost optimization.',
      features: ['AWS SageMaker & Bedrock', 'Azure ML & Cognitive Services', 'Google Cloud AI Platform', 'Cross-cloud Deployment']
    },
    {
      icon: <BeakerIcon className="h-8 w-8" />,
      title: 'Intelligent Data Engineering',
      description: 'End-to-end data pipelines with automated validation, monitoring, and self-healing capabilities.',
      features: ['ETL Pipeline Automation', 'Data Quality Validation', 'Real-time Processing', 'Airflow & Orchestration']
    },
    {
      icon: <DocumentTextIcon className="h-8 w-8" />,
      title: 'Document Intelligence & OCR',
      description: 'Extract, classify, and analyze documents at scale using advanced OCR, NLP, and computer vision techniques.',
      features: ['Document Classification', 'Text Extraction & OCR', 'Entity Recognition (NER)', 'Compliance Automation']
    },
    {
      icon: <ChartBarIcon className="h-8 w-8" />,
      title: 'Predictive Analytics & Forecasting',
      description: 'Time-series forecasting, churn prediction, and demand planning using advanced statistical models and ML techniques.',
      features: ['Time-series Forecasting', 'Customer Churn Prediction', 'Inventory Optimization', 'Risk Assessment']
    },
    {
      icon: <AcademicCapIcon className="h-8 w-8" />,
      title: 'AI Strategy & Consulting',
      description: 'Strategic AI roadmap development, technology assessment, and implementation planning for enterprise-grade solutions.',
      features: ['AI Strategy Development', 'Technology Assessment', 'ROI Analysis', 'Implementation Planning']
    }
  ];

  const process = [
    {
      icon: <ChatBubbleLeftRightIcon className="h-6 w-6" />,
      title: 'Problem Analysis',
      description: 'Understanding your business challenges and identifying AI/ML opportunities with measurable ROI.'
    },
    {
      icon: <BeakerIcon className="h-6 w-6" />,
      title: 'Data Assessment & POC',
      description: 'Evaluating data quality, building proof-of-concept models, and validating technical feasibility.'
    },
    {
      icon: <CpuChipIcon className="h-6 w-6" />,
      title: 'Model Development',
      description: 'Building, training, and fine-tuning AI models using cutting-edge techniques and best practices.'
    },
    {
      icon: <CloudIcon className="h-6 w-6" />,
      title: 'Deployment & Scaling',
      description: 'Deploying models to production with monitoring, scaling, and automated retraining pipelines.'
    },
    {
      icon: <ChartBarIcon className="h-6 w-6" />,
      title: 'Monitoring & Optimization',
      description: 'Continuous monitoring, performance optimization, and iterative improvements for maximum impact.'
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              AI/ML <span className="text-gradient">Services</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Comprehensive AI and machine learning solutions to transform your business with intelligent automation and data-driven insights
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card p-8 text-center space-y-6 group"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full text-primary-600 dark:text-primary-400 group-hover:bg-primary-600 group-hover:text-white transition-all duration-300">
                  {service.icon}
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400">
                  {service.description}
                </p>
                
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="text-sm text-gray-500 dark:text-gray-400">
                      ✓ {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center space-y-6 mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              My AI/ML <span className="text-gradient">Process</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              A proven methodology to deliver AI solutions that create measurable business value
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8">
            {process.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center space-y-4"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-600 rounded-full text-white font-bold text-lg">
                  {index + 1}
                </div>
                
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white dark:bg-dark-700 rounded-full text-primary-600 dark:text-primary-400 shadow-lg">
                  {step.icon}
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {step.title}
                </h3>
                
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary-600 dark:bg-primary-700">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center space-y-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Ready to Transform Your Business with AI?
            </h2>
            <p className="text-xl text-primary-100 max-w-2xl mx-auto">
              Let's discuss how AI and machine learning can solve your biggest challenges and drive growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/contact" 
                className="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Discuss Your AI Project
              </a>
              <a 
                href="/portfolio" 
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-8 rounded-lg transition-all duration-300"
              >
                View AI/ML Projects
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Services;
