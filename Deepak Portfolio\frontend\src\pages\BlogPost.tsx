import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  CalendarDaysIcon, 
  ClockIcon, 
  UserIcon, 
  ArrowLeftIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

const BlogPost: React.FC = () => {
  // const { slug } = useParams(); // TODO: Use slug to fetch specific blog post from API
  
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  };

  // Sample blog post data
  const blogPost = {
    id: 1,
    title: 'Building Scalable Web Applications with React and Node.js',
    content: `
      <p>Building scalable web applications is a crucial skill for modern developers. In this comprehensive guide, we'll explore how to create robust, maintainable applications using React for the frontend and Node.js for the backend.</p>
      
      <h2>Why Choose React and Node.js?</h2>
      <p>React and Node.js form a powerful combination for full-stack development. React provides a component-based architecture that makes building complex user interfaces manageable, while Node.js offers excellent performance for server-side applications.</p>
      
      <h3>Key Benefits:</h3>
      <ul>
        <li>JavaScript everywhere - one language for frontend and backend</li>
        <li>Large ecosystem with extensive libraries and tools</li>
        <li>Strong community support and regular updates</li>
        <li>Excellent performance and scalability</li>
      </ul>
      
      <h2>Architecture Overview</h2>
      <p>A well-structured application architecture is essential for scalability. Here's the recommended approach:</p>
      
      <h3>Frontend (React)</h3>
      <ul>
        <li>Component-based structure</li>
        <li>State management with Redux or Context API</li>
        <li>Routing with React Router</li>
        <li>API integration with Axios or Fetch</li>
      </ul>
      
      <h3>Backend (Node.js)</h3>
      <ul>
        <li>Express.js for server setup</li>
        <li>RESTful API design</li>
        <li>Database integration (MongoDB, PostgreSQL)</li>
        <li>Authentication and authorization</li>
      </ul>
      
      <h2>Best Practices</h2>
      <p>Following best practices ensures your application remains maintainable and scalable:</p>
      
      <h3>Code Organization</h3>
      <ul>
        <li>Separate concerns with proper folder structure</li>
        <li>Use TypeScript for better type safety</li>
        <li>Implement proper error handling</li>
        <li>Write comprehensive tests</li>
      </ul>
      
      <h3>Performance Optimization</h3>
      <ul>
        <li>Implement code splitting and lazy loading</li>
        <li>Optimize bundle size with tree shaking</li>
        <li>Use caching strategies</li>
        <li>Implement proper database indexing</li>
      </ul>
      
      <h2>Deployment Considerations</h2>
      <p>Proper deployment is crucial for production applications:</p>
      
      <ul>
        <li>Use environment variables for configuration</li>
        <li>Implement CI/CD pipelines</li>
        <li>Monitor application performance</li>
        <li>Set up proper logging and error tracking</li>
      </ul>
      
      <h2>Conclusion</h2>
      <p>Building scalable web applications with React and Node.js requires careful planning and following best practices. By implementing proper architecture, optimization techniques, and deployment strategies, you can create applications that scale efficiently and provide excellent user experiences.</p>
    `,
    excerpt: 'Learn how to build modern, scalable web applications using React for the frontend and Node.js for the backend. This comprehensive guide covers best practices, architecture patterns, and deployment strategies.',
    slug: 'building-scalable-web-applications-react-nodejs',
    image_url: '/api/placeholder/1200/600',
    published: true,
    created_at: '2024-01-15T10:00:00Z',
    read_time: '8 min read',
    category: 'Web Development',
    author: 'Deepak Kumar',
    tags: ['React', 'Node.js', 'JavaScript', 'Full Stack', 'Web Development']
  };

  const relatedPosts = [
    {
      id: 2,
      title: 'The Future of Mobile Development: React Native vs Flutter',
      slug: 'react-native-vs-flutter-comparison',
      image_url: '/api/placeholder/400/300',
      category: 'Mobile Development',
      read_time: '12 min read'
    },
    {
      id: 3,
      title: 'Mastering API Design: RESTful vs GraphQL',
      slug: 'api-design-restful-vs-graphql',
      image_url: '/api/placeholder/400/300',
      category: 'Backend Development',
      read_time: '10 min read'
    },
    {
      id: 4,
      title: 'DevOps Best Practices for Modern Web Applications',
      slug: 'devops-best-practices-modern-web-apps',
      image_url: '/api/placeholder/400/300',
      category: 'DevOps',
      read_time: '15 min read'
    }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen pt-16">
      {/* Back Button */}
      <div className="container-custom py-8">
        <Link
          to="/blog"
          className="inline-flex items-center gap-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-300"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back to Blog
        </Link>
      </div>

      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="max-w-4xl mx-auto text-center space-y-6"
          >
            <div className="flex items-center justify-center gap-4 mb-6">
              <span className="bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 px-3 py-1 rounded-full text-sm font-medium">
                {blogPost.category}
              </span>
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <CalendarDaysIcon className="h-4 w-4" />
                  {formatDate(blogPost.created_at)}
                </div>
                <div className="flex items-center gap-1">
                  <ClockIcon className="h-4 w-4" />
                  {blogPost.read_time}
                </div>
              </div>
            </div>
            
            <h1 className="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
              {blogPost.title}
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              {blogPost.excerpt}
            </p>
            
            <div className="flex items-center justify-center gap-6 pt-4">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                  <UserIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {blogPost.author}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Full Stack Developer
                  </p>
                </div>
              </div>
              
              <button className="flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-300">
                <ShareIcon className="h-4 w-4" />
                Share
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="py-8">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="max-w-4xl mx-auto"
          >
            <img
              src={blogPost.image_url}
              alt={blogPost.title}
              className="w-full h-96 object-cover rounded-xl shadow-lg"
            />
          </motion.div>
        </div>
      </section>

      {/* Article Content */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Main Content */}
              <motion.article
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={fadeInUp}
                className="lg:w-2/3"
              >
                <div 
                  className="prose prose-lg dark:prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ __html: blogPost.content }}
                />
                
                {/* Tags */}
                <div className="mt-12 pt-8 border-t border-gray-200 dark:border-dark-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Tags
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {blogPost.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300 cursor-pointer"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.article>

              {/* Sidebar */}
              <motion.aside
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={fadeInUp}
                className="lg:w-1/3 space-y-8"
              >
                {/* Author Card */}
                <div className="card p-6 text-center">
                  <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <UserIcon className="h-10 w-10 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {blogPost.author}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    Full Stack Developer passionate about creating innovative solutions and sharing knowledge through code.
                  </p>
                  <Link
                    to="/contact"
                    className="btn-primary text-sm"
                  >
                    Get In Touch
                  </Link>
                </div>

                {/* Related Posts */}
                <div className="card p-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                    Related Posts
                  </h3>
                  <div className="space-y-4">
                    {relatedPosts.map((post) => (
                      <Link
                        key={post.id}
                        to={`/blog/${post.slug}`}
                        className="block group"
                      >
                        <div className="flex gap-4">
                          <img
                            src={post.image_url}
                            alt={post.title}
                            className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                          />
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 text-sm leading-tight">
                              {post.title}
                            </h4>
                            <div className="flex items-center gap-2 mt-2 text-xs text-gray-500 dark:text-gray-400">
                              <span>{post.category}</span>
                              <span>•</span>
                              <span>{post.read_time}</span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              </motion.aside>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPost;
