# PowerShell Development startup script for <PERSON>ak's Portfolio
# This script will start both frontend and backend in development mode

Write-Host "Starting Deepak's Portfolio Development Environment..." -ForegroundColor Green

# Check if Docker is installed
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Docker is required but not installed. Please install Docker first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is installed
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "Docker Compose is required but not installed. Please install Docker Compose first." -ForegroundColor Red
    exit 1
}

# Create .env file if it doesn't exist
if (-not (Test-Path .env)) {
    Write-Host "Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item .env.example .env
    Write-Host ".env file created. Please update it with your configuration." -ForegroundColor Green
}

# Start services with Docker Compose
Write-Host "Starting Docker containers..." -ForegroundColor Cyan
docker-compose up -d

# Wait for database to be ready
Write-Host "Waiting for MySQL database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Run database migrations and seed data
Write-Host "Setting up database..." -ForegroundColor Cyan
$migrateCommand = @"
from app.core.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)
print('Database tables created successfully!')
"@

docker-compose exec backend python -c $migrateCommand

# Seed database with sample data
Write-Host "Seeding database with sample data..." -ForegroundColor Cyan
docker-compose exec backend python seed_database.py

# Show running services
Write-Host "Development environment is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Running Services:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "   API Documentation: http://localhost:8000/docs" -ForegroundColor White
Write-Host "   Database: localhost:3307 (portfolio_db)" -ForegroundColor White
Write-Host ""
Write-Host "Useful Commands:" -ForegroundColor Cyan
Write-Host "   - Stop all services: docker-compose down" -ForegroundColor White
Write-Host "   - View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "   - Restart services: docker-compose restart" -ForegroundColor White
Write-Host "   - Build and restart: docker-compose up --build" -ForegroundColor White
Write-Host ""
Write-Host "Happy coding!" -ForegroundColor Green
