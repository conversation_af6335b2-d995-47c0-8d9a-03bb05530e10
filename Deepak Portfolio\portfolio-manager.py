#!/usr/bin/env python3
"""
Ultimate Portfolio Manager
Easy-to-use script for managing your AI/ML portfolio without Docker knowledge
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

class PortfolioManager:
    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.compose_file = self.project_dir / "docker-compose.yml"
        
    def print_banner(self):
        print("=" * 60)
        print("🚀 DEEPAK GARG - AI/ML PORTFOLIO MANAGER")
        print("=" * 60)
        print("Ultimate management interface for your professional portfolio")
        print("No Docker knowledge required!")
        print("=" * 60)
        
    def check_docker(self):
        """Check if Docker is installed and running"""
        try:
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker is not installed or not accessible")
                return False
            
            result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker is not running. Please start Docker Desktop")
                return False
                
            print("✅ Docker is ready")
            return True
        except FileNotFoundError:
            print("❌ Docker is not installed")
            return False
    
    def start_portfolio(self):
        """Start the portfolio application"""
        print("\n🚀 Starting your AI/ML Portfolio...")
        
        if not self.check_docker():
            return False
            
        try:
            # Start the services
            cmd = ['docker-compose', 'up', '-d', '--build']
            result = subprocess.run(cmd, cwd=self.project_dir, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ Failed to start portfolio: {result.stderr}")
                return False
            
            print("✅ Portfolio is starting up...")
            print("⏳ Waiting for services to be ready...")
            
            # Wait for services to be ready
            time.sleep(10)
            
            # Check if services are running
            result = subprocess.run(['docker-compose', 'ps'], cwd=self.project_dir, capture_output=True, text=True)
            print(result.stdout)
            
            print("\n🎉 Portfolio is now running!")
            print("📱 Frontend: http://localhost:3000")
            print("🔧 Backend API: http://localhost:8000")
            print("⚙️  Admin Panel: http://localhost:3000/admin")
            print("📚 API Docs: http://localhost:8000/docs")
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting portfolio: {e}")
            return False
    
    def stop_portfolio(self):
        """Stop the portfolio application"""
        print("\n🛑 Stopping your portfolio...")
        
        try:
            cmd = ['docker-compose', 'down']
            result = subprocess.run(cmd, cwd=self.project_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Portfolio stopped successfully")
            else:
                print(f"⚠️  Warning: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error stopping portfolio: {e}")
    
    def restart_portfolio(self):
        """Restart the portfolio application"""
        print("\n🔄 Restarting your portfolio...")
        self.stop_portfolio()
        time.sleep(3)
        self.start_portfolio()
    
    def view_logs(self):
        """View application logs"""
        print("\n📋 Viewing portfolio logs...")
        print("Press Ctrl+C to exit log view")
        
        try:
            cmd = ['docker-compose', 'logs', '-f']
            subprocess.run(cmd, cwd=self.project_dir)
        except KeyboardInterrupt:
            print("\n✅ Log viewing stopped")
    
    def seed_database(self):
        """Seed database with professional content"""
        print("\n🌱 Seeding database with professional AI/ML content...")
        
        try:
            # Run seed script in backend container
            cmd = ['docker-compose', 'exec', 'backend', 'python', 'seed_database.py']
            result = subprocess.run(cmd, cwd=self.project_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Database seeded successfully!")
                print("🎯 Your portfolio now has professional AI/ML content")
            else:
                print(f"❌ Failed to seed database: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error seeding database: {e}")
    
    def open_portfolio(self):
        """Open portfolio in browser"""
        print("\n🌐 Opening portfolio in browser...")
        webbrowser.open('http://localhost:3000')
    
    def open_admin(self):
        """Open admin panel in browser"""
        print("\n⚙️  Opening admin panel in browser...")
        webbrowser.open('http://localhost:3000/admin')
    
    def show_status(self):
        """Show current status"""
        print("\n📊 Portfolio Status:")
        
        try:
            result = subprocess.run(['docker-compose', 'ps'], cwd=self.project_dir, capture_output=True, text=True)
            print(result.stdout)
        except Exception as e:
            print(f"❌ Error getting status: {e}")
    
    def show_menu(self):
        """Show main menu"""
        print("\n" + "=" * 40)
        print("PORTFOLIO MANAGEMENT MENU")
        print("=" * 40)
        print("1. 🚀 Start Portfolio")
        print("2. 🛑 Stop Portfolio")
        print("3. 🔄 Restart Portfolio")
        print("4. 🌱 Seed Database (Load Professional Content)")
        print("5. 🌐 Open Portfolio in Browser")
        print("6. ⚙️  Open Admin Panel")
        print("7. 📋 View Logs")
        print("8. 📊 Show Status")
        print("9. ❌ Exit")
        print("=" * 40)
    
    def run(self):
        """Main application loop"""
        self.print_banner()
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\nEnter your choice (1-9): ").strip()
                
                if choice == '1':
                    self.start_portfolio()
                elif choice == '2':
                    self.stop_portfolio()
                elif choice == '3':
                    self.restart_portfolio()
                elif choice == '4':
                    self.seed_database()
                elif choice == '5':
                    self.open_portfolio()
                elif choice == '6':
                    self.open_admin()
                elif choice == '7':
                    self.view_logs()
                elif choice == '8':
                    self.show_status()
                elif choice == '9':
                    print("\n👋 Thanks for using Portfolio Manager!")
                    break
                else:
                    print("❌ Invalid choice. Please enter 1-9.")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Thanks for using Portfolio Manager!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

if __name__ == "__main__":
    manager = PortfolioManager()
    manager.run()
