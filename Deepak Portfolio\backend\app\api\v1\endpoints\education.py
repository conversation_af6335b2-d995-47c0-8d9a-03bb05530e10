from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import Education
from app.schemas import EducationResponse
from typing import List

router = APIRouter()

@router.get("/", response_model=List[EducationResponse])
async def get_education(db: Session = Depends(get_db)):
    """Get all education records"""
    education = db.query(Education).order_by(Education.start_date.desc()).all()
    return education
