<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chart" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bg)"/>
  
  <!-- Dashboard -->
  <rect x="50" y="80" width="700" height="450" rx="20" fill="white" opacity="0.95"/>
  
  <!-- Header -->
  <rect x="50" y="80" width="700" height="60" rx="20" fill="#667eea" opacity="0.8"/>
  <text x="400" y="120" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Multi-Cloud Churn Prediction</text>
  
  <!-- Cloud Providers -->
  <rect x="80" y="160" width="180" height="100" rx="10" fill="#FF9900" opacity="0.8"/>
  <text x="170" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">AWS</text>
  <text x="170" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">SageMaker</text>
  <text x="170" y="225" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">XGBoost</text>
  <text x="170" y="245" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">89% Accuracy</text>
  
  <rect x="310" y="160" width="180" height="100" rx="10" fill="#00BCF2" opacity="0.8"/>
  <text x="400" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Azure</text>
  <text x="400" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">ML Studio</text>
  <text x="400" y="225" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">AutoML</text>
  <text x="400" y="245" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">91% Accuracy</text>
  
  <rect x="540" y="160" width="180" height="100" rx="10" fill="#4285F4" opacity="0.8"/>
  <text x="630" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Google Cloud</text>
  <text x="630" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Vertex AI</text>
  <text x="630" y="225" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">TensorFlow</text>
  <text x="630" y="245" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">93% Accuracy</text>
  
  <!-- Ensemble Model -->
  <rect x="250" y="290" width="300" height="80" rx="15" fill="url(#chart)" opacity="0.9"/>
  <text x="400" y="320" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Ensemble Model</text>
  <text x="400" y="345" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">95% Final Accuracy</text>
  <text x="400" y="360" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Weighted Voting</text>
  
  <!-- Arrows -->
  <path d="M 170 270 L 350 285" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M 400 270 L 400 285" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M 630 270 L 450 285" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>
  
  <!-- Results -->
  <rect x="150" y="400" width="200" height="80" rx="10" fill="#4CAF50" opacity="0.8"/>
  <text x="250" y="425" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Cost Savings</text>
  <text x="250" y="450" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">$2.1M+</text>
  <text x="250" y="470" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Annual</text>
  
  <rect x="450" y="400" width="200" height="80" rx="10" fill="#2196F3" opacity="0.8"/>
  <text x="550" y="425" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Retention Rate</text>
  <text x="550" y="450" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">87%</text>
  <text x="550" y="470" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">Improvement</text>
  
  <!-- Tech Stack -->
  <text x="400" y="560" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Multi-Cloud ML • Ensemble Methods • Feature Engineering • Real-time Scoring</text>
</svg>
