/*! For license information please see main.5ad70058.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,i={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)o.call(t,r)&&!l.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:i,_owner:s.current}}t.Fragment=i,t.jsx=c,t.jsxs=c},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var b=x.prototype=new v;b.constructor=x,m(b,y.prototype),b.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},j={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var a,i={},o=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,a)&&!j.hasOwnProperty(a)&&(i[a]=t[a]);var l=arguments.length-2;if(1===l)i.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(a in l=e.defaultProps)void 0===i[a]&&(i[a]=l[a]);return{$$typeof:n,type:e,key:o,ref:s,props:i,_owner:S.current}}function N(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function A(e,t,a,i,o){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return o=o(l=e),e=""===i?"."+P(l,0):i,w(o)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),A(o,t,a,"",function(e){return e})):null!=o&&(N(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(C,"$&/")+"/")+e)),t.push(o)),1;if(l=0,i=""===i?".":i+":",w(e))for(var c=0;c<e.length;c++){var u=i+P(s=e[c],c);l+=A(s,t,a,u,o)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(s=e.next()).done;)l+=A(s=s.value,t,a,u=i+P(s,c++),o);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function T(e,t,n){if(null==e)return e;var r=[],a=0;return A(e,r,"","",function(e){return t.call(n,e,a++)}),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},M={transition:null},_={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:M,ReactCurrentOwner:S};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:T,forEach:function(e,t,n){T(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return T(e,function(){t++}),t},toArray:function(e){return T(e,function(e){return e})||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=o,t.PureComponent=x,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_,t.act=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),i=e.key,o=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,s=S.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)k.call(t,c)&&!j.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];a.children=l}return{$$typeof:n,type:e.type,key:i,ref:o,props:a,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var s=2*(r+1)-1,l=e[s],c=s+1,u=e[c];if(0>i(l,n))c<a&&0>i(u,l)?(e[r]=u,e[c]=n,r=c):(e[r]=l,e[s]=n,r=s);else{if(!(c<a&&0>i(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var c=[],u=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(g=!1,b(e),!m)if(null!==r(c))m=!0,M(k);else{var t=r(u);null!==t&&_(w,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,v(N),N=-1),p=!0;var i=h;try{for(b(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!A());){var o=f.callback;if("function"===typeof o){f.callback=null,h=f.priorityLevel;var s=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(c)&&a(c),b(n)}else a(c);f=r(c)}if(null!==f)var l=!0;else{var d=r(u);null!==d&&_(w,d.startTime-n),l=!1}return l}finally{f=null,h=i,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,j=!1,E=null,N=-1,C=5,P=-1;function A(){return!(t.unstable_now()-P<C)}function T(){if(null!==E){var e=t.unstable_now();P=e;var n=!0;try{n=E(!0,e)}finally{n?S():(j=!1,E=null)}}else j=!1}if("function"===typeof x)S=function(){x(T)};else if("undefined"!==typeof MessageChannel){var L=new MessageChannel,R=L.port2;L.port1.onmessage=T,S=function(){R.postMessage(null)}}else S=function(){y(T,0)};function M(e){E=e,j||(j=!0,S())}function _(e,n){N=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,M(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,i){var o=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?o+i:o:i=o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:s=i+s,sortIndex:-1},i>o?(e.sortIndex=i,n(u,e),null===r(c)&&e===r(u)&&(g?(v(N),N=-1):g=!0,_(w,i-o))):(e.sortIndex=s,n(c,e),m||p||(m=!0,M(k))),e},t.unstable_shouldYield=A,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,s={};function l(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(s[e]=t,e=0;e<t.length;e++)o.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,a,i,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function x(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),C=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),L=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var M=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var _=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=_&&e[_]||e["@@iterator"])?e:null}var O,I=Object.assign;function V(e){if(void 0===O)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);O=t&&t[1]||""}return"\n"+O+e}var F=!1;function B(e,t){if(!e||F)return"";F=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),i=r.stack.split("\n"),o=a.length-1,s=i.length-1;1<=o&&0<=s&&a[o]!==i[s];)s--;for(;1<=o&&0<=s;o--,s--)if(a[o]!==i[s]){if(1!==o||1!==s)do{if(o--,0>--s||a[o]!==i[s]){var l="\n"+a[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=o&&0<=s);break}}}finally{F=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?V(e):""}function z(e){switch(e.tag){case 5:return V(e.type);case 16:return V("Lazy");case 13:return V("Suspense");case 19:return V("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case E:return"Profiler";case j:return"StrictMode";case A:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case N:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case L:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return U(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===j?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function $(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function G(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Z(e,t){var n=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function K(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&x(e,"checked",t,!1)}function X(e,t){Y(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&G(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function ie(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ue(e,t)})}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var ye=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function xe(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var be=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,je=null;function Ee(e){if(e=xa(e)){if("function"!==typeof ke)throw Error(i(280));var t=e.stateNode;t&&(t=wa(t),ke(e.stateNode,e.type,t))}}function Ne(e){Se?je?je.push(e):je=[e]:Se=e}function Ce(){if(Se){var e=Se,t=je;if(je=Se=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Pe(e,t){return e(t)}function Ae(){}var Te=!1;function Le(e,t,n){if(Te)return e(t,n);Te=!0;try{return Pe(e,t,n)}finally{Te=!1,(null!==Se||null!==je)&&(Ae(),Ce())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Me=!1;if(u)try{var _e={};Object.defineProperty(_e,"passive",{get:function(){Me=!0}}),window.addEventListener("test",_e,_e),window.removeEventListener("test",_e,_e)}catch(ue){Me=!1}function De(e,t,n,r,a,i,o,s,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Oe=!1,Ie=null,Ve=!1,Fe=null,Be={onError:function(e){Oe=!0,Ie=e}};function ze(e,t,n,r,a,i,o,s,l){Oe=!1,Ie=null,De.apply(Be,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(Ue(e)!==e)throw Error(i(188))}function qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return He(a),e;if(o===r)return He(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,l=a.child;l;){if(l===n){s=!0,n=a,r=o;break}if(l===r){s=!0,r=a,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=a;break}if(l===r){s=!0,r=o,n=a;break}l=l.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?$e(e):null}function $e(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=$e(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ge=a.unstable_cancelCallback,Ze=a.unstable_shouldYield,Ke=a.unstable_requestPaint,Ye=a.unstable_now,Xe=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,it=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,o=268435455&n;if(0!==o){var s=o&~a;0!==s?r=dt(s):0!==(i&=o)&&(r=dt(i))}else 0!==(o=n&~a)?r=dt(o):0!==i&&(r=dt(i));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(i=t&-t)||16===a&&0!==(4194240&i)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var xt=0;function bt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,kt,St,jt,Et,Nt=!1,Ct=[],Pt=null,At=null,Tt=null,Lt=new Map,Rt=new Map,Mt=[],_t="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":At=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Lt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Ot(e,t,n,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==t&&(null!==(t=xa(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function It(e){var t=va(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Et(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Vt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Zt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=xa(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);be=r,n.target.dispatchEvent(r),be=null,t.shift()}return!0}function Ft(e,t,n){Vt(e)&&n.delete(t)}function Bt(){Nt=!1,null!==Pt&&Vt(Pt)&&(Pt=null),null!==At&&Vt(At)&&(At=null),null!==Tt&&Vt(Tt)&&(Tt=null),Lt.forEach(Ft),Rt.forEach(Ft)}function zt(e,t){e.blockedOn===t&&(e.blockedOn=null,Nt||(Nt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Bt)))}function Ut(e){function t(t){return zt(t,e)}if(0<Ct.length){zt(Ct[0],e);for(var n=1;n<Ct.length;n++){var r=Ct[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&zt(Pt,e),null!==At&&zt(At,e),null!==Tt&&zt(Tt,e),Lt.forEach(t),Rt.forEach(t),n=0;n<Mt.length;n++)(r=Mt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Mt.length&&null===(n=Mt[0]).blockedOn;)It(n),null===n.blockedOn&&Mt.shift()}var Wt=b.ReactCurrentBatchConfig,Ht=!0;function qt(e,t,n,r){var a=xt,i=Wt.transition;Wt.transition=null;try{xt=1,Qt(e,t,n,r)}finally{xt=a,Wt.transition=i}}function $t(e,t,n,r){var a=xt,i=Wt.transition;Wt.transition=null;try{xt=4,Qt(e,t,n,r)}finally{xt=a,Wt.transition=i}}function Qt(e,t,n,r){if(Ht){var a=Zt(e,t,n,r);if(null===a)Hr(e,t,r,Gt,n),Dt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Pt=Ot(Pt,e,t,n,r,a),!0;case"dragenter":return At=Ot(At,e,t,n,r,a),!0;case"mouseover":return Tt=Ot(Tt,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return Lt.set(i,Ot(Lt.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,Rt.set(i,Ot(Rt.get(i)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<_t.indexOf(e)){for(;null!==a;){var i=xa(a);if(null!==i&&wt(i),null===(i=Zt(e,t,n,r))&&Hr(e,t,r,Gt,n),i===a)break;a=i}null!==a&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Gt=null;function Zt(e,t,n,r){if(Gt=null,null!==(e=va(e=we(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gt=e,null}function Kt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xe()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Xt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Xt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,i=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[i-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,i){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,sn,ln,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=I({},cn,{view:0,detail:0}),fn=an(dn),hn=I({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=an(hn),mn=an(I({},hn,{dataTransfer:0})),gn=an(I({},dn,{relatedTarget:0})),yn=an(I({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=I({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xn=an(vn),bn=an(I({},cn,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function En(){return jn}var Nn=I({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Cn=an(Nn),Pn=an(I({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),An=an(I({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Tn=an(I({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Ln=I({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=an(Ln),Mn=[9,13,27,32],_n=u&&"CompositionEvent"in window,Dn=null;u&&"documentMode"in document&&(Dn=document.documentMode);var On=u&&"TextEvent"in window&&!Dn,In=u&&(!_n||Dn&&8<Dn&&11>=Dn),Vn=String.fromCharCode(32),Fn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Mn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function qn(e,t,n,r){Ne(r),0<(t=$r(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $n=null,Qn=null;function Gn(e){Vr(e,0)}function Zn(e){if(Q(ba(e)))return e}function Kn(e,t){if("change"===e)return t}var Yn=!1;if(u){var Xn;if(u){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Xn=Jn}else Xn=!1;Yn=Xn&&(!document.documentMode||9<document.documentMode)}function tr(){$n&&($n.detachEvent("onpropertychange",nr),Qn=$n=null)}function nr(e){if("value"===e.propertyName&&Zn(Qn)){var t=[];qn(t,Qn,e,we(e)),Le(Gn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,($n=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Zn(Qn)}function ir(e,t){if("click"===e)return Zn(t)}function or(e,t){if("input"===e||"change"===e)return Zn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!sr(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=G();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=G((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,i=Math.min(r.start,a);r=void 0===r.end?i:Math.min(r.end,a),!e.extend&&i>r&&(a=r,r=i,i=a),a=ur(n,i);var o=ur(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,xr=!1;function br(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==gr||gr!==G(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&lr(vr,r)||(vr=r,0<(r=$r(yr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},jr={};function Er(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in jr)return Sr[e]=n[t];return e}u&&(jr=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Nr=Er("animationend"),Cr=Er("animationiteration"),Pr=Er("animationstart"),Ar=Er("transitionend"),Tr=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Tr.set(e,t),l(t,[e])}for(var Mr=0;Mr<Lr.length;Mr++){var _r=Lr[Mr];Rr(_r.toLowerCase(),"on"+(_r[0].toUpperCase()+_r.slice(1)))}Rr(Nr,"onAnimationEnd"),Rr(Cr,"onAnimationIteration"),Rr(Pr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Ar,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Or=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Ir(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,s,l,c){if(ze.apply(this,arguments),Oe){if(!Oe)throw Error(i(198));var u=Ie;Oe=!1,Ie=null,Ve||(Ve=!0,Fe=u)}}(r,t,void 0,e),e.currentTarget=null}function Vr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,c=s.currentTarget;if(s=s.listener,l!==i&&a.isPropagationStopped())break e;Ir(a,s,c),i=l}else for(o=0;o<r.length;o++){if(l=(s=r[o]).instance,c=s.currentTarget,s=s.listener,l!==i&&a.isPropagationStopped())break e;Ir(a,s,c),i=l}}}if(Ve)throw e=Fe,Ve=!1,Fe=null,e}function Fr(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var zr="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[zr]){e[zr]=!0,o.forEach(function(t){"selectionchange"!==t&&(Or.has(t)||Br(t,!1,e),Br(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[zr]||(t[zr]=!0,Br("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Kt(t)){case 1:var a=qt;break;case 4:a=$t;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Me||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,a){var i=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===a||8===s.nodeType&&s.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var l=o.tag;if((3===l||4===l)&&((l=o.stateNode.containerInfo)===a||8===l.nodeType&&l.parentNode===a))return;o=o.return}for(;null!==s;){if(null===(o=va(s)))return;if(5===(l=o.tag)||6===l){r=i=o;continue e}s=s.parentNode}}r=r.return}Le(function(){var r=i,a=we(n),o=[];e:{var s=Tr.get(e);if(void 0!==s){var l=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=Cn;break;case"focusin":c="focus",l=gn;break;case"focusout":c="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=An;break;case Nr:case Cr:case Pr:l=yn;break;case Ar:l=Tn;break;case"scroll":l=fn;break;case"wheel":l=Rn;break;case"copy":case"cut":case"paste":l=xn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Pn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==s?s+"Capture":null:s;u=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Re(p,f))&&u.push(qr(p,m,h)))),d)break;p=p.return}0<u.length&&(s=new l(s,c,null,n,a),o.push({event:s,listeners:u}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===be||!(c=n.relatedTarget||n.fromElement)||!va(c)&&!c[pa])&&(l||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(c=(c=n.relatedTarget||n.toElement)?va(c):null)&&(c!==(d=Ue(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(l=null,c=r),l!==c)){if(u=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(u=Pn,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:ba(l),h=null==c?s:ba(c),(s=new u(m,p+"leave",l,n,a)).target=d,s.relatedTarget=h,m=null,va(a)===r&&((u=new u(f,p+"enter",c,n,a)).target=h,u.relatedTarget=d,m=u),d=m,l&&c)e:{for(f=c,p=0,h=u=l;h;h=Qr(h))p++;for(h=0,m=f;m;m=Qr(m))h++;for(;0<p-h;)u=Qr(u),p--;for(;0<h-p;)f=Qr(f),h--;for(;p--;){if(u===f||null!==f&&u===f.alternate)break e;u=Qr(u),f=Qr(f)}u=null}else u=null;null!==l&&Gr(o,s,l,u,!1),null!==c&&null!==d&&Gr(o,d,c,u,!0)}if("select"===(l=(s=r?ba(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Kn;else if(Hn(s))if(Yn)g=or;else{g=ar;var y=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=ir);switch(g&&(g=g(e,r))?qn(o,g,n,a):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&ee(s,"number",s.value)),y=r?ba(r):window,e){case"focusin":(Hn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,br(o,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":br(o,n,a)}var v;if(_n)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Un?Bn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(In&&"ko"!==n.locale&&(Un||"onCompositionStart"!==x?"onCompositionEnd"===x&&Un&&(v=en()):(Xt="value"in(Yt=a)?Yt.value:Yt.textContent,Un=!0)),0<(y=$r(r,x)).length&&(x=new bn(x,e,null,n,a),o.push({event:x,listeners:y}),v?x.data=v:null!==(v=zn(n))&&(x.data=v))),(v=On?function(e,t){switch(e){case"compositionend":return zn(t);case"keypress":return 32!==t.which?null:(Fn=!0,Vn);case"textInput":return(e=t.data)===Vn&&Fn?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!_n&&Bn(e,t)?(e=en(),Jt=Xt=Yt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return In&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=$r(r,"onBeforeInput")).length&&(a=new bn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=v))}Vr(o,t)})}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $r(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;5===a.tag&&null!==i&&(a=i,null!=(i=Re(e,n))&&r.unshift(qr(e,i,a)),null!=(i=Re(e,t))&&r.push(qr(e,i,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Gr(e,t,n,r,a){for(var i=t._reactName,o=[];null!==n&&n!==r;){var s=n,l=s.alternate,c=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==c&&(s=c,a?null!=(l=Re(n,i))&&o.unshift(qr(n,l,s)):a||null!=(l=Re(n,i))&&o.push(qr(n,l,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Zr=/\r\n?/g,Kr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Zr,"\n").replace(Kr,"")}function Xr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(i(425))}function Jr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,ia="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ia?function(e){return ia.resolve(null).then(e).catch(sa)}:ra;function sa(e){setTimeout(function(){throw e})}function la(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Ut(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,ha="__reactProps$"+da,pa="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,ya="__reactHandles$"+da;function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pa]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ua(e);null!==e;){if(n=e[fa])return n;e=ua(e)}return t}n=(e=n).parentNode}return null}function xa(e){return!(e=e[fa]||e[pa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function ba(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function wa(e){return e[ha]||null}var ka=[],Sa=-1;function ja(e){return{current:e}}function Ea(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Na(e,t){Sa++,ka[Sa]=e.current,e.current=t}var Ca={},Pa=ja(Ca),Aa=ja(!1),Ta=Ca;function La(e,t){var n=e.type.contextTypes;if(!n)return Ca;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in n)i[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ra(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ma(){Ea(Aa),Ea(Pa)}function _a(e,t,n){if(Pa.current!==Ca)throw Error(i(168));Na(Pa,t),Na(Aa,n)}function Da(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(i(108,W(e)||"Unknown",a));return I({},n,r)}function Oa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ca,Ta=Pa.current,Na(Pa,e),Na(Aa,Aa.current),!0}function Ia(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=Da(e,t,Ta),r.__reactInternalMemoizedMergedChildContext=e,Ea(Aa),Ea(Pa),Na(Pa,e)):Ea(Aa),Na(Aa,n)}var Va=null,Fa=!1,Ba=!1;function za(e){null===Va?Va=[e]:Va.push(e)}function Ua(){if(!Ba&&null!==Va){Ba=!0;var e=0,t=xt;try{var n=Va;for(xt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Va=null,Fa=!1}catch(a){throw null!==Va&&(Va=Va.slice(e+1)),Qe(Je,Ua),a}finally{xt=t,Ba=!1}}return null}var Wa=[],Ha=0,qa=null,$a=0,Qa=[],Ga=0,Za=null,Ka=1,Ya="";function Xa(e,t){Wa[Ha++]=$a,Wa[Ha++]=qa,qa=e,$a=t}function Ja(e,t,n){Qa[Ga++]=Ka,Qa[Ga++]=Ya,Qa[Ga++]=Za,Za=e;var r=Ka;e=Ya;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var i=32-ot(t)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Ka=1<<32-ot(t)+a|n<<a|r,Ya=i+e}else Ka=1<<i|n<<a|r,Ya=e}function ei(e){null!==e.return&&(Xa(e,1),Ja(e,1,0))}function ti(e){for(;e===qa;)qa=Wa[--Ha],Wa[Ha]=null,$a=Wa[--Ha],Wa[Ha]=null;for(;e===Za;)Za=Qa[--Ga],Qa[Ga]=null,Ya=Qa[--Ga],Qa[Ga]=null,Ka=Qa[--Ga],Qa[Ga]=null}var ni=null,ri=null,ai=!1,ii=null;function oi(e,t){var n=Lc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function si(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,ri=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,ri=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Za?{id:Ka,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ni=e,ri=null,!0);default:return!1}}function li(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ci(e){if(ai){var t=ri;if(t){var n=t;if(!si(e,t)){if(li(e))throw Error(i(418));t=ca(n.nextSibling);var r=ni;t&&si(e,t)?oi(r,n):(e.flags=-4097&e.flags|2,ai=!1,ni=e)}}else{if(li(e))throw Error(i(418));e.flags=-4097&e.flags|2,ai=!1,ni=e}}}function ui(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function di(e){if(e!==ni)return!1;if(!ai)return ui(e),ai=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ri)){if(li(e))throw fi(),Error(i(418));for(;t;)oi(e,t),t=ca(t.nextSibling)}if(ui(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ri=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ri=null}}else ri=ni?ca(e.stateNode.nextSibling):null;return!0}function fi(){for(var e=ri;e;)e=ca(e.nextSibling)}function hi(){ri=ni=null,ai=!1}function pi(e){null===ii?ii=[e]:ii.push(e)}var mi=b.ReactCurrentBatchConfig;function gi(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function yi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function vi(e){return(0,e._init)(e._payload)}function xi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Mc(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Ic(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var i=n.type;return i===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===R&&vi(i)===t.type)?((r=a(t,n.props)).ref=gi(e,t,n),r.return=e,r):((r=_c(n.type,n.key,n.props,null,e.mode,r)).ref=gi(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Dc(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Ic(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=_c(t.type,t.key,t.props,null,e.mode,n)).ref=gi(e,null,t),n.return=e,n;case k:return(t=Vc(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Dc(t,e.mode,n,null)).return=e,t;yi(e,t)}return null}function h(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?c(e,t,n,r):null;case k:return n.key===a?u(e,t,n,r):null;case R:return h(e,t,(a=n._init)(n._payload),r)}if(te(n)||D(n))return null!==a?null:d(e,t,n,r,null);yi(e,n)}return null}function p(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case R:return p(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,a,null);yi(t,r)}return null}function m(a,i,s,l){for(var c=null,u=null,d=i,m=i=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=h(a,d,s[m],l);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(a,d),i=o(y,i,m),null===u?c=y:u.sibling=y,u=y,d=g}if(m===s.length)return n(a,d),ai&&Xa(a,m),c;if(null===d){for(;m<s.length;m++)null!==(d=f(a,s[m],l))&&(i=o(d,i,m),null===u?c=d:u.sibling=d,u=d);return ai&&Xa(a,m),c}for(d=r(a,d);m<s.length;m++)null!==(g=p(d,a,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),i=o(g,i,m),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach(function(e){return t(a,e)}),ai&&Xa(a,m),c}function g(a,s,l,c){var u=D(l);if("function"!==typeof u)throw Error(i(150));if(null==(l=u.call(l)))throw Error(i(151));for(var d=u=null,m=s,g=s=0,y=null,v=l.next();null!==m&&!v.done;g++,v=l.next()){m.index>g?(y=m,m=null):y=m.sibling;var x=h(a,m,v.value,c);if(null===x){null===m&&(m=y);break}e&&m&&null===x.alternate&&t(a,m),s=o(x,s,g),null===d?u=x:d.sibling=x,d=x,m=y}if(v.done)return n(a,m),ai&&Xa(a,g),u;if(null===m){for(;!v.done;g++,v=l.next())null!==(v=f(a,v.value,c))&&(s=o(v,s,g),null===d?u=v:d.sibling=v,d=v);return ai&&Xa(a,g),u}for(m=r(a,m);!v.done;g++,v=l.next())null!==(v=p(m,a,g,v.value,c))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),s=o(v,s,g),null===d?u=v:d.sibling=v,d=v);return e&&m.forEach(function(e){return t(a,e)}),ai&&Xa(a,g),u}return function e(r,i,o,l){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var c=o.key,u=i;null!==u;){if(u.key===c){if((c=o.type)===S){if(7===u.tag){n(r,u.sibling),(i=a(u,o.props.children)).return=r,r=i;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===R&&vi(c)===u.type){n(r,u.sibling),(i=a(u,o.props)).ref=gi(r,u,o),i.return=r,r=i;break e}n(r,u);break}t(r,u),u=u.sibling}o.type===S?((i=Dc(o.props.children,r.mode,l,o.key)).return=r,r=i):((l=_c(o.type,o.key,o.props,null,r.mode,l)).ref=gi(r,i,o),l.return=r,r=l)}return s(r);case k:e:{for(u=o.key;null!==i;){if(i.key===u){if(4===i.tag&&i.stateNode.containerInfo===o.containerInfo&&i.stateNode.implementation===o.implementation){n(r,i.sibling),(i=a(i,o.children||[])).return=r,r=i;break e}n(r,i);break}t(r,i),i=i.sibling}(i=Vc(o,r.mode,l)).return=r,r=i}return s(r);case R:return e(r,i,(u=o._init)(o._payload),l)}if(te(o))return m(r,i,o,l);if(D(o))return g(r,i,o,l);yi(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==i&&6===i.tag?(n(r,i.sibling),(i=a(i,o)).return=r,r=i):(n(r,i),(i=Ic(o,r.mode,l)).return=r,r=i),s(r)):n(r,i)}}var bi=xi(!0),wi=xi(!1),ki=ja(null),Si=null,ji=null,Ei=null;function Ni(){Ei=ji=Si=null}function Ci(e){var t=ki.current;Ea(ki),e._currentValue=t}function Pi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ai(e,t){Si=e,Ei=ji=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(xs=!0),e.firstContext=null)}function Ti(e){var t=e._currentValue;if(Ei!==e)if(e={context:e,memoizedValue:t,next:null},null===ji){if(null===Si)throw Error(i(308));ji=e,Si.dependencies={lanes:0,firstContext:e}}else ji=ji.next=e;return t}var Li=null;function Ri(e){null===Li?Li=[e]:Li.push(e)}function Mi(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Ri(t)):(n.next=a.next,a.next=n),t.interleaved=n,_i(e,r)}function _i(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Di=!1;function Oi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ii(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Fi(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Pl)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,_i(e,n)}return null===(a=r.interleaved)?(t.next=t,Ri(r)):(t.next=a.next,a.next=t),r.interleaved=t,_i(e,n)}function Bi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function zi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?a=i=o:i=i.next=o,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ui(e,t,n,r){var a=e.updateQueue;Di=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,c=l.next;l.next=null,null===o?i=c:o.next=c,o=l;var u=e.alternate;null!==u&&((s=(u=u.updateQueue).lastBaseUpdate)!==o&&(null===s?u.firstBaseUpdate=c:s.next=c,u.lastBaseUpdate=l))}if(null!==i){var d=a.baseState;for(o=0,u=c=l=null,s=i;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=I({},d,f);break e;case 2:Di=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===u?(c=u=h,l=d):u=u.next=h,o|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(f=s).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(l=d),a.baseState=l,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===i&&(a.shared.lanes=0);Ol|=o,e.lanes=o,e.memoizedState=d}}function Wi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(i(191,a));a.call(r)}}}var Hi={},qi=ja(Hi),$i=ja(Hi),Qi=ja(Hi);function Gi(e){if(e===Hi)throw Error(i(174));return e}function Zi(e,t){switch(Na(Qi,t),Na($i,e),Na(qi,Hi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ea(qi),Na(qi,t)}function Ki(){Ea(qi),Ea($i),Ea(Qi)}function Yi(e){Gi(Qi.current);var t=Gi(qi.current),n=le(t,e.type);t!==n&&(Na($i,e),Na(qi,n))}function Xi(e){$i.current===e&&(Ea(qi),Ea($i))}var Ji=ja(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=b.ReactCurrentDispatcher,ao=b.ReactCurrentBatchConfig,io=0,oo=null,so=null,lo=null,co=!1,uo=!1,fo=0,ho=0;function po(){throw Error(i(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(io=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Jo:es,e=n(r,a),uo){o=0;do{if(uo=!1,fo=0,25<=o)throw Error(i(301));o+=1,lo=so=null,t.updateQueue=null,ro.current=ts,e=n(r,a)}while(uo)}if(ro.current=Xo,t=null!==so&&null!==so.next,io=0,lo=so=oo=null,co=!1,t)throw Error(i(300));return e}function yo(){var e=0!==fo;return fo=0,e}function vo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===lo?oo.memoizedState=lo=e:lo=lo.next=e,lo}function xo(){if(null===so){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=so.next;var t=null===lo?oo.memoizedState:lo.next;if(null!==t)lo=t,so=e;else{if(null===e)throw Error(i(310));e={memoizedState:(so=e).memoizedState,baseState:so.baseState,baseQueue:so.baseQueue,queue:so.queue,next:null},null===lo?oo.memoizedState=lo=e:lo=lo.next=e}return lo}function bo(e,t){return"function"===typeof t?t(e):t}function wo(e){var t=xo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=so,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var l=s=null,c=null,u=o;do{var d=u.lane;if((io&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(l=c=f,s=r):c=c.next=f,oo.lanes|=d,Ol|=d}u=u.next}while(null!==u&&u!==o);null===c?s=r:c.next=l,sr(r,t.memoizedState)||(xs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Ol|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ko(e){var t=xo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=e(o,s.action),s=s.next}while(s!==a);sr(o,t.memoizedState)||(xs=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function So(){}function jo(e,t){var n=oo,r=xo(),a=t(),o=!sr(r.memoizedState,a);if(o&&(r.memoizedState=a,xs=!0),r=r.queue,Oo(Co.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==lo&&1&lo.memoizedState.tag){if(n.flags|=2048,Lo(9,No.bind(null,n,r,a,t),void 0,null),null===Al)throw Error(i(349));0!==(30&io)||Eo(n,t,a)}return a}function Eo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function No(e,t,n,r){t.value=n,t.getSnapshot=r,Po(t)&&Ao(e)}function Co(e,t,n){return n(function(){Po(t)&&Ao(e)})}function Po(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Ao(e){var t=_i(e,1);null!==t&&nc(t,e,1,-1)}function To(e){var t=vo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:bo,lastRenderedState:e},t.queue=e,e=e.dispatch=Go.bind(null,oo,e),[t.memoizedState,e]}function Lo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ro(){return xo().memoizedState}function Mo(e,t,n,r){var a=vo();oo.flags|=e,a.memoizedState=Lo(1|t,n,void 0,void 0===r?null:r)}function _o(e,t,n,r){var a=xo();r=void 0===r?null:r;var i=void 0;if(null!==so){var o=so.memoizedState;if(i=o.destroy,null!==r&&mo(r,o.deps))return void(a.memoizedState=Lo(t,n,i,r))}oo.flags|=e,a.memoizedState=Lo(1|t,n,i,r)}function Do(e,t){return Mo(8390656,8,e,t)}function Oo(e,t){return _o(2048,8,e,t)}function Io(e,t){return _o(4,2,e,t)}function Vo(e,t){return _o(4,4,e,t)}function Fo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,_o(4,4,Fo.bind(null,t,e),n)}function zo(){}function Uo(e,t){var n=xo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wo(e,t){var n=xo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ho(e,t,n){return 0===(21&io)?(e.baseState&&(e.baseState=!1,xs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),oo.lanes|=n,Ol|=n,e.baseState=!0),t)}function qo(e,t){var n=xt;xt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{xt=n,ao.transition=r}}function $o(){return xo().memoizedState}function Qo(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Zo(e))Ko(t,n);else if(null!==(n=Mi(e,t,n,r))){nc(n,e,r,ec()),Yo(n,t,r)}}function Go(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Zo(e))Ko(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var o=t.lastRenderedState,s=i(o,n);if(a.hasEagerState=!0,a.eagerState=s,sr(s,o)){var l=t.interleaved;return null===l?(a.next=a,Ri(t)):(a.next=l.next,l.next=a),void(t.interleaved=a)}}catch(c){}null!==(n=Mi(e,t,a,r))&&(nc(n,e,r,a=ec()),Yo(n,t,r))}}function Zo(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Ko(e,t){uo=co=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yo(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Xo={readContext:Ti,useCallback:po,useContext:po,useEffect:po,useImperativeHandle:po,useInsertionEffect:po,useLayoutEffect:po,useMemo:po,useReducer:po,useRef:po,useState:po,useDebugValue:po,useDeferredValue:po,useTransition:po,useMutableSource:po,useSyncExternalStore:po,useId:po,unstable_isNewReconciler:!1},Jo={readContext:Ti,useCallback:function(e,t){return vo().memoizedState=[e,void 0===t?null:t],e},useContext:Ti,useEffect:Do,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Mo(4194308,4,Fo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Mo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Mo(4,2,e,t)},useMemo:function(e,t){var n=vo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vo().memoizedState=e},useState:To,useDebugValue:zo,useDeferredValue:function(e){return vo().memoizedState=e},useTransition:function(){var e=To(!1),t=e[0];return e=qo.bind(null,e[1]),vo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=vo();if(ai){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===Al)throw Error(i(349));0!==(30&io)||Eo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Do(Co.bind(null,r,o,e),[e]),r.flags|=2048,Lo(9,No.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=vo(),t=Al.identifierPrefix;if(ai){var n=Ya;t=":"+t+"R"+(n=(Ka&~(1<<32-ot(Ka)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ho++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Ti,useCallback:Uo,useContext:Ti,useEffect:Oo,useImperativeHandle:Bo,useInsertionEffect:Io,useLayoutEffect:Vo,useMemo:Wo,useReducer:wo,useRef:Ro,useState:function(){return wo(bo)},useDebugValue:zo,useDeferredValue:function(e){return Ho(xo(),so.memoizedState,e)},useTransition:function(){return[wo(bo)[0],xo().memoizedState]},useMutableSource:So,useSyncExternalStore:jo,useId:$o,unstable_isNewReconciler:!1},ts={readContext:Ti,useCallback:Uo,useContext:Ti,useEffect:Oo,useImperativeHandle:Bo,useInsertionEffect:Io,useLayoutEffect:Vo,useMemo:Wo,useReducer:ko,useRef:Ro,useState:function(){return ko(bo)},useDebugValue:zo,useDeferredValue:function(e){var t=xo();return null===so?t.memoizedState=e:Ho(t,so.memoizedState,e)},useTransition:function(){return[ko(bo)[0],xo().memoizedState]},useMutableSource:So,useSyncExternalStore:jo,useId:$o,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=I({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:I({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),i=Vi(r,a);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Fi(e,i,a))&&(nc(t,e,a,r),Bi(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),i=Vi(r,a);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Fi(e,i,a))&&(nc(t,e,a,r),Bi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Vi(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Fi(e,a,r))&&(nc(t,e,r,n),Bi(t,e,r))}};function is(e,t,n,r,a,i,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,o):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(a,i))}function os(e,t,n){var r=!1,a=Ca,i=t.contextType;return"object"===typeof i&&null!==i?i=Ti(i):(a=Ra(t)?Ta:Pa.current,i=(r=null!==(r=t.contextTypes)&&void 0!==r)?La(e,a):Ca),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Oi(e);var i=t.contextType;"object"===typeof i&&null!==i?a.context=Ti(i):(i=Ra(t)?Ta:Pa.current,a.context=La(e,i)),a.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(rs(e,t,i,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&as.enqueueReplaceState(a,a.state,null),Ui(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function cs(e,t){try{var n="",r=t;do{n+=z(r),r=r.return}while(r);var a=n}catch(i){a="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:a,digest:null}}function us(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=Vi(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hl||(Hl=!0,ql=r),ds(0,t)},n}function ps(e,t,n){(n=Vi(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ds(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===$l?$l=new Set([this]):$l.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Ec.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Vi(-1,1)).tag=2,Fi(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var vs=b.ReactCurrentOwner,xs=!1;function bs(e,t,n,r){t.child=null===e?wi(t,null,n,r):bi(t,e.child,n,r)}function ws(e,t,n,r,a){n=n.render;var i=t.ref;return Ai(t,a),r=go(e,t,n,r,i,a),n=yo(),null===e||xs?(ai&&n&&ei(t),t.flags|=1,bs(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hs(e,t,a))}function ks(e,t,n,r,a){if(null===e){var i=n.type;return"function"!==typeof i||Rc(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=_c(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Ss(e,t,i,r,a))}if(i=e.child,0===(e.lanes&a)){var o=i.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(o,r)&&e.ref===t.ref)return Hs(e,t,a)}return t.flags|=1,(e=Mc(i,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,a){if(null!==e){var i=e.memoizedProps;if(lr(i,r)&&e.ref===t.ref){if(xs=!1,t.pendingProps=r=i,0===(e.lanes&a))return t.lanes=e.lanes,Hs(e,t,a);0!==(131072&e.flags)&&(xs=!0)}}return Ns(e,t,n,r,a)}function js(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Na(Ml,Rl),Rl|=n;else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Na(Ml,Rl),Rl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==i?i.baseLanes:n,Na(Ml,Rl),Rl|=r}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,Na(Ml,Rl),Rl|=r;return bs(e,t,a,n),t.child}function Es(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ns(e,t,n,r,a){var i=Ra(n)?Ta:Pa.current;return i=La(t,i),Ai(t,a),n=go(e,t,n,r,i,a),r=yo(),null===e||xs?(ai&&r&&ei(t),t.flags|=1,bs(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hs(e,t,a))}function Cs(e,t,n,r,a){if(Ra(n)){var i=!0;Oa(t)}else i=!1;if(Ai(t,a),null===t.stateNode)Ws(e,t),os(t,n,r),ls(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,s=t.memoizedProps;o.props=s;var l=o.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ti(c):c=La(t,c=Ra(n)?Ta:Pa.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==r||l!==c)&&ss(t,o,r,c),Di=!1;var f=t.memoizedState;o.state=f,Ui(t,r,o,a),l=t.memoizedState,s!==r||f!==l||Aa.current||Di?("function"===typeof u&&(rs(t,n,u,r),l=t.memoizedState),(s=Di||is(t,n,s,r,f,l,c))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=c,r=s):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Ii(e,t),s=t.memoizedProps,c=t.type===t.elementType?s:ns(t.type,s),o.props=c,d=t.pendingProps,f=o.context,"object"===typeof(l=n.contextType)&&null!==l?l=Ti(l):l=La(t,l=Ra(n)?Ta:Pa.current);var h=n.getDerivedStateFromProps;(u="function"===typeof h||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,o,r,l),Di=!1,f=t.memoizedState,o.state=f,Ui(t,r,o,a);var p=t.memoizedState;s!==d||f!==p||Aa.current||Di?("function"===typeof h&&(rs(t,n,h,r),p=t.memoizedState),(c=Di||is(t,n,c,r,f,p,l)||!1)?(u||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=l,r=c):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ps(e,t,n,r,i,a)}function Ps(e,t,n,r,a,i){Es(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Ia(t,n,!1),Hs(e,t,i);r=t.stateNode,vs.current=t;var s=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=bi(t,e.child,null,i),t.child=bi(t,null,s,i)):bs(e,t,s,i),t.memoizedState=r.state,a&&Ia(t,n,!0),t.child}function As(e){var t=e.stateNode;t.pendingContext?_a(0,t.pendingContext,t.pendingContext!==t.context):t.context&&_a(0,t.context,!1),Zi(e,t.containerInfo)}function Ts(e,t,n,r,a){return hi(),pi(a),t.flags|=256,bs(e,t,n,r),t.child}var Ls,Rs,Ms,_s,Ds={dehydrated:null,treeContext:null,retryLane:0};function Os(e){return{baseLanes:e,cachePool:null,transitions:null}}function Is(e,t,n){var r,a=t.pendingProps,o=Ji.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Na(Ji,1&o),null===e)return ci(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=a.children,e=a.fallback,s?(a=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&a)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Oc(l,a,0,null),e=Dc(e,a,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Os(n),t.memoizedState=Ds,e):Vs(t,l));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,s){if(n)return 256&t.flags?(t.flags&=-257,Fs(e,t,s,r=us(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Oc({mode:"visible",children:r.children},a,0,null),(o=Dc(o,a,s,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&bi(t,e.child,null,s),t.child.memoizedState=Os(s),t.memoizedState=Ds,o);if(0===(1&t.mode))return Fs(e,t,s,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var l=r.dgst;return r=l,Fs(e,t,s,r=us(o=Error(i(419)),r,void 0))}if(l=0!==(s&e.childLanes),xs||l){if(null!==(r=Al)){switch(s&-s){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|s))?0:a)&&a!==o.retryLane&&(o.retryLane=a,_i(e,a),nc(r,e,a,-1))}return mc(),Fs(e,t,s,r=us(Error(i(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cc.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,ri=ca(a.nextSibling),ni=t,ai=!0,ii=null,null!==e&&(Qa[Ga++]=Ka,Qa[Ga++]=Ya,Qa[Ga++]=Za,Ka=e.id,Ya=e.overflow,Za=t),t=Vs(t,r.children),t.flags|=4096,t)}(e,t,l,a,r,o,n);if(s){s=a.fallback,l=t.mode,r=(o=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&l)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Mc(o,c)).subtreeFlags=14680064&o.subtreeFlags,null!==r?s=Mc(r,s):(s=Dc(s,l,n,null)).flags|=2,s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,l=null===(l=e.child.memoizedState)?Os(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Ds,a}return e=(s=e.child).sibling,a=Mc(s,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Vs(e,t){return(t=Oc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Fs(e,t,n,r){return null!==r&&pi(r),bi(t,e.child,null,n),(e=Vs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Pi(e.return,t,n)}function zs(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function Us(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(bs(e,t,r.children,n),0!==(2&(r=Ji.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bs(e,n,t);else if(19===e.tag)Bs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Na(Ji,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),zs(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}zs(t,!0,n,null,i);break;case"together":zs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ws(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ol|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Mc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function qs(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function $s(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qs(e,t,n){var r=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $s(t),null;case 1:case 17:return Ra(t.type)&&Ma(),$s(t),null;case 3:return r=t.stateNode,Ki(),Ea(Aa),Ea(Pa),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(di(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ii&&(oc(ii),ii=null))),Rs(e,t),$s(t),null;case 5:Xi(t);var a=Gi(Qi.current);if(n=t.type,null!==e&&null!=t.stateNode)Ms(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(i(166));return $s(t),null}if(e=Gi(qi.current),di(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[ha]=o,e=0!==(1&t.mode),n){case"dialog":Fr("cancel",r),Fr("close",r);break;case"iframe":case"object":case"embed":Fr("load",r);break;case"video":case"audio":for(a=0;a<Dr.length;a++)Fr(Dr[a],r);break;case"source":Fr("error",r);break;case"img":case"image":case"link":Fr("error",r),Fr("load",r);break;case"details":Fr("toggle",r);break;case"input":K(r,o),Fr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Fr("invalid",r);break;case"textarea":ae(r,o),Fr("invalid",r)}for(var l in ve(n,o),a=null,o)if(o.hasOwnProperty(l)){var c=o[l];"children"===l?"string"===typeof c?r.textContent!==c&&(!0!==o.suppressHydrationWarning&&Xr(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==o.suppressHydrationWarning&&Xr(r.textContent,c,e),a=["children",""+c]):s.hasOwnProperty(l)&&null!=c&&"onScroll"===l&&Fr("scroll",r)}switch(n){case"input":$(r),J(r,o,!0);break;case"textarea":$(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Jr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fa]=t,e[ha]=r,Ls(e,t,!1,!1),t.stateNode=e;e:{switch(l=xe(n,r),n){case"dialog":Fr("cancel",e),Fr("close",e),a=r;break;case"iframe":case"object":case"embed":Fr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Dr.length;a++)Fr(Dr[a],e);a=r;break;case"source":Fr("error",e),a=r;break;case"img":case"image":case"link":Fr("error",e),Fr("load",e),a=r;break;case"details":Fr("toggle",e),a=r;break;case"input":K(e,r),a=Z(e,r),Fr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=I({},r,{value:void 0}),Fr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Fr("invalid",e)}for(o in ve(n,a),c=a)if(c.hasOwnProperty(o)){var u=c[o];"style"===o?ge(e,u):"dangerouslySetInnerHTML"===o?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===o?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(s.hasOwnProperty(o)?null!=u&&"onScroll"===o&&Fr("scroll",e):null!=u&&x(e,o,u,l))}switch(n){case"input":$(e),J(e,r,!1);break;case"textarea":$(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return $s(t),null;case 6:if(e&&null!=t.stateNode)_s(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(n=Gi(Qi.current),Gi(qi.current),di(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=ni))switch(e.tag){case 3:Xr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return $s(t),null;case 13:if(Ea(Ji),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ai&&null!==ri&&0!==(1&t.mode)&&0===(128&t.flags))fi(),hi(),t.flags|=98560,o=!1;else if(o=di(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(i(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(i(317));o[fa]=t}else hi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;$s(t),o=!1}else null!==ii&&(oc(ii),ii=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Ji.current)?0===_l&&(_l=3):mc())),null!==t.updateQueue&&(t.flags|=4),$s(t),null);case 4:return Ki(),Rs(e,t),null===e&&Ur(t.stateNode.containerInfo),$s(t),null;case 10:return Ci(t.type._context),$s(t),null;case 19:if(Ea(Ji),null===(o=t.memoizedState))return $s(t),null;if(r=0!==(128&t.flags),null===(l=o.rendering))if(r)qs(o,!1);else{if(0!==_l||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=eo(e))){for(t.flags|=128,qs(o,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(l=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Na(Ji,1&Ji.current|2),t.child}e=e.sibling}null!==o.tail&&Ye()>Ul&&(t.flags|=128,r=!0,qs(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),qs(o,!0),null===o.tail&&"hidden"===o.tailMode&&!l.alternate&&!ai)return $s(t),null}else 2*Ye()-o.renderingStartTime>Ul&&1073741824!==n&&(t.flags|=128,r=!0,qs(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=o.last)?n.sibling=l:t.child=l,o.last=l)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ye(),t.sibling=null,n=Ji.current,Na(Ji,r?1&n|2:1&n),t):($s(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rl)&&($s(t),6&t.subtreeFlags&&(t.flags|=8192)):$s(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Gs(e,t){switch(ti(t),t.tag){case 1:return Ra(t.type)&&Ma(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ki(),Ea(Aa),Ea(Pa),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Xi(t),null;case 13:if(Ea(Ji),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));hi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ea(Ji),null;case 4:return Ki(),null;case 10:return Ci(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Ls=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Rs=function(){},Ms=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Gi(qi.current);var i,o=null;switch(n){case"input":a=Z(e,a),r=Z(e,r),o=[];break;case"select":a=I({},a,{value:void 0}),r=I({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(u in ve(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var l=a[u];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(s.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var c=r[u];if(l=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(null!=c||null!=l))if("style"===u)if(l){for(i in l)!l.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&l[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(o||(o=[]),o.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,l=l?l.__html:void 0,null!=c&&l!==c&&(o=o||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(o=o||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(s.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Fr("scroll",e),o||l===c||(o=[])):(o=o||[]).push(u,c))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}},_s=function(e,t,n,r){n!==r&&(t.flags|=4)};var Zs=!1,Ks=!1,Ys="function"===typeof WeakSet?WeakSet:Set,Xs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){jc(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){jc(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&el(t,n,i)}a=a.next}while(a!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function il(e){var t=e.alternate;null!==t&&(e.alternate=null,il(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[ha],delete t[ma],delete t[ga],delete t[ya])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ol(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ol(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function cl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cl(e,t,n),e=e.sibling;null!==e;)cl(e,t,n),e=e.sibling}var ul=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(at,n)}catch(s){}switch(n.tag){case 5:Ks||Js(n,t);case 6:var r=ul,a=dl;ul=null,fl(e,t,n),dl=a,null!==(ul=r)&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ul.removeChild(n.stateNode));break;case 18:null!==ul&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?la(e.parentNode,n):1===e.nodeType&&la(e,n),Ut(e)):la(ul,n.stateNode));break;case 4:r=ul,a=dl,ul=n.stateNode.containerInfo,dl=!0,fl(e,t,n),ul=r,dl=a;break;case 0:case 11:case 14:case 15:if(!Ks&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var i=a,o=i.destroy;i=i.tag,void 0!==o&&(0!==(2&i)||0!==(4&i))&&el(n,t,o),a=a.next}while(a!==r)}fl(e,t,n);break;case 1:if(!Ks&&(Js(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){jc(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Ks=(r=Ks)||null!==n.memoizedState,fl(e,t,n),Ks=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Ys),t.forEach(function(t){var r=Pc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:ul=l.stateNode,dl=!1;break e;case 3:case 4:ul=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===ul)throw Error(i(160));hl(o,s,a),ul=null,dl=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){jc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),yl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){jc(e,e.return,g)}try{nl(5,e,e.return)}catch(g){jc(e,e.return,g)}}break;case 1:ml(t,e),yl(e),512&r&&null!==n&&Js(n,n.return);break;case 5:if(ml(t,e),yl(e),512&r&&null!==n&&Js(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){jc(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,s=null!==n?n.memoizedProps:o,l=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===l&&"radio"===o.type&&null!=o.name&&Y(a,o),xe(l,s);var u=xe(l,o);for(s=0;s<c.length;s+=2){var d=c[s],f=c[s+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):x(a,d,f,u)}switch(l){case"input":X(a,o);break;case"textarea":ie(a,o);break;case"select":var h=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var p=o.value;null!=p?ne(a,!!o.multiple,p,!1):h!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[ha]=o}catch(g){jc(e,e.return,g)}}break;case 6:if(ml(t,e),yl(e),4&r){if(null===e.stateNode)throw Error(i(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){jc(e,e.return,g)}}break;case 3:if(ml(t,e),yl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(g){jc(e,e.return,g)}break;case 4:default:ml(t,e),yl(e);break;case 13:ml(t,e),yl(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(zl=Ye())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ks=(u=Ks)||d,ml(t,e),Ks=u):ml(t,e),yl(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Xs=e,d=e.child;null!==d;){for(f=Xs=d;null!==Xs;){switch(p=(h=Xs).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Js(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){jc(r,n,g)}}break;case 5:Js(h,h.return);break;case 22:if(null!==h.memoizedState){wl(f);continue}}null!==p?(p.return=h,Xs=p):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(l=f.stateNode,s=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,l.style.display=me("display",s))}catch(g){jc(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){jc(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),yl(e),4&r&&pl(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ol(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),cl(e,sl(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ll(e,sl(e),o);break;default:throw Error(i(161))}}catch(s){jc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Xs=e,xl(e,t,n)}function xl(e,t,n){for(var r=0!==(1&e.mode);null!==Xs;){var a=Xs,i=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Zs;if(!o){var s=a.alternate,l=null!==s&&null!==s.memoizedState||Ks;s=Zs;var c=Ks;if(Zs=o,(Ks=l)&&!c)for(Xs=a;null!==Xs;)l=(o=Xs).child,22===o.tag&&null!==o.memoizedState?kl(a):null!==l?(l.return=o,Xs=l):kl(a);for(;null!==i;)Xs=i,xl(i,t,n),i=i.sibling;Xs=a,Zs=s,Ks=c}bl(e)}else 0!==(8772&a.subtreeFlags)&&null!==i?(i.return=a,Xs=i):bl(e)}}function bl(e){for(;null!==Xs;){var t=Xs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ks||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ks)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Wi(t,o,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wi(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ut(f)}}}break;default:throw Error(i(163))}Ks||512&t.flags&&al(t)}catch(h){jc(t,t.return,h)}}if(t===e){Xs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xs=n;break}Xs=t.return}}function wl(e){for(;null!==Xs;){var t=Xs;if(t===e){Xs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xs=n;break}Xs=t.return}}function kl(e){for(;null!==Xs;){var t=Xs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){jc(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(l){jc(t,a,l)}}var i=t.return;try{al(t)}catch(l){jc(t,i,l)}break;case 5:var o=t.return;try{al(t)}catch(l){jc(t,o,l)}}}catch(l){jc(t,t.return,l)}if(t===e){Xs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Xs=s;break}Xs=t.return}}var Sl,jl=Math.ceil,El=b.ReactCurrentDispatcher,Nl=b.ReactCurrentOwner,Cl=b.ReactCurrentBatchConfig,Pl=0,Al=null,Tl=null,Ll=0,Rl=0,Ml=ja(0),_l=0,Dl=null,Ol=0,Il=0,Vl=0,Fl=null,Bl=null,zl=0,Ul=1/0,Wl=null,Hl=!1,ql=null,$l=null,Ql=!1,Gl=null,Zl=0,Kl=0,Yl=null,Xl=-1,Jl=0;function ec(){return 0!==(6&Pl)?Ye():-1!==Xl?Xl:Xl=Ye()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Pl)&&0!==Ll?Ll&-Ll:null!==mi.transition?(0===Jl&&(Jl=mt()),Jl):0!==(e=xt)?e:e=void 0===(e=window.event)?16:Kt(e.type)}function nc(e,t,n,r){if(50<Kl)throw Kl=0,Yl=null,Error(i(185));yt(e,n,r),0!==(2&Pl)&&e===Al||(e===Al&&(0===(2&Pl)&&(Il|=n),4===_l&&sc(e,Ll)),rc(e,r),1===n&&0===Pl&&0===(1&t.mode)&&(Ul=Ye()+500,Fa&&Ua()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-ot(i),s=1<<o,l=a[o];-1===l?0!==(s&n)&&0===(s&r)||(a[o]=ht(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}(e,t);var r=ft(e,e===Al?Ll:0);if(0===r)null!==n&&Ge(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ge(n),1===t)0===e.tag?function(e){Fa=!0,za(e)}(lc.bind(null,e)):za(lc.bind(null,e)),oa(function(){0===(6&Pl)&&Ua()}),n=null;else{switch(bt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Ac(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Xl=-1,Jl=0,0!==(6&Pl))throw Error(i(327));var n=e.callbackNode;if(kc()&&e.callbackNode!==n)return null;var r=ft(e,e===Al?Ll:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var a=Pl;Pl|=2;var o=pc();for(Al===e&&Ll===t||(Wl=null,Ul=Ye()+500,fc(e,t));;)try{vc();break}catch(l){hc(e,l)}Ni(),El.current=o,Pl=a,null!==Tl?t=0:(Al=null,Ll=0,t=_l)}if(0!==t){if(2===t&&(0!==(a=pt(e))&&(r=a,t=ic(e,a))),1===t)throw n=Dl,fc(e,0),sc(e,r),rc(e,Ye()),n;if(6===t)sc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!sr(i(),a))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gc(e,r))&&(0!==(o=pt(e))&&(r=o,t=ic(e,o))),1===t))throw n=Dl,fc(e,0),sc(e,r),rc(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:case 5:wc(e,Bl,Wl);break;case 3:if(sc(e,r),(130023424&r)===r&&10<(t=zl+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wc.bind(null,e,Bl,Wl),t);break}wc(e,Bl,Wl);break;case 4:if(sc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var s=31-ot(r);o=1<<s,(s=t[s])>a&&(a=s),r&=~o}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jl(r/1960))-r)){e.timeoutHandle=ra(wc.bind(null,e,Bl,Wl),r);break}wc(e,Bl,Wl);break;default:throw Error(i(329))}}}return rc(e,Ye()),e.callbackNode===n?ac.bind(null,e):null}function ic(e,t){var n=Fl;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Bl,Bl=n,null!==t&&oc(t)),e}function oc(e){null===Bl?Bl=e:Bl.push.apply(Bl,e)}function sc(e,t){for(t&=~Vl,t&=~Il,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function lc(e){if(0!==(6&Pl))throw Error(i(327));kc();var t=ft(e,0);if(0===(1&t))return rc(e,Ye()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=ic(e,r))}if(1===n)throw n=Dl,fc(e,0),sc(e,t),rc(e,Ye()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Bl,Wl),rc(e,Ye()),null}function cc(e,t){var n=Pl;Pl|=1;try{return e(t)}finally{0===(Pl=n)&&(Ul=Ye()+500,Fa&&Ua())}}function uc(e){null!==Gl&&0===Gl.tag&&0===(6&Pl)&&kc();var t=Pl;Pl|=1;var n=Cl.transition,r=xt;try{if(Cl.transition=null,xt=1,e)return e()}finally{xt=r,Cl.transition=n,0===(6&(Pl=t))&&Ua()}}function dc(){Rl=Ml.current,Ea(Ml)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Tl)for(n=Tl.return;null!==n;){var r=n;switch(ti(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ma();break;case 3:Ki(),Ea(Aa),Ea(Pa),no();break;case 5:Xi(r);break;case 4:Ki();break;case 13:case 19:Ea(Ji);break;case 10:Ci(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Al=e,Tl=e=Mc(e.current,null),Ll=Rl=t,_l=0,Dl=null,Vl=Il=Ol=0,Bl=Fl=null,null!==Li){for(t=0;t<Li.length;t++)if(null!==(r=(n=Li[t]).interleaved)){n.interleaved=null;var a=r.next,i=n.pending;if(null!==i){var o=i.next;i.next=a,r.next=o}n.pending=r}Li=null}return e}function hc(e,t){for(;;){var n=Tl;try{if(Ni(),ro.current=Xo,co){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}co=!1}if(io=0,lo=so=oo=null,uo=!1,fo=0,Nl.current=null,null===n||null===n.return){_l=1,Dl=t,Tl=null;break}e:{var o=e,s=n.return,l=n,c=t;if(t=Ll,l.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=gs(s);if(null!==p){p.flags&=-257,ys(p,s,l,0,t),1&p.mode&&ms(o,u,t),c=u;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){ms(o,u,t),mc();break e}c=Error(i(426))}else if(ai&&1&l.mode){var y=gs(s);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),ys(y,s,l,0,t),pi(cs(c,l));break e}}o=c=cs(c,l),4!==_l&&(_l=2),null===Fl?Fl=[o]:Fl.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,zi(o,hs(0,c,t));break e;case 1:l=c;var v=o.type,x=o.stateNode;if(0===(128&o.flags)&&("function"===typeof v.getDerivedStateFromError||null!==x&&"function"===typeof x.componentDidCatch&&(null===$l||!$l.has(x)))){o.flags|=65536,t&=-t,o.lanes|=t,zi(o,ps(o,l,t));break e}}o=o.return}while(null!==o)}bc(n)}catch(b){t=b,Tl===n&&null!==n&&(Tl=n=n.return);continue}break}}function pc(){var e=El.current;return El.current=Xo,null===e?Xo:e}function mc(){0!==_l&&3!==_l&&2!==_l||(_l=4),null===Al||0===(268435455&Ol)&&0===(268435455&Il)||sc(Al,Ll)}function gc(e,t){var n=Pl;Pl|=2;var r=pc();for(Al===e&&Ll===t||(Wl=null,fc(e,t));;)try{yc();break}catch(a){hc(e,a)}if(Ni(),Pl=n,El.current=r,null!==Tl)throw Error(i(261));return Al=null,Ll=0,_l}function yc(){for(;null!==Tl;)xc(Tl)}function vc(){for(;null!==Tl&&!Ze();)xc(Tl)}function xc(e){var t=Sl(e.alternate,e,Rl);e.memoizedProps=e.pendingProps,null===t?bc(e):Tl=t,Nl.current=null}function bc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qs(n,t,Rl)))return void(Tl=n)}else{if(null!==(n=Gs(n,t)))return n.flags&=32767,void(Tl=n);if(null===e)return _l=6,void(Tl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Tl=t);Tl=t=e}while(null!==t);0===_l&&(_l=5)}function wc(e,t,n){var r=xt,a=Cl.transition;try{Cl.transition=null,xt=1,function(e,t,n,r){do{kc()}while(null!==Gl);if(0!==(6&Pl))throw Error(i(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}(e,o),e===Al&&(Tl=Al=null,Ll=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ql||(Ql=!0,Ac(tt,function(){return kc(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Cl.transition,Cl.transition=null;var s=xt;xt=1;var l=Pl;Pl|=4,Nl.current=null,function(e,t){if(ea=Ht,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var s=0,l=-1,c=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==o||0!==r&&3!==f.nodeType||(c=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++u===a&&(l=s),h===o&&++d===r&&(c=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Ht=!1,Xs=t;null!==Xs;)if(e=(t=Xs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Xs=e;else for(;null!==Xs;){t=Xs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,x=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(i(163))}}catch(w){jc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Xs=e;break}Xs=t.return}m=tl,tl=!1}(e,n),gl(n,e),pr(ta),Ht=!!ea,ta=ea=null,e.current=n,vl(n,e,a),Ke(),Pl=l,xt=s,Cl.transition=o}else e.current=n;if(Ql&&(Ql=!1,Gl=e,Zl=a),o=e.pendingLanes,0===o&&($l=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Hl)throw Hl=!1,e=ql,ql=null,e;0!==(1&Zl)&&0!==e.tag&&kc(),o=e.pendingLanes,0!==(1&o)?e===Yl?Kl++:(Kl=0,Yl=e):Kl=0,Ua()}(e,t,n,r)}finally{Cl.transition=a,xt=r}return null}function kc(){if(null!==Gl){var e=bt(Zl),t=Cl.transition,n=xt;try{if(Cl.transition=null,xt=16>e?16:e,null===Gl)var r=!1;else{if(e=Gl,Gl=null,Zl=0,0!==(6&Pl))throw Error(i(331));var a=Pl;for(Pl|=4,Xs=e.current;null!==Xs;){var o=Xs,s=o.child;if(0!==(16&Xs.flags)){var l=o.deletions;if(null!==l){for(var c=0;c<l.length;c++){var u=l[c];for(Xs=u;null!==Xs;){var d=Xs;switch(d.tag){case 0:case 11:case 15:nl(8,d,o)}var f=d.child;if(null!==f)f.return=d,Xs=f;else for(;null!==Xs;){var h=(d=Xs).sibling,p=d.return;if(il(d),d===u){Xs=null;break}if(null!==h){h.return=p,Xs=h;break}Xs=p}}}var m=o.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Xs=o}}if(0!==(2064&o.subtreeFlags)&&null!==s)s.return=o,Xs=s;else e:for(;null!==Xs;){if(0!==(2048&(o=Xs).flags))switch(o.tag){case 0:case 11:case 15:nl(9,o,o.return)}var v=o.sibling;if(null!==v){v.return=o.return,Xs=v;break e}Xs=o.return}}var x=e.current;for(Xs=x;null!==Xs;){var b=(s=Xs).child;if(0!==(2064&s.subtreeFlags)&&null!==b)b.return=s,Xs=b;else e:for(s=x;null!==Xs;){if(0!==(2048&(l=Xs).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(k){jc(l,l.return,k)}if(l===s){Xs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Xs=w;break e}Xs=l.return}}if(Pl=a,Ua(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{xt=n,Cl.transition=t}}return!1}function Sc(e,t,n){e=Fi(e,t=hs(0,t=cs(n,t),1),1),t=ec(),null!==e&&(yt(e,1,t),rc(e,t))}function jc(e,t,n){if(3===e.tag)Sc(e,e,n);else for(;null!==t;){if(3===t.tag){Sc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===$l||!$l.has(r))){t=Fi(t,e=ps(t,e=cs(n,e),1),1),e=ec(),null!==t&&(yt(t,1,e),rc(t,e));break}}t=t.return}}function Ec(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Al===e&&(Ll&n)===n&&(4===_l||3===_l&&(130023424&Ll)===Ll&&500>Ye()-zl?fc(e,0):Vl|=n),rc(e,t)}function Nc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=_i(e,t))&&(yt(e,t,n),rc(e,n))}function Cc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Nc(e,n)}function Pc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}null!==r&&r.delete(t),Nc(e,n)}function Ac(e,t){return Qe(e,t)}function Tc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lc(e,t,n,r){return new Tc(e,t,n,r)}function Rc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mc(e,t){var n=e.alternate;return null===n?((n=Lc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function _c(e,t,n,r,a,o){var s=2;if(r=e,"function"===typeof e)Rc(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case S:return Dc(n.children,a,o,t);case j:s=8,a|=8;break;case E:return(e=Lc(12,n,t,2|a)).elementType=E,e.lanes=o,e;case A:return(e=Lc(13,n,t,a)).elementType=A,e.lanes=o,e;case T:return(e=Lc(19,n,t,a)).elementType=T,e.lanes=o,e;case M:return Oc(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case N:s=10;break e;case C:s=9;break e;case P:s=11;break e;case L:s=14;break e;case R:s=16,r=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Lc(s,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Dc(e,t,n,r){return(e=Lc(7,e,r,t)).lanes=n,e}function Oc(e,t,n,r){return(e=Lc(22,e,r,t)).elementType=M,e.lanes=n,e.stateNode={isHidden:!1},e}function Ic(e,t,n){return(e=Lc(6,e,null,t)).lanes=n,e}function Vc(e,t,n){return(t=Lc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Fc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Bc(e,t,n,r,a,i,o,s,l){return e=new Fc(e,t,n,s,l),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Lc(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oi(i),e}function zc(e){if(!e)return Ca;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ra(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Ra(n))return Da(e,n,t)}return t}function Uc(e,t,n,r,a,i,o,s,l){return(e=Bc(n,r,!0,e,0,i,0,s,l)).context=zc(null),n=e.current,(i=Vi(r=ec(),a=tc(n))).callback=void 0!==t&&null!==t?t:null,Fi(n,i,a),e.current.lanes=a,yt(e,a,r),rc(e,r),e}function Wc(e,t,n,r){var a=t.current,i=ec(),o=tc(a);return n=zc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Vi(i,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Fi(a,t,o))&&(nc(e,a,o,i),Bi(e,a,o)),o}function Hc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function $c(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}Sl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Aa.current)xs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return xs=!1,function(e,t,n){switch(t.tag){case 3:As(t),hi();break;case 5:Yi(t);break;case 1:Ra(t.type)&&Oa(t);break;case 4:Zi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Na(ki,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Na(Ji,1&Ji.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Is(e,t,n):(Na(Ji,1&Ji.current),null!==(e=Hs(e,t,n))?e.sibling:null);Na(Ji,1&Ji.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Us(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Na(Ji,Ji.current),r)break;return null;case 22:case 23:return t.lanes=0,js(e,t,n)}return Hs(e,t,n)}(e,t,n);xs=0!==(131072&e.flags)}else xs=!1,ai&&0!==(1048576&t.flags)&&Ja(t,$a,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ws(e,t),e=t.pendingProps;var a=La(t,Pa.current);Ai(t,n),a=go(null,t,r,e,a,n);var o=yo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ra(r)?(o=!0,Oa(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Oi(t),a.updater=as,t.stateNode=a,a._reactInternals=t,ls(t,r,e,n),t=Ps(null,t,r,!0,o,n)):(t.tag=0,ai&&o&&ei(t),bs(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ws(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Rc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===L)return 14}return 2}(r),e=ns(r,e),a){case 0:t=Ns(null,t,r,e,n);break e;case 1:t=Cs(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ns(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ns(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 1:return r=t.type,a=t.pendingProps,Cs(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 3:e:{if(As(t),null===e)throw Error(i(387));r=t.pendingProps,a=(o=t.memoizedState).element,Ii(e,t),Ui(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ts(e,t,r,n,a=cs(Error(i(423)),t));break e}if(r!==a){t=Ts(e,t,r,n,a=cs(Error(i(424)),t));break e}for(ri=ca(t.stateNode.containerInfo.firstChild),ni=t,ai=!0,ii=null,n=wi(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hi(),r===a){t=Hs(e,t,n);break e}bs(e,t,r,n)}t=t.child}return t;case 5:return Yi(t),null===e&&ci(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,s=a.children,na(r,a)?s=null:null!==o&&na(r,o)&&(t.flags|=32),Es(e,t),bs(e,t,s,n),t.child;case 6:return null===e&&ci(t),null;case 13:return Is(e,t,n);case 4:return Zi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=bi(t,null,r,n):bs(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ws(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 7:return bs(e,t,t.pendingProps,n),t.child;case 8:case 12:return bs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,s=a.value,Na(ki,r._currentValue),r._currentValue=s,null!==o)if(sr(o.value,s)){if(o.children===a.children&&!Aa.current){t=Hs(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var l=o.dependencies;if(null!==l){s=o.child;for(var c=l.firstContext;null!==c;){if(c.context===r){if(1===o.tag){(c=Vi(-1,n&-n)).tag=2;var u=o.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}o.lanes|=n,null!==(c=o.alternate)&&(c.lanes|=n),Pi(o.return,n,t),l.lanes|=n;break}c=c.next}}else if(10===o.tag)s=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(s=o.return))throw Error(i(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Pi(s,n,t),s=o.sibling}else s=o.child;if(null!==s)s.return=o;else for(s=o;null!==s;){if(s===t){s=null;break}if(null!==(o=s.sibling)){o.return=s.return,s=o;break}s=s.return}o=s}bs(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ai(t,n),r=r(a=Ti(a)),t.flags|=1,bs(e,t,r,n),t.child;case 14:return a=ns(r=t.type,t.pendingProps),ks(e,t,r,a=ns(r.type,a),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ns(r,a),Ws(e,t),t.tag=1,Ra(r)?(e=!0,Oa(t)):e=!1,Ai(t,n),os(t,r,a),ls(t,r,a,n),Ps(null,t,r,!0,e,n);case 19:return Us(e,t,n);case 22:return js(e,t,n)}throw Error(i(156,t.tag))};var Qc="function"===typeof reportError?reportError:function(e){console.error(e)};function Gc(e){this._internalRoot=e}function Zc(e){this._internalRoot=e}function Kc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xc(){}function Jc(e,t,n,r,a){var i=n._reactRootContainer;if(i){var o=i;if("function"===typeof a){var s=a;a=function(){var e=Hc(o);s.call(e)}}Wc(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var i=r;r=function(){var e=Hc(o);i.call(e)}}var o=Uc(t,r,e,0,null,!1,0,"",Xc);return e._reactRootContainer=o,e[pa]=o.current,Ur(8===e.nodeType?e.parentNode:e),uc(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var s=r;r=function(){var e=Hc(l);s.call(e)}}var l=Bc(e,0,!1,null,0,!1,0,"",Xc);return e._reactRootContainer=l,e[pa]=l.current,Ur(8===e.nodeType?e.parentNode:e),uc(function(){Wc(t,l,n,r)}),l}(n,t,e,a,r);return Hc(o)}Zc.prototype.render=Gc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Wc(e,t,null,null)},Zc.prototype.unmount=Gc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc(function(){Wc(null,e,null,null)}),t[pa]=null}},Zc.prototype.unstable_scheduleHydration=function(e){if(e){var t=jt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Mt.length&&0!==t&&t<Mt[n].priority;n++);Mt.splice(n,0,e),0===n&&It(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),rc(t,Ye()),0===(6&Pl)&&(Ul=Ye()+500,Ua()))}break;case 13:uc(function(){var t=_i(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}}),$c(e,1)}},kt=function(e){if(13===e.tag){var t=_i(e,134217728);if(null!==t)nc(t,e,134217728,ec());$c(e,134217728)}},St=function(e){if(13===e.tag){var t=tc(e),n=_i(e,t);if(null!==n)nc(n,e,t,ec());$c(e,t)}},jt=function(){return xt},Et=function(e,t){var n=xt;try{return xt=e,t()}finally{xt=n}},ke=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(i(90));Q(r),X(r,a)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=cc,Ae=uc;var eu={usingClientEntryPoint:!1,Events:[xa,ba,wa,Ne,Ce,cc]},tu={findFiberByHostInstance:va,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{at=ru.inject(nu),it=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Kc(t))throw Error(i(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Kc(e))throw Error(i(299));var n=!1,r="",a=Qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Bc(e,1,!1,null,0,n,0,r,a),e[pa]=t.current,Ur(8===e.nodeType?e.parentNode:e),new Gc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Yc(t))throw Error(i(200));return Jc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Kc(e))throw Error(i(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",s=Qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Uc(t,null,e,1,null!=n?n:null,a,0,o,s),e[pa]=t.current,Ur(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Zc(t)},t.render=function(e,t,n){if(!Yc(t))throw Error(i(200));return Jc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yc(e))throw Error(i(40));return!!e._reactRootContainer&&(uc(function(){Jc(null,null,e,!1,function(){e._reactRootContainer=null,e[pa]=null})}),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yc(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Jc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var i=Object.create(null);n.r(i);var o={};e=e||[null,t({}),t([]),t(t)];for(var s=2&a&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>o[e]=()=>r[e]);return o.default=()=>r,n.d(i,o),i}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Eu,hasStandardBrowserEnv:()=>Cu,hasStandardBrowserWebWorkerEnv:()=>Pu,navigator:()=>Nu,origin:()=>Au});var a,i=n(43),o=n.t(i,2),s=n(391),l=n(950),c=n.t(l,2);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const d="popstate";function f(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function h(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function p(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?y(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function g(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function y(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v(e,t,n,r){void 0===r&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,s=i.history,l=a.Pop,c=null,h=y();function y(){return(s.state||{idx:null}).idx}function v(){l=a.Pop;let e=y(),t=null==e?null:e-h;h=e,c&&c({action:l,location:b.location,delta:t})}function x(e){let t="null"!==i.location.origin?i.location.origin:i.location.href,n="string"===typeof e?e:g(e);return n=n.replace(/ $/,"%20"),f(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==h&&(h=0,s.replaceState(u({},s.state,{idx:h}),""));let b={get action(){return l},get location(){return e(i,s)},listen(e){if(c)throw new Error("A history only accepts one active listener");return i.addEventListener(d,v),c=e,()=>{i.removeEventListener(d,v),c=null}},createHref:e=>t(i,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l=a.Push;let r=m(b.location,e,t);n&&n(r,e),h=y()+1;let u=p(r,h),d=b.createHref(r);try{s.pushState(u,"",d)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;i.location.assign(d)}o&&c&&c({action:l,location:b.location,delta:1})},replace:function(e,t){l=a.Replace;let r=m(b.location,e,t);n&&n(r,e),h=y();let i=p(r,h),u=b.createHref(r);s.replaceState(i,"",u),o&&c&&c({action:l,location:b.location,delta:0})},go:e=>s.go(e)};return b}var x;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(x||(x={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function b(e,t,n){return void 0===n&&(n="/"),w(e,t,n,!1)}function w(e,t,n,r){let a=D(("string"===typeof t?y(t):t).pathname||"/",n);if(null==a)return null;let i=k(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let o=null;for(let s=0;null==o&&s<i.length;++s){let e=_(a);o=R(i[s],e,r)}return o}function k(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,i)=>{let o={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(f(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let s=B([r,o.relativePath]),l=n.concat(o);e.children&&e.children.length>0&&(f(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),k(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:L(s,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of S(e.path))a(e,t,r);else a(e,t)}),t}function S(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return a?[i,""]:[i];let o=S(r.join("/")),s=[];return s.push(...o.map(e=>""===e?i:[i,e].join("/"))),a&&s.push(...o),s.map(t=>e.startsWith("/")&&""===t?"/":t)}const j=/^:[\w-]+$/,E=3,N=2,C=1,P=10,A=-2,T=e=>"*"===e;function L(e,t){let n=e.split("/"),r=n.length;return n.some(T)&&(r+=A),t&&(r+=N),n.filter(e=>!T(e)).reduce((e,t)=>e+(j.test(t)?E:""===t?C:P),r)}function R(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},i="/",o=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,c="/"===i?t:t.slice(i.length)||"/",u=M({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},c),d=e.route;if(!u&&l&&n&&!r[r.length-1].route.index&&(u=M({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),o.push({params:a,pathname:B([i,u.pathname]),pathnameBase:z(B([i,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(i=B([i,u.pathnameBase]))}return o}function M(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);h("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let i=new RegExp(a,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),s=a.slice(1),l=r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";o=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{});return{params:l,pathname:i,pathnameBase:o,pattern:e}}function _(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return h(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function D(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function O(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function I(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function V(e,t){let n=I(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function F(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=y(e):(a=u({},e),f(!a.pathname||!a.pathname.includes("?"),O("?","pathname","search",a)),f(!a.pathname||!a.pathname.includes("#"),O("#","pathname","hash",a)),f(!a.search||!a.search.includes("#"),O("#","search","hash",a)));let i,o=""===e||""===a.pathname,s=o?"/":a.pathname;if(null==s)i=n;else{let e=t.length-1;if(!r&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}i=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?y(e):e,i=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:i,search:U(r),hash:W(a)}}(a,i),c=s&&"/"!==s&&s.endsWith("/"),d=(o||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const B=e=>e.join("/").replace(/\/\/+/g,"/"),z=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),U=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",W=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function H(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const q=["post","put","patch","delete"],$=(new Set(q),["get",...q]);new Set($),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)}const G=i.createContext(null);const Z=i.createContext(null);const K=i.createContext(null);const Y=i.createContext(null);const X=i.createContext({outlet:null,matches:[],isDataRoute:!1});const J=i.createContext(null);function ee(){return null!=i.useContext(Y)}function te(){return ee()||f(!1),i.useContext(Y).location}function ne(e){i.useContext(K).static||i.useLayoutEffect(e)}function re(){let{isDataRoute:e}=i.useContext(X);return e?function(){let{router:e}=he(de.UseNavigateStable),t=me(fe.UseNavigateStable),n=i.useRef(!1);ne(()=>{n.current=!0});let r=i.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,Q({fromRouteId:t},a)))},[e,t]);return r}():function(){ee()||f(!1);let e=i.useContext(G),{basename:t,future:n,navigator:r}=i.useContext(K),{matches:a}=i.useContext(X),{pathname:o}=te(),s=JSON.stringify(V(a,n.v7_relativeSplatPath)),l=i.useRef(!1);return ne(()=>{l.current=!0}),i.useCallback(function(n,a){if(void 0===a&&(a={}),!l.current)return;if("number"===typeof n)return void r.go(n);let i=F(n,JSON.parse(s),o,"path"===a.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:B([t,i.pathname])),(a.replace?r.replace:r.push)(i,a.state,a)},[t,r,s,o,e])}()}function ae(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=i.useContext(K),{matches:a}=i.useContext(X),{pathname:o}=te(),s=JSON.stringify(V(a,r.v7_relativeSplatPath));return i.useMemo(()=>F(e,JSON.parse(s),o,"path"===n),[e,s,o,n])}function ie(e,t,n,r){ee()||f(!1);let{navigator:o}=i.useContext(K),{matches:s}=i.useContext(X),l=s[s.length-1],c=l?l.params:{},u=(l&&l.pathname,l?l.pathnameBase:"/");l&&l.route;let d,h=te();if(t){var p;let e="string"===typeof t?y(t):t;"/"===u||(null==(p=e.pathname)?void 0:p.startsWith(u))||f(!1),d=e}else d=h;let m=d.pathname||"/",g=m;if("/"!==u){let e=u.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let v=b(e,{pathname:g});let x=ue(v&&v.map(e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:B([u,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:B([u,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),s,n,r);return t&&x?i.createElement(Y.Provider,{value:{location:Q({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:a.Pop}},x):x}function oe(){let e=function(){var e;let t=i.useContext(J),n=pe(fe.UseRouteError),r=me(fe.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=H(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r};return i.createElement(i.Fragment,null,i.createElement("h2",null,"Unexpected Application Error!"),i.createElement("h3",{style:{fontStyle:"italic"}},t),n?i.createElement("pre",{style:a},n):null,null)}const se=i.createElement(oe,null);class le extends i.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?i.createElement(X.Provider,{value:this.props.routeContext},i.createElement(J.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ce(e){let{routeContext:t,match:n,children:r}=e,a=i.useContext(G);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),i.createElement(X.Provider,{value:t},r)}function ue(e,t,n,r){var a;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,l=null==(a=n)?void 0:a.errors;if(null!=l){let e=s.findIndex(e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id]));e>=0||f(!1),s=s.slice(0,Math.min(s.length,e+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let i=0;i<s.length;i++){let e=s[i];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=i),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){c=!0,s=u>=0?s.slice(0,u+1):[s[0]];break}}}return s.reduceRight((e,r,a)=>{let o,d=!1,f=null,h=null;var p;n&&(o=l&&r.route.id?l[r.route.id]:void 0,f=r.route.errorElement||se,c&&(u<0&&0===a?(p="route-fallback",!1||ge[p]||(ge[p]=!0),d=!0,h=null):u===a&&(d=!0,h=r.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,a+1)),g=()=>{let t;return t=o?f:d?h:r.route.Component?i.createElement(r.route.Component,null):r.route.element?r.route.element:e,i.createElement(ce,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?i.createElement(le,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var de=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(de||{}),fe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(fe||{});function he(e){let t=i.useContext(G);return t||f(!1),t}function pe(e){let t=i.useContext(Z);return t||f(!1),t}function me(e){let t=function(){let e=i.useContext(X);return e||f(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||f(!1),n.route.id}const ge={};function ye(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}o.startTransition;function ve(e){f(!1)}function xe(e){let{basename:t="/",children:n=null,location:r,navigationType:o=a.Pop,navigator:s,static:l=!1,future:c}=e;ee()&&f(!1);let u=t.replace(/^\/*/,"/"),d=i.useMemo(()=>({basename:u,navigator:s,static:l,future:Q({v7_relativeSplatPath:!1},c)}),[u,c,s,l]);"string"===typeof r&&(r=y(r));let{pathname:h="/",search:p="",hash:m="",state:g=null,key:v="default"}=r,x=i.useMemo(()=>{let e=D(h,u);return null==e?null:{location:{pathname:e,search:p,hash:m,state:g,key:v},navigationType:o}},[u,h,p,m,g,v,o]);return null==x?null:i.createElement(K.Provider,{value:d},i.createElement(Y.Provider,{children:n,value:x}))}function be(e){let{children:t,location:n}=e;return ie(we(t),n)}new Promise(()=>{});i.Component;function we(e,t){void 0===t&&(t=[]);let n=[];return i.Children.forEach(e,(e,r)=>{if(!i.isValidElement(e))return;let a=[...t,r];if(e.type===i.Fragment)return void n.push.apply(n,we(e.props.children,a));e.type!==ve&&f(!1),e.props.index&&e.props.children&&f(!1);let o={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=we(e.props.children,a)),n.push(o)}),n}function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ke.apply(this,arguments)}function Se(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const je=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Ap){}new Map;const Ee=o.startTransition;c.flushSync,o.useId;function Ne(e){let{basename:t,children:n,future:r,window:a}=e,o=i.useRef();var s;null==o.current&&(o.current=(void 0===(s={window:a,v5Compat:!0})&&(s={}),v(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return m("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:g(t)},null,s)));let l=o.current,[c,u]=i.useState({action:l.action,location:l.location}),{v7_startTransition:d}=r||{},f=i.useCallback(e=>{d&&Ee?Ee(()=>u(e)):u(e)},[u,d]);return i.useLayoutEffect(()=>l.listen(f),[l,f]),i.useEffect(()=>ye(r),[r]),i.createElement(xe,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:l,future:r})}const Ce="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Pe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ae=i.forwardRef(function(e,t){let n,{onClick:r,relative:a,reloadDocument:o,replace:s,state:l,target:c,to:u,preventScrollReset:d,viewTransition:h}=e,p=Se(e,je),{basename:m}=i.useContext(K),y=!1;if("string"===typeof u&&Pe.test(u)&&(n=u,Ce))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=D(t.pathname,m);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:y=!0}catch(Ap){}let v=function(e,t){let{relative:n}=void 0===t?{}:t;ee()||f(!1);let{basename:r,navigator:a}=i.useContext(K),{hash:o,pathname:s,search:l}=ae(e,{relative:n}),c=s;return"/"!==r&&(c="/"===s?r:B([r,s])),a.createHref({pathname:c,search:l,hash:o})}(u,{relative:a}),x=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:o,relative:s,viewTransition:l}=void 0===t?{}:t,c=re(),u=te(),d=ae(e,{relative:s});return i.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:g(u)===g(d);c(e,{replace:n,state:a,preventScrollReset:o,relative:s,viewTransition:l})}},[u,c,d,r,a,n,e,o,s,l])}(u,{replace:s,state:l,target:c,preventScrollReset:d,relative:a,viewTransition:h});return i.createElement("a",ke({},p,{href:n||v,onClick:y||o?r:function(e){r&&r(e),e.defaultPrevented||x(e)},ref:t,target:c}))});var Te,Le;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Te||(Te={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Le||(Le={}));function Re(e){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Re(e)}function Me(e){var t=function(e,t){if("object"!=Re(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Re(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Re(t)?t:t+""}function _e(e,t,n){return(t=Me(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?De(Object(n),!0).forEach(function(t){_e(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):De(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const Ie=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ve=(0,i.createContext)({}),Fe=(0,i.createContext)(null),Be="undefined"!==typeof document,ze=Be?i.useLayoutEffect:i.useEffect,Ue=(0,i.createContext)({strict:!1}),We=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),He="data-"+We("framerAppearId");function qe(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function $e(e){return"string"===typeof e||Array.isArray(e)}function Qe(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}const Ge=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ze=["initial",...Ge];function Ke(e){return Qe(e.animate)||Ze.some(t=>$e(e[t]))}function Ye(e){return Boolean(Ke(e)||e.variants)}function Xe(e){const{initial:t,animate:n}=function(e,t){if(Ke(e)){const{initial:t,animate:n}=e;return{initial:!1===t||$e(t)?t:void 0,animate:$e(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(Ve));return(0,i.useMemo)(()=>({initial:t,animate:n}),[Je(t),Je(n)])}function Je(e){return Array.isArray(e)?e.join(" "):e}const et={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},tt={};for(const Lp in et)tt[Lp]={isEnabled:e=>et[Lp].some(t=>!!e[t])};const nt=(0,i.createContext)({}),rt=(0,i.createContext)({}),at=Symbol.for("motionComponentSymbol");function it(e){let{preloadedFeatures:t,createVisualElement:n,useRender:r,useVisualState:a,Component:o}=e;t&&function(e){for(const t in e)tt[t]=Oe(Oe({},tt[t]),e[t])}(t);const s=(0,i.forwardRef)(function(e,s){let l;const c=Oe(Oe(Oe({},(0,i.useContext)(Ie)),e),{},{layoutId:ot(e)}),{isStatic:u}=c,d=Xe(e),f=a(e,u);if(!u&&Be){d.visualElement=function(e,t,n,r){const{visualElement:a}=(0,i.useContext)(Ve),o=(0,i.useContext)(Ue),s=(0,i.useContext)(Fe),l=(0,i.useContext)(Ie).reducedMotion,c=(0,i.useRef)();r=r||o.renderer,!c.current&&r&&(c.current=r(e,{visualState:t,parent:a,props:n,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));const u=c.current;(0,i.useInsertionEffect)(()=>{u&&u.update(n,s)});const d=(0,i.useRef)(Boolean(n[He]&&!window.HandoffComplete));return ze(()=>{u&&(u.render(),d.current&&u.animationState&&u.animationState.animateChanges())}),(0,i.useEffect)(()=>{u&&(u.updateFeatures(),!d.current&&u.animationState&&u.animationState.animateChanges(),d.current&&(d.current=!1,window.HandoffComplete=!0))}),u}(o,f,c,n);const e=(0,i.useContext)(rt),r=(0,i.useContext)(Ue).strict;d.visualElement&&(l=d.visualElement.loadFeatures(c,r,t,e))}return i.createElement(Ve.Provider,{value:d},l&&d.visualElement?i.createElement(l,Oe({visualElement:d.visualElement},c)):null,r(o,e,function(e,t,n){return(0,i.useCallback)(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):qe(n)&&(n.current=r))},[t])}(f,d.visualElement,s),f,u,d.visualElement))});return s[at]=o,s}function ot(e){let{layoutId:t}=e;const n=(0,i.useContext)(nt).id;return n&&void 0!==t?n+"-"+t:t}function st(e){function t(t){return it(e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}if("undefined"===typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const lt=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ct(e){return"string"===typeof e&&!e.includes("-")&&!!(lt.indexOf(e)>-1||/[A-Z]/.test(e))}const ut={};const dt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ft=new Set(dt);function ht(e,t){let{layout:n,layoutId:r}=t;return ft.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!ut[e]||"opacity"===e)}const pt=e=>Boolean(e&&e.getVelocity),mt={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},gt=dt.length;const yt=e=>t=>"string"===typeof t&&t.startsWith(e),vt=yt("--"),xt=yt("var(--"),bt=(e,t)=>t&&"number"===typeof e?t.transform(e):e,wt=(e,t,n)=>Math.min(Math.max(n,e),t),kt={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},St=Oe(Oe({},kt),{},{transform:e=>wt(0,1,e)}),jt=Oe(Oe({},kt),{},{default:1}),Et=e=>Math.round(1e5*e)/1e5,Nt=/(-)?([\d]*\.?[\d])+/g,Ct=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Pt=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function At(e){return"string"===typeof e}const Tt=e=>({test:t=>At(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),Lt=Tt("deg"),Rt=Tt("%"),Mt=Tt("px"),_t=Tt("vh"),Dt=Tt("vw"),Ot=Oe(Oe({},Rt),{},{parse:e=>Rt.parse(e)/100,transform:e=>Rt.transform(100*e)}),It=Oe(Oe({},kt),{},{transform:Math.round}),Vt={borderWidth:Mt,borderTopWidth:Mt,borderRightWidth:Mt,borderBottomWidth:Mt,borderLeftWidth:Mt,borderRadius:Mt,radius:Mt,borderTopLeftRadius:Mt,borderTopRightRadius:Mt,borderBottomRightRadius:Mt,borderBottomLeftRadius:Mt,width:Mt,maxWidth:Mt,height:Mt,maxHeight:Mt,size:Mt,top:Mt,right:Mt,bottom:Mt,left:Mt,padding:Mt,paddingTop:Mt,paddingRight:Mt,paddingBottom:Mt,paddingLeft:Mt,margin:Mt,marginTop:Mt,marginRight:Mt,marginBottom:Mt,marginLeft:Mt,rotate:Lt,rotateX:Lt,rotateY:Lt,rotateZ:Lt,scale:jt,scaleX:jt,scaleY:jt,scaleZ:jt,skew:Lt,skewX:Lt,skewY:Lt,distance:Mt,translateX:Mt,translateY:Mt,translateZ:Mt,x:Mt,y:Mt,z:Mt,perspective:Mt,transformPerspective:Mt,opacity:St,originX:Ot,originY:Ot,originZ:Mt,zIndex:It,fillOpacity:St,strokeOpacity:St,numOctaves:It};function Ft(e,t,n,r){const{style:a,vars:i,transform:o,transformOrigin:s}=e;let l=!1,c=!1,u=!0;for(const d in t){const e=t[d];if(vt(d)){i[d]=e;continue}const n=Vt[d],r=bt(e,n);if(ft.has(d)){if(l=!0,o[d]=r,!u)continue;e!==(n.default||0)&&(u=!1)}else d.startsWith("origin")?(c=!0,s[d]=r):a[d]=r}if(t.transform||(l||r?a.transform=function(e,t,n,r){let{enableHardwareAcceleration:a=!0,allowTransformNone:i=!0}=t,o="";for(let s=0;s<gt;s++){const t=dt[s];void 0!==e[t]&&(o+="".concat(mt[t]||t,"(").concat(e[t],") "))}return a&&!e.z&&(o+="translateZ(0)"),o=o.trim(),r?o=r(e,n?"":o):i&&n&&(o="none"),o}(e.transform,n,u,r):a.transform&&(a.transform="none")),c){const{originX:e="50%",originY:t="50%",originZ:n=0}=s;a.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}const Bt=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function zt(e,t,n){for(const r in t)pt(t[r])||ht(r,n)||(e[r]=t[r])}function Ut(e,t,n){const r={};return zt(r,e.style||{},e),Object.assign(r,function(e,t,n){let{transformTemplate:r}=e;return(0,i.useMemo)(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Ft(e,t,{enableHardwareAcceleration:!n},r),Object.assign({},e.vars,e.style)},[t])}(e,t,n)),e.transformValues?e.transformValues(r):r}function Wt(e,t,n){const r={},a=Ut(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,a.userSelect=a.WebkitUserSelect=a.WebkitTouchCallout="none",a.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=a,r}const Ht=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function qt(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Ht.has(e)}let $t=e=>!qt(e);try{(Qt=require("@emotion/is-prop-valid").default)&&($t=e=>e.startsWith("on")?!qt(e):Qt(e))}catch(Tp){}var Qt;function Gt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Zt(e,t,n){return"string"===typeof e?e:Mt.transform(t+n*e)}const Kt={offset:"stroke-dashoffset",array:"stroke-dasharray"},Yt={offset:"strokeDashoffset",array:"strokeDasharray"};const Xt=["attrX","attrY","attrScale","originX","originY","pathLength","pathSpacing","pathOffset"];function Jt(e,t,n,r,a){let{attrX:i,attrY:o,attrScale:s,originX:l,originY:c,pathLength:u,pathSpacing:d=1,pathOffset:f=0}=t;if(Ft(e,Gt(t,Xt),n,a),r)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:h,style:p,dimensions:m}=e;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==l||void 0!==c||p.transform)&&(p.transformOrigin=function(e,t,n){const r=Zt(t,e.x,e.width),a=Zt(n,e.y,e.height);return"".concat(r," ").concat(a)}(m,void 0!==l?l:.5,void 0!==c?c:.5)),void 0!==i&&(h.x=i),void 0!==o&&(h.y=o),void 0!==s&&(h.scale=s),void 0!==u&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const i=a?Kt:Yt;e[i.offset]=Mt.transform(-r);const o=Mt.transform(t),s=Mt.transform(n);e[i.array]="".concat(o," ").concat(s)}(h,u,d,f,!1)}const en=()=>Oe(Oe({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}}),tn=e=>"string"===typeof e&&"svg"===e.toLowerCase();function nn(e,t,n,r){const a=(0,i.useMemo)(()=>{const n=en();return Jt(n,t,{enableHardwareAcceleration:!1},tn(r),e.transformTemplate),Oe(Oe({},n.attrs),{},{style:Oe({},n.style)})},[t]);if(e.style){const t={};zt(t,e.style,e),a.style=Oe(Oe({},t),a.style)}return a}function rn(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,r,a,o)=>{let{latestValues:s}=a;const l=(ct(t)?nn:Wt)(n,s,o,t),c=function(e,t,n){const r={};for(const a in e)"values"===a&&"object"===typeof e.values||($t(a)||!0===n&&qt(a)||!t&&!qt(a)||e.draggable&&a.startsWith("onDrag"))&&(r[a]=e[a]);return r}(n,"string"===typeof t,e),u=Oe(Oe(Oe({},c),l),{},{ref:r}),{children:d}=n,f=(0,i.useMemo)(()=>pt(d)?d.get():d,[d]);return(0,i.createElement)(t,Oe(Oe({},u),{},{children:f}))}}function an(e,t,n,r){let{style:a,vars:i}=t;Object.assign(e.style,a,r&&r.getProjectionStyles(n));for(const o in i)e.style.setProperty(o,i[o])}const on=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function sn(e,t,n,r){an(e,t,void 0,r);for(const a in t.attrs)e.setAttribute(on.has(a)?a:We(a),t.attrs[a])}function ln(e,t){const{style:n}=e,r={};for(const a in n)(pt(n[a])||t.style&&pt(t.style[a])||ht(a,e))&&(r[a]=n[a]);return r}function cn(e,t){const n=ln(e,t);for(const r in e)if(pt(e[r])||pt(t[r])){n[-1!==dt.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]}return n}function un(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,a)),"string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,a)),t}function dn(e){const t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}const fn=e=>Array.isArray(e),hn=e=>fn(e)?e[e.length-1]||0:e;function pn(e){const t=pt(e)?e.get():e;return n=t,Boolean(n&&"object"===typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const mn=["transitionEnd","transition"];const gn=e=>(t,n)=>{const r=(0,i.useContext)(Ve),a=(0,i.useContext)(Fe),o=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:a,createRenderState:i,onMount:o}=e;const s={latestValues:yn(t,n,r,a),renderState:i()};return o&&(s.mount=e=>o(t,e,s)),s}(e,t,r,a);return n?o():dn(o)};function yn(e,t,n,r){const a={},i=r(e,{});for(const f in i)a[f]=pn(i[f]);let{initial:o,animate:s}=e;const l=Ke(e),c=Ye(e);t&&c&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===s&&(s=t.animate));let u=!!n&&!1===n.initial;u=u||!1===o;const d=u?s:o;if(d&&"boolean"!==typeof d&&!Qe(d)){(Array.isArray(d)?d:[d]).forEach(t=>{const n=un(e,t);if(!n)return;const{transitionEnd:r,transition:i}=n,o=Gt(n,mn);for(const e in o){let t=o[e];if(Array.isArray(t)){t=t[u?t.length-1:0]}null!==t&&(a[e]=t)}for(const e in r)a[e]=r[e]})}return a}const vn=e=>e;class xn{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}const bn=["prepare","read","update","preRender","render","postRender"];const{schedule:wn,cancel:kn,state:Sn,steps:jn}=function(e,t){let n=!1,r=!0;const a={delta:0,timestamp:0,isProcessing:!1},i=bn.reduce((e,t)=>(e[t]=function(e){let t=new xn,n=new xn,r=0,a=!1,i=!1;const o=new WeakSet,s={schedule:function(e){const i=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&a,s=i?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&o.add(e),s.add(e)&&i&&a&&(r=t.order.length),e},cancel:e=>{n.remove(e),o.delete(e)},process:l=>{if(a)i=!0;else{if(a=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let n=0;n<r;n++){const r=t.order[n];r(l),o.has(r)&&(s.schedule(r),e())}a=!1,i&&(i=!1,s.process(l))}}};return s}(()=>n=!0),e),{}),o=e=>i[e].process(a),s=()=>{const i=performance.now();n=!1,a.delta=r?1e3/60:Math.max(Math.min(i-a.timestamp,40),1),a.timestamp=i,a.isProcessing=!0,bn.forEach(o),a.isProcessing=!1,n&&t&&(r=!1,e(s))},l=bn.reduce((t,o)=>{const l=i[o];return t[o]=function(t){let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,a.isProcessing||e(s)),l.schedule(t,i,o)},t},{});return{schedule:l,cancel:e=>bn.forEach(t=>i[t].cancel(e)),state:a,steps:i}}("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:vn,!0),En={useVisualState:gn({scrapeMotionValuesFromProps:cn,createRenderState:en,onMount:(e,t,n)=>{let{renderState:r,latestValues:a}=n;wn.read(()=>{try{r.dimensions="function"===typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(Ap){r.dimensions={x:0,y:0,width:0,height:0}}}),wn.render(()=>{Jt(r,a,{enableHardwareAcceleration:!1},tn(t.tagName),e.transformTemplate),sn(t,r)})}})},Nn={useVisualState:gn({scrapeMotionValuesFromProps:ln,createRenderState:Bt})};function Cn(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Pn=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function An(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function Tn(e,t,n,r){return Cn(e,t,(e=>t=>Pn(t)&&e(t,An(t)))(n),r)}const Ln=(e,t)=>n=>t(e(n)),Rn=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(Ln)};function Mn(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const _n=Mn("dragHorizontal"),Dn=Mn("dragVertical");function On(e){let t=!1;if("y"===e)t=Dn();else if("x"===e)t=_n();else{const e=_n(),n=Dn();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function In(){const e=On(!0);return!e||(e(),!1)}class Vn{constructor(e){this.isMounted=!1,this.node=e}update(){}}function Fn(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End");return Tn(e.current,n,(n,a)=>{if("touch"===n.pointerType||In())return;const i=e.getProps();e.animationState&&i.whileHover&&e.animationState.setActive("whileHover",t),i[r]&&wn.update(()=>i[r](n,a))},{passive:!e.getProps()[r]})}const Bn=(e,t)=>!!t&&(e===t||Bn(e,t.parentElement));function zn(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,An(n))}const Un=["root"],Wn=new WeakMap,Hn=new WeakMap,qn=e=>{const t=Wn.get(e.target);t&&t(e)},$n=e=>{e.forEach(qn)};function Qn(e,t,n){const r=function(e){let{root:t}=e,n=Gt(e,Un);const r=t||document;Hn.has(r)||Hn.set(r,{});const a=Hn.get(r),i=JSON.stringify(n);return a[i]||(a[i]=new IntersectionObserver($n,Oe({root:t},n))),a[i]}(t);return Wn.set(e,n),r.observe(e),()=>{Wn.delete(e),r.unobserve(e)}}const Gn={some:0,all:1};const Zn={inView:{Feature:class extends Vn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:a}=e,i={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:Gn[r]};return Qn(this.node.current,i,e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,a&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),i=t?n:r;i&&i(e)})}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Vn{constructor(){super(...arguments),this.removeStartListeners=vn,this.removeEndListeners=vn,this.removeAccessibleListeners=vn,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),r=Tn(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:r,globalTapTarget:a}=this.node.getProps();wn.update(()=>{a||Bn(this.node.current,e.target)?n&&n(e,t):r&&r(e,t)})},{passive:!(n.onTap||n.onPointerUp)}),a=Tn(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=Rn(r,a),this.startPress(e,t)},this.startAccessiblePress=()=>{const e=Cn(this.node.current,"keydown",e=>{if("Enter"!==e.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=Cn(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&zn("up",(e,t)=>{const{onTap:n}=this.node.getProps();n&&wn.update(()=>n(e,t))})}),zn("down",(e,t)=>{this.startPress(e,t)})}),t=Cn(this.node.current,"blur",()=>{this.isPressing&&zn("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=Rn(e,t)}}startPress(e,t){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&wn.update(()=>n(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!In()}cancelPress(e,t){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&wn.update(()=>n(e,t))}mount(){const e=this.node.getProps(),t=Tn(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=Cn(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Rn(t,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends Vn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(Ap){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Rn(Cn(this.node.current,"focus",()=>this.onFocus()),Cn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Vn{mount(){this.unmount=Rn(Fn(this.node,!0),Fn(this.node,!1))}unmount(){}}}};function Kn(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Yn(e,t,n){const r=e.getProps();return un(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.get()),t}(e),function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.getVelocity()),t}(e))}let Xn=vn,Jn=vn;const er=e=>1e3*e,tr=e=>e/1e3,nr=!1,rr=e=>Array.isArray(e)&&"number"===typeof e[0];function ar(e){return Boolean(!e||"string"===typeof e&&or[e]||rr(e)||Array.isArray(e)&&e.every(ar))}const ir=e=>{let[t,n,r,a]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(a,")")},or={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ir([0,.65,.55,1]),circOut:ir([.55,0,1,.45]),backIn:ir([.31,.01,.66,-.59]),backOut:ir([.33,1.53,.69,.99])};function sr(e){if(e)return rr(e)?ir(e):Array.isArray(e)?e.map(sr):or[e]}const lr=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function cr(e,t,n,r){if(e===t&&n===r)return vn;const a=t=>function(e,t,n,r,a){let i,o,s=0;do{o=t+(n-t)/2,i=lr(o,r,a)-e,i>0?n=o:t=o}while(Math.abs(i)>1e-7&&++s<12);return o}(t,0,1,e,n);return e=>0===e||1===e?e:lr(a(e),t,r)}const ur=cr(.42,0,1,1),dr=cr(0,0,.58,1),fr=cr(.42,0,.58,1),hr=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,pr=e=>t=>1-e(1-t),mr=e=>1-Math.sin(Math.acos(e)),gr=pr(mr),yr=hr(mr),vr=cr(.33,1.53,.69,.99),xr=pr(vr),br=hr(xr),wr={linear:vn,easeIn:ur,easeInOut:fr,easeOut:dr,circIn:mr,circInOut:yr,circOut:gr,backIn:xr,backInOut:br,backOut:vr,anticipate:e=>(e*=2)<1?.5*xr(e):.5*(2-Math.pow(2,-10*(e-1)))},kr=e=>{if(Array.isArray(e)){Jn(4===e.length,"Cubic bezier arrays must contain four numerical values.");const[t,n,r,a]=e;return cr(t,n,r,a)}return"string"===typeof e?(Jn(void 0!==wr[e],"Invalid easing type '".concat(e,"'")),wr[e]):e},Sr=(e,t)=>n=>Boolean(At(n)&&Pt.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),jr=(e,t,n)=>r=>{if(!At(r))return r;const[a,i,o,s]=r.match(Nt);return{[e]:parseFloat(a),[t]:parseFloat(i),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},Er=Oe(Oe({},kt),{},{transform:e=>Math.round((e=>wt(0,255,e))(e))}),Nr={test:Sr("rgb","red"),parse:jr("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:a=1}=e;return"rgba("+Er.transform(t)+", "+Er.transform(n)+", "+Er.transform(r)+", "+Et(St.transform(a))+")"}};const Cr={test:Sr("#"),parse:function(e){let t="",n="",r="",a="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),a=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),a=e.substring(4,5),t+=t,n+=n,r+=r,a+=a),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:a?parseInt(a,16)/255:1}},transform:Nr.transform},Pr={test:Sr("hsl","hue"),parse:jr("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:a=1}=e;return"hsla("+Math.round(t)+", "+Rt.transform(Et(n))+", "+Rt.transform(Et(r))+", "+Et(St.transform(a))+")"}},Ar={test:e=>Nr.test(e)||Cr.test(e)||Pr.test(e),parse:e=>Nr.test(e)?Nr.parse(e):Pr.test(e)?Pr.parse(e):Cr.parse(e),transform:e=>At(e)?e:e.hasOwnProperty("red")?Nr.transform(e):Pr.transform(e)},Tr=(e,t,n)=>-n*e+n*t+e;function Lr(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const Rr=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Mr=[Cr,Nr,Pr];function _r(e){const t=(n=e,Mr.find(e=>e.test(n)));var n;Jn(Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."));let r=t.parse(e);return t===Pr&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:a}=e;t/=360,n/=100,r/=100;let i=0,o=0,s=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,a=2*r-e;i=Lr(a,e,t+1/3),o=Lr(a,e,t),s=Lr(a,e,t-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:a}}(r)),r}const Dr=(e,t)=>{const n=_r(e),r=_r(t),a=Oe({},n);return e=>(a.red=Rr(n.red,r.red,e),a.green=Rr(n.green,r.green,e),a.blue=Rr(n.blue,r.blue,e),a.alpha=Tr(n.alpha,r.alpha,e),Nr.transform(a))};const Or={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:vn},Ir={regex:Ct,countKey:"Colors",token:"${c}",parse:Ar.parse},Vr={regex:Nt,countKey:"Numbers",token:"${n}",parse:kt.parse};function Fr(e,t){let{regex:n,countKey:r,token:a,parse:i}=t;const o=e.tokenised.match(n);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(n,a),e.values.push(...o.map(i)))}function Br(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Fr(n,Or),Fr(n,Ir),Fr(n,Vr),n}function zr(e){return Br(e).values}function Ur(e){const{values:t,numColors:n,numVars:r,tokenised:a}=Br(e),i=t.length;return e=>{let t=a;for(let a=0;a<i;a++)t=a<r?t.replace(Or.token,e[a]):a<r+n?t.replace(Ir.token,Ar.transform(e[a])):t.replace(Vr.token,Et(e[a]));return t}}const Wr=e=>"number"===typeof e?0:e;const Hr={test:function(e){var t,n;return isNaN(e)&&At(e)&&((null===(t=e.match(Nt))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(Ct))||void 0===n?void 0:n.length)||0)>0},parse:zr,createTransformer:Ur,getAnimatableNone:function(e){const t=zr(e);return Ur(e)(t.map(Wr))}},qr=(e,t)=>n=>"".concat(n>0?t:e);function $r(e,t){return"number"===typeof e?n=>Tr(e,t,n):Ar.test(e)?Dr(e,t):e.startsWith("var(")?qr(e,t):Zr(e,t)}const Qr=(e,t)=>{const n=[...e],r=n.length,a=e.map((e,n)=>$r(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=a[t](e);return n}},Gr=(e,t)=>{const n=Oe(Oe({},e),t),r={};for(const a in n)void 0!==e[a]&&void 0!==t[a]&&(r[a]=$r(e[a],t[a]));return e=>{for(const t in r)n[t]=r[t](e);return n}},Zr=(e,t)=>{const n=Hr.createTransformer(t),r=Br(e),a=Br(t);return r.numVars===a.numVars&&r.numColors===a.numColors&&r.numNumbers>=a.numNumbers?Rn(Qr(r.values,a.values),n):(Xn(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.")),qr(e,t))},Kr=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},Yr=(e,t)=>n=>Tr(e,t,n);function Xr(e,t,n){const r=[],a=n||("number"===typeof(i=e[0])?Yr:"string"===typeof i?Ar.test(i)?Dr:Zr:Array.isArray(i)?Qr:"object"===typeof i?Gr:Yr);var i;const o=e.length-1;for(let s=0;s<o;s++){let n=a(e[s],e[s+1]);if(t){const e=Array.isArray(t)?t[s]||vn:t;n=Rn(e,n)}r.push(n)}return r}function Jr(e,t){let{clamp:n=!0,ease:r,mixer:a}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=e.length;if(Jn(i===t.length,"Both input and output ranges must be the same length"),1===i)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=Xr(t,r,a),s=o.length,l=t=>{let n=0;if(s>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=Kr(e[n],e[n+1],t);return o[n](r)};return n?t=>l(wt(e[0],e[i-1],t)):l}function ea(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const a=Kr(0,t,r);e.push(Tr(n,1,a))}}(t,e.length-1),t}function ta(e){let{duration:t=300,keyframes:n,times:r,ease:a="easeInOut"}=e;const i=(e=>Array.isArray(e)&&"number"!==typeof e[0])(a)?a.map(kr):kr(a),o={done:!1,value:n[0]},s=function(e,t){return e.map(e=>e*t)}(r&&r.length===n.length?r:ea(n),t),l=Jr(s,n,{ease:Array.isArray(i)?i:(c=n,u=i,c.map(()=>u||fr).splice(0,c.length-1))});var c,u;return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}function na(e,t){return t?e*(1e3/t):0}function ra(e,t,n){const r=Math.max(t-5,0);return na(n-e(r),t-r)}const aa=.001;function ia(e){let t,n,{duration:r=800,bounce:a=.25,velocity:i=0,mass:o=1}=e;Xn(r<=er(10),"Spring duration must be 10 seconds or less");let s=1-a;s=wt(.05,1,s),r=wt(.01,10,tr(r)),s<1?(t=e=>{const t=e*s,n=t*r,a=t-i,o=sa(e,s),l=Math.exp(-n);return aa-a/o*l},n=e=>{const n=e*s*r,a=n*i+i,o=Math.pow(s,2)*Math.pow(e,2)*r,l=Math.exp(-n),c=sa(Math.pow(e,2),s);return(-t(e)+aa>0?-1:1)*((a-o)*l)/c}):(t=e=>Math.exp(-e*r)*((e-i)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(i-e)));const l=function(e,t,n){let r=n;for(let a=1;a<oa;a++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=er(r),isNaN(l))return{stiffness:100,damping:10,duration:r};{const e=Math.pow(l,2)*o;return{stiffness:e,damping:2*s*Math.sqrt(o*e),duration:r}}}const oa=12;function sa(e,t){return e*Math.sqrt(1-t*t)}const la=["keyframes","restDelta","restSpeed"],ca=["duration","bounce"],ua=["stiffness","damping","mass"];function da(e,t){return t.some(t=>void 0!==e[t])}function fa(e){let{keyframes:t,restDelta:n,restSpeed:r}=e,a=Gt(e,la);const i=t[0],o=t[t.length-1],s={done:!1,value:i},{stiffness:l,damping:c,mass:u,duration:d,velocity:f,isResolvedFromDuration:h}=function(e){let t=Oe({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},e);if(!da(e,ua)&&da(e,ca)){const n=ia(e);t=Oe(Oe(Oe({},t),n),{},{mass:1}),t.isResolvedFromDuration=!0}return t}(Oe(Oe({},a),{},{velocity:-tr(a.velocity||0)})),p=f||0,m=c/(2*Math.sqrt(l*u)),g=o-i,y=tr(Math.sqrt(l/u)),v=Math.abs(g)<5;let x;if(r||(r=v?.01:2),n||(n=v?.005:.5),m<1){const e=sa(y,m);x=t=>{const n=Math.exp(-m*y*t);return o-n*((p+m*y*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}}else if(1===m)x=e=>o-Math.exp(-y*e)*(g+(p+y*g)*e);else{const e=y*Math.sqrt(m*m-1);x=t=>{const n=Math.exp(-m*y*t),r=Math.min(e*t,300);return o-n*((p+m*y*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}return{calculatedDuration:h&&d||null,next:e=>{const t=x(e);if(h)s.done=e>=d;else{let a=p;0!==e&&(a=m<1?ra(x,e,t):0);const i=Math.abs(a)<=r,l=Math.abs(o-t)<=n;s.done=i&&l}return s.value=s.done?o:t,s}}}function ha(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:a=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:c,restDelta:u=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?c:void 0===c||Math.abs(l-e)<Math.abs(c-e)?l:c;let m=r*n;const g=f+m,y=void 0===s?g:s(g);y!==g&&(m=y-f);const v=e=>-m*Math.exp(-e/a),x=e=>y+v(e),b=e=>{const t=v(e),n=x(e);h.done=Math.abs(t)<=u,h.value=h.done?y:n};let w,k;const S=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==c&&t>c)&&(w=e,k=fa({keyframes:[h.value,p(h.value)],velocity:ra(x,e,h.value),damping:i,stiffness:o,restDelta:u,restSpeed:d}))};return S(0),{calculatedDuration:null,next:e=>{let t=!1;return k||void 0!==w||(t=!0,b(e),S(e)),void 0!==w&&e>w?k.next(e-w):(!t&&b(e),h)}}}const pa=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:()=>wn.update(t,!0),stop:()=>kn(t),now:()=>Sn.isProcessing?Sn.timestamp:performance.now()}};function ma(e){let t=0;let n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}const ga=["autoplay","delay","driver","keyframes","type","repeat","repeatDelay","repeatType","onPlay","onStop","onComplete","onUpdate"],ya={decay:ha,inertia:ha,tween:ta,keyframes:ta,spring:fa};function va(e){let t,n,{autoplay:r=!0,delay:a=0,driver:i=pa,keyframes:o,type:s="keyframes",repeat:l=0,repeatDelay:c=0,repeatType:u="loop",onPlay:d,onStop:f,onComplete:h,onUpdate:p}=e,m=Gt(e,ga),g=1,y=!1;const v=()=>{n=new Promise(e=>{t=e})};let x;v();const b=ya[s]||ta;let w;b!==ta&&"number"!==typeof o[0]&&(w=Jr([0,100],o,{clamp:!1}),o=[0,100]);const k=b(Oe(Oe({},m),{},{keyframes:o}));let S;"mirror"===u&&(S=b(Oe(Oe({},m),{},{keyframes:[...o].reverse(),velocity:-(m.velocity||0)})));let j="idle",E=null,N=null,C=null;null===k.calculatedDuration&&l&&(k.calculatedDuration=ma(k));const{calculatedDuration:P}=k;let A=1/0,T=1/0;null!==P&&(A=P+c,T=A*(l+1)-c);let L=0;const R=e=>{if(null===N)return;g>0&&(N=Math.min(N,e)),g<0&&(N=Math.min(e-T/g,N)),L=null!==E?E:Math.round(e-N)*g;const t=L-a*(g>=0?1:-1),n=g>=0?t<0:t>T;L=Math.max(t,0),"finished"===j&&null===E&&(L=T);let r=L,i=k;if(l){const e=Math.min(L,T)/A;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,l+1);Boolean(t%2)&&("reverse"===u?(n=1-n,c&&(n-=c/A)):"mirror"===u&&(i=S)),r=wt(0,1,n)*A}const s=n?{done:!1,value:o[0]}:i.next(r);w&&(s.value=w(s.value));let{done:d}=s;n||null===P||(d=g>=0?L>=T:L<=0);const f=null===E&&("finished"===j||"running"===j&&d);return p&&p(s.value),f&&D(),s},M=()=>{x&&x.stop(),x=void 0},_=()=>{j="idle",M(),t(),v(),N=C=null},D=()=>{j="finished",h&&h(),M(),t()},O=()=>{if(y)return;x||(x=i(R));const e=x.now();d&&d(),null!==E?N=e-E:N&&"finished"!==j||(N=e),"finished"===j&&v(),C=N,E=null,j="running",x.start()};r&&O();const I={then:(e,t)=>n.then(e,t),get time(){return tr(L)},set time(e){e=er(e),L=e,null===E&&x&&0!==g?N=x.now()-e/g:E=e},get duration(){const e=null===k.calculatedDuration?ma(k):k.calculatedDuration;return tr(e)},get speed(){return g},set speed(e){e!==g&&x&&(g=e,I.time=tr(L))},get state(){return j},play:O,pause:()=>{j="paused",E=L},stop:()=>{y=!0,"idle"!==j&&(j="idle",f&&f(),_())},cancel:()=>{null!==C&&R(C),_()},complete:()=>{j="finished"},sample:e=>(N=0,R(e))};return I}const xa=["onUpdate","onComplete"],ba=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),wa=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function ka(e,t,n){let{onUpdate:r,onComplete:a}=n,i=Gt(n,xa);if(!(ba()&&wa.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type))return!1;let o,s,l=!1,c=!1;const u=()=>{s=new Promise(e=>{o=e})};u();let{keyframes:d,duration:f=300,ease:h,times:p}=i;if(((e,t)=>"spring"===t.type||"backgroundColor"===e||!ar(t.ease))(t,i)){const e=va(Oe(Oe({},i),{},{repeat:0,delay:0}));let t={done:!1,value:d[0]};const n=[];let r=0;for(;!t.done&&r<2e4;)t=e.sample(r),n.push(t.value),r+=10;p=void 0,d=n,f=r-10,h="linear"}const m=function(e,t,n){let{delay:r=0,duration:a,repeat:i=0,repeatType:o="loop",ease:s,times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const c={[t]:n};l&&(c.offset=l);const u=sr(s);return Array.isArray(u)&&(c.easing=u),e.animate(c,{delay:r,duration:a,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:i+1,direction:"reverse"===o?"alternate":"normal"})}(e.owner.current,t,d,Oe(Oe({},i),{},{duration:f,ease:h,times:p})),g=()=>{c=!1,m.cancel()},y=()=>{c=!0,wn.update(g),o(),u()};m.onfinish=()=>{c||(e.set(function(e,t){let{repeat:n,repeatType:r="loop"}=t;return e[n&&"loop"!==r&&n%2===1?0:e.length-1]}(d,i)),a&&a(),y())};return{then:(e,t)=>s.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,vn),get time(){return tr(m.currentTime||0)},set time(e){m.currentTime=er(e)},get speed(){return m.playbackRate},set speed(e){m.playbackRate=e},get duration(){return tr(f)},play:()=>{l||(m.play(),kn(g))},pause:()=>m.pause(),stop:()=>{if(l=!0,"idle"===m.playState)return;const{currentTime:t}=m;if(t){const n=va(Oe(Oe({},i),{},{autoplay:!1}));e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}y()},complete:()=>{c||m.finish()},cancel:y}}const Sa={type:"spring",stiffness:500,damping:25,restSpeed:10},ja={type:"keyframes",duration:.8},Ea={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Na=(e,t)=>{let{keyframes:n}=t;return n.length>2?ja:ft.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:Sa:Ea},Ca=(e,t)=>"zIndex"!==e&&(!("number"!==typeof t&&!Array.isArray(t))||!("string"!==typeof t||!Hr.test(t)&&"0"!==t||t.startsWith("url("))),Pa=new Set(["brightness","contrast","saturate","opacity"]);function Aa(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(Nt)||[];if(!r)return e;const a=n.replace(r,"");let i=Pa.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+a+")"}const Ta=/([a-z-]*)\(.*?\)/g,La=Oe(Oe({},Hr),{},{getAnimatableNone:e=>{const t=e.match(Ta);return t?t.map(Aa).join(" "):e}}),Ra=Oe(Oe({},Vt),{},{color:Ar,backgroundColor:Ar,outlineColor:Ar,fill:Ar,stroke:Ar,borderColor:Ar,borderTopColor:Ar,borderRightColor:Ar,borderBottomColor:Ar,borderLeftColor:Ar,filter:La,WebkitFilter:La}),Ma=e=>Ra[e];function _a(e,t){let n=Ma(e);return n!==La&&(n=Hr),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Da=e=>/^0[^.\s]+$/.test(e);function Oa(e){return"number"===typeof e?0===e:null!==e?"none"===e||"0"===e||Da(e):void 0}const Ia=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];function Va(e,t){return e[t]||e.default||e}const Fa=!1,Ba=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return a=>{const i=Va(r,e)||{},o=i.delay||r.delay||0;let{elapsed:s=0}=r;s-=er(o);const l=function(e,t,n,r){const a=Ca(t,n);let i;i=Array.isArray(n)?[...n]:[null,n];const o=void 0!==r.from?r.from:e.get();let s;const l=[];for(let c=0;c<i.length;c++)null===i[c]&&(i[c]=0===c?o:i[c-1]),Oa(i[c])&&l.push(c),"string"===typeof i[c]&&"none"!==i[c]&&"0"!==i[c]&&(s=i[c]);if(a&&l.length&&s)for(let c=0;c<l.length;c++)i[l[c]]=_a(t,s);return i}(t,e,n,i),c=l[0],u=l[l.length-1],d=Ca(e,c),f=Ca(e,u);Xn(d===f,"You are trying to animate ".concat(e,' from "').concat(c,'" to "').concat(u,'". ').concat(c," is not an animatable value - to enable this animation set ").concat(c," to a value animatable to ").concat(u," via the `style` property."));let h=Oe(Oe({keyframes:l,velocity:t.getVelocity(),ease:"easeOut"},i),{},{delay:-s,onUpdate:e=>{t.set(e),i.onUpdate&&i.onUpdate(e)},onComplete:()=>{a(),i.onComplete&&i.onComplete()}});if(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:a,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:c,elapsed:u}=e,d=Gt(e,Ia);return!!Object.keys(d).length}(i)||(h=Oe(Oe({},h),Na(e,h))),h.duration&&(h.duration=er(h.duration)),h.repeatDelay&&(h.repeatDelay=er(h.repeatDelay)),!d||!f||nr||!1===i.type||Fa)return function(e){let{keyframes:t,delay:n,onUpdate:r,onComplete:a}=e;const i=()=>(r&&r(t[t.length-1]),a&&a(),{time:0,speed:1,duration:0,play:vn,pause:vn,stop:vn,then:e=>(e(),Promise.resolve()),cancel:vn,complete:vn});return n?va({keyframes:[0,1],duration:0,delay:n,onComplete:i}):i()}(nr?Oe(Oe({},h),{},{delay:0}):h);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const n=ka(t,e,h);if(n)return n}return va(h)}};function za(e){return Boolean(pt(e)&&e.add)}const Ua=e=>/^\-?\d*\.?\d+$/.test(e);function Wa(e,t){-1===e.indexOf(t)&&e.push(t)}function Ha(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class qa{constructor(){this.subscriptions=[]}add(e){return Wa(this.subscriptions,e),()=>Ha(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let a=0;a<r;a++){const r=this.subscriptions[a];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const $a={current:void 0};class Qa{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var r;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t.prev=t.current,t.current=e;const{delta:r,timestamp:a}=Sn;t.lastUpdated!==a&&(t.timeDelta=r,t.lastUpdated=a,wn.postRender(t.scheduleVelocityCheck)),t.prev!==t.current&&t.events.change&&t.events.change.notify(t.current),t.events.velocityChange&&t.events.velocityChange.notify(t.getVelocity()),n&&t.events.renderRequest&&t.events.renderRequest.notify(t.current)},this.scheduleVelocityCheck=()=>wn.postRender(this.velocityCheck),this.velocityCheck=e=>{let{timestamp:t}=e;t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(r=this.current,!isNaN(parseFloat(r))),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new qa);const n=this.events[e].add(t);return"change"===e?()=>{n(),wn.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return $a.current&&$a.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?na(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ga(e,t){return new Qa(e,t)}const Za=e=>t=>t.test(e),Ka=[kt,Mt,Rt,Lt,Dt,_t,{test:e=>"auto"===e,parse:e=>e}],Ya=e=>Ka.find(Za(e)),Xa=[...Ka,Ar,Hr],Ja=e=>Xa.find(Za(e)),ei=["transitionEnd","transition"];function ti(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Ga(n))}function ni(e,t){const n=Yn(e,t);let r=n?e.makeTargetAnimatable(n,!1):{},{transitionEnd:a={},transition:i={}}=r,o=Gt(r,ei);o=Oe(Oe({},o),a);for(const s in o){ti(e,s,hn(o[s]))}}function ri(e,t){if(!t)return;return(t[e]||t.default||t).from}const ai=["transition","transitionEnd"];function ii(e,t){let{protectedKeys:n,needsAnimating:r}=e;const a=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,a}function oi(e,t){const n=e.get();if(!Array.isArray(t))return n!==t;for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}function si(e,t){let{delay:n=0,transitionOverride:r,type:a}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=e.makeTargetAnimatable(t),{transition:o=e.getDefaultTransition(),transitionEnd:s}=i,l=Gt(i,ai);const c=e.getValue("willChange");r&&(o=r);const u=[],d=a&&e.animationState&&e.animationState.getState()[a];for(const f in l){const t=e.getValue(f),r=l[f];if(!t||void 0===r||d&&ii(d,f))continue;const a=Oe({delay:n,elapsed:0},Va(o||{},f));if(window.HandoffAppearAnimations){const n=e.getProps()[He];if(n){const e=window.HandoffAppearAnimations(n,f,t,wn);null!==e&&(a.elapsed=e,a.isHandoff=!0)}}let i=!a.isHandoff&&!oi(t,r);if("spring"===a.type&&(t.getVelocity()||a.velocity)&&(i=!1),t.animation&&(i=!1),i)continue;t.start(Ba(f,t,r,e.shouldReduceMotion&&ft.has(f)?{type:!1}:a));const s=t.animation;za(c)&&(c.add(f),s.then(()=>c.remove(f))),u.push(s)}return s&&Promise.all(u).then(()=>{s&&ni(e,s)}),u}function li(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=Yn(e,t,n.custom);let{transition:a=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(a=n.transitionOverride);const i=r?()=>Promise.all(si(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:i=0,staggerChildren:o,staggerDirection:s}=a;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,i=arguments.length>5?arguments[5]:void 0;const o=[],s=(e.variantChildren.size-1)*r,l=1===a?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return s-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(ci).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(li(e,t,Oe(Oe({},i),{},{delay:n+l(r)})).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,i+r,o,s,n)}:()=>Promise.resolve(),{when:s}=a;if(s){const[e,t]="beforeChildren"===s?[i,o]:[o,i];return e().then(()=>t())}return Promise.all([i(),o(n.delay)])}function ci(e,t){return e.sortNodePosition(t)}const ui=["transition","transitionEnd"],di=[...Ge].reverse(),fi=Ge.length;function hi(e){return t=>Promise.all(t.map(t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const a=t.map(t=>li(e,t,r));n=Promise.all(a)}else if("string"===typeof t)n=li(e,t,r);else{const a="function"===typeof t?Yn(e,t,r.custom):t;n=Promise.all(si(e,a,r))}return n.then(()=>e.notify("AnimationComplete",t))}(e,n,r)}))}function pi(e){let t=hi(e);const n={animate:gi(!0),whileInView:gi(),whileHover:gi(),whileTap:gi(),whileDrag:gi(),whileFocus:gi(),exit:gi()};let r=!0;const a=(t,n)=>{const r=Yn(e,n);if(r){const{transition:e,transitionEnd:n}=r,a=Gt(r,ui);t=Oe(Oe(Oe({},t),a),n)}return t};function i(i,o){const s=e.getProps(),l=e.getVariantContext(!0)||{},c=[],u=new Set;let d={},f=1/0;for(let t=0;t<fi;t++){const h=di[t],p=n[h],m=void 0!==s[h]?s[h]:l[h],g=$e(m),y=h===o?p.isActive:null;!1===y&&(f=t);let v=m===l[h]&&m!==s[h]&&g;if(v&&r&&e.manuallyAnimateOnMount&&(v=!1),p.protectedKeys=Oe({},d),!p.isActive&&null===y||!m&&!p.prevProp||Qe(m)||"boolean"===typeof m)continue;let x=mi(p.prevProp,m)||h===o&&p.isActive&&!v&&g||t>f&&g,b=!1;const w=Array.isArray(m)?m:[m];let k=w.reduce(a,{});!1===y&&(k={});const{prevResolvedValues:S={}}=p,j=Oe(Oe({},S),k),E=e=>{x=!0,u.has(e)&&(b=!0,u.delete(e)),p.needsAnimating[e]=!0};for(const e in j){const t=k[e],n=S[e];if(d.hasOwnProperty(e))continue;let r=!1;r=fn(t)&&fn(n)?!Kn(t,n):t!==n,r?void 0!==t?E(e):u.add(e):void 0!==t&&u.has(e)?E(e):p.protectedKeys[e]=!0}p.prevProp=m,p.prevResolvedValues=k,p.isActive&&(d=Oe(Oe({},d),k)),r&&e.blockInitialAnimation&&(x=!1),!x||v&&!b||c.push(...w.map(e=>({animation:e,options:Oe({type:h},i)})))}if(u.size){const t={};u.forEach(n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)}),c.push({animation:t})}let h=Boolean(c.length);return!r||!1!==s.initial&&s.initial!==s.animate||e.manuallyAnimateOnMount||(h=!1),r=!1,h?t(c):Promise.resolve()}return{animateChanges:i,setActive:function(t,r,a){var o;if(n[t].isActive===r)return Promise.resolve();null===(o=e.variantChildren)||void 0===o||o.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;const s=i(a,t);for(const e in n)n[e].protectedKeys={};return s},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function mi(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!Kn(t,e)}function gi(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let yi=0;const vi={animation:{Feature:class extends Vn{constructor(e){super(e),e.animationState||(e.animationState=pi(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),Qe(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends Vn{constructor(){super(...arguments),this.id=yi++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t,custom:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const a=this.node.animationState.setActive("exit",!e,{custom:null!==n&&void 0!==n?n:this.node.getProps().custom});t&&!e&&a.then(()=>t(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}},xi=(e,t)=>Math.abs(e-t);class bi{constructor(e,t){let{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=Si(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=xi(e.x,t.x),r=xi(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:a}=Sn;this.history.push(Oe(Oe({},r),{},{timestamp:a}));const{onStart:i,onMove:o}=this.handlers;t||(i&&i(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=wi(t,this.transformPagePoint),wn.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:a}=this.handlers;if(this.dragSnapToOrigin&&a&&a(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const i=Si("pointercancel"===e.type?this.lastMoveEventInfo:wi(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,i),r&&r(e,i)},!Pn(e))return;this.dragSnapToOrigin=a,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;const i=wi(An(e),this.transformPagePoint),{point:o}=i,{timestamp:s}=Sn;this.history=[Oe(Oe({},o),{},{timestamp:s})];const{onSessionStart:l}=t;l&&l(e,Si(i,this.history)),this.removeListeners=Rn(Tn(this.contextWindow,"pointermove",this.handlePointerMove),Tn(this.contextWindow,"pointerup",this.handlePointerUp),Tn(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),kn(this.updatePoint)}}function wi(e,t){return t?{point:t(e.point)}:e}function ki(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Si(e,t){let{point:n}=e;return{point:n,delta:ki(n,Ei(t)),offset:ki(n,ji(t)),velocity:Ni(t,.1)}}function ji(e){return e[0]}function Ei(e){return e[e.length-1]}function Ni(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const a=Ei(e);for(;n>=0&&(r=e[n],!(a.timestamp-r.timestamp>er(t)));)n--;if(!r)return{x:0,y:0};const i=tr(a.timestamp-r.timestamp);if(0===i)return{x:0,y:0};const o={x:(a.x-r.x)/i,y:(a.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ci(e){return e.max-e.min}function Pi(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(e-t)<=n}function Ai(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=Tr(t.min,t.max,e.origin),e.scale=Ci(n)/Ci(t),(Pi(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=Tr(n.min,n.max,e.origin)-e.originPoint,(Pi(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Ti(e,t,n,r){Ai(e.x,t.x,n.x,r?r.originX:void 0),Ai(e.y,t.y,n.y,r?r.originY:void 0)}function Li(e,t,n){e.min=n.min+t.min,e.max=e.min+Ci(t)}function Ri(e,t,n){e.min=t.min-n.min,e.max=e.min+Ci(t)}function Mi(e,t,n){Ri(e.x,t.x,n.x),Ri(e.y,t.y,n.y)}function _i(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function Di(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const Oi=.35;function Ii(e,t,n){return{min:Vi(e,t),max:Vi(e,n)}}function Vi(e,t){return"number"===typeof e?e:e[t]||0}const Fi=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Bi(e){return[e("x"),e("y")]}function zi(e){let{top:t,left:n,right:r,bottom:a}=e;return{x:{min:n,max:r},y:{min:t,max:a}}}function Ui(e){return void 0===e||1===e}function Wi(e){let{scale:t,scaleX:n,scaleY:r}=e;return!Ui(t)||!Ui(n)||!Ui(r)}function Hi(e){return Wi(e)||qi(e)||e.z||e.rotate||e.rotateX||e.rotateY}function qi(e){return $i(e.x)||$i(e.y)}function $i(e){return e&&"0%"!==e}function Qi(e,t,n){return n+t*(e-n)}function Gi(e,t,n,r,a){return void 0!==a&&(e=Qi(e,a,r)),Qi(e,n,r)+t}function Zi(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;e.min=Gi(e.min,t,n,r,a),e.max=Gi(e.max,t,n,r,a)}function Ki(e,t){let{x:n,y:r}=t;Zi(e.x,n.translate,n.scale,n.originPoint),Zi(e.y,r.translate,r.scale,r.originPoint)}function Yi(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Xi(e,t){e.min=e.min+t,e.max=e.max+t}function Ji(e,t,n){let[r,a,i]=n;const o=void 0!==t[i]?t[i]:.5,s=Tr(e.min,e.max,o);Zi(e,t[r],t[a],s,t.scale)}const eo=["x","scaleX","originX"],to=["y","scaleY","originY"];function no(e,t){Ji(e.x,t,eo),Ji(e.y,t,to)}function ro(e,t){return zi(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const ao=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},io=new WeakMap;class oo{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:r}=this.getProps();this.panSession=new bi(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(An(e,"page").point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:a}=this.getProps();if(n&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=On(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Bi(e=>{let t=this.getAxisMotionValue(e).get()||0;if(Rt.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=Ci(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t}),a&&wn.update(()=>a(e,t),!1,!0);const{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:a,onDrag:i}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:o}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(o),void(null!==this.currentDirection&&a&&a(this.currentDirection));this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),i&&i(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>Bi(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:ao(this.visualElement)})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&wn.update(()=>a(e,t))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!so(e,r,this.currentDirection))return;const a=this.getAxisMotionValue(e);let i=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(i=function(e,t,n){let{min:r,max:a}=t;return void 0!==r&&e<r?e=n?Tr(r,e,n.min):Math.max(e,r):void 0!==a&&e>a&&(e=n?Tr(a,e,n.max):Math.min(e,a)),e}(i,this.constraints[e],this.elastic[e])),a.set(i)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,a=this.constraints;t&&qe(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:a,right:i}=t;return{x:_i(e.x,r,i),y:_i(e.y,n,a)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Oi;return!1===e?e=0:!0===e&&(e=Oi),{x:Ii(e,"left","right"),y:Ii(e,"top","bottom")}}(n),a!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Bi(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!qe(e))return!1;const n=e.current;Jn(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");const{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const a=function(e,t,n){const r=ro(e,n),{scroll:a}=t;return a&&(Xi(r.x,a.offset.x),Xi(r.y,a.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let i=function(e,t){return{x:Di(e.x,t.x),y:Di(e.y,t.y)}}(r.layout.layoutBox,a);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(i));this.hasMutatedConstraints=!!e,e&&(i=zi(e))}return i}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:a,dragSnapToOrigin:i,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{},l=Bi(o=>{if(!so(o,t,this.currentDirection))return;let l=s&&s[o]||{};i&&(l={min:0,max:0});const c=r?200:1e6,u=r?40:1e7,d=Oe(Oe({type:"inertia",velocity:n?e[o]:0,bounceStiffness:c,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10},a),l);return this.startAxisValueAnimation(o,d)});return Promise.all(l).then(o)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(Ba(e,n,0,t))}stopAnimation(){Bi(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Bi(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag"+e.toUpperCase(),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){Bi(t=>{const{drag:n}=this.getProps();if(!so(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,a=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:i}=r.layout.layoutBox[t];a.set(e[t]-Tr(n,i,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!qe(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};Bi(e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();r[e]=function(e,t){let n=.5;const r=Ci(e),a=Ci(t);return a>r?n=Kr(t.min,t.max-r,e.min):r>a&&(n=Kr(e.min,e.max-a,t.min)),wt(0,1,n)}({min:n,max:n},this.constraints[e])}});const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Bi(t=>{if(!so(t,e,null))return;const n=this.getAxisMotionValue(t),{min:a,max:i}=this.constraints[t];n.set(Tr(a,i,r[t]))})}addListeners(){if(!this.visualElement.current)return;io.set(this.visualElement,this);const e=Tn(this.visualElement.current,"pointerdown",e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{const{dragConstraints:e}=this.getProps();qe(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),t();const a=Cn(window,"resize",()=>this.scalePositionWithinConstraints()),i=n.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(Bi(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{a(),e(),r(),i&&i()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:a=!1,dragElastic:i=Oi,dragMomentum:o=!0}=e;return Oe(Oe({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:a,dragElastic:i,dragMomentum:o})}}function so(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const lo=e=>(t,n)=>{e&&wn.update(()=>e(t,n))};const co={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function uo(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const fo={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!Mt.test(e))return e;e=parseFloat(e)}const n=uo(e,t.target.x),r=uo(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},ho={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const a=e,i=Hr.parse(e);if(i.length>5)return a;const o=Hr.createTransformer(e),s="number"!==typeof i[0]?1:0,l=r.x.scale*n.x,c=r.y.scale*n.y;i[0+s]/=l,i[1+s]/=c;const u=Tr(l,c,.5);return"number"===typeof i[2+s]&&(i[2+s]/=u),"number"===typeof i[3+s]&&(i[3+s]/=u),o(i)}};class po extends i.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:a}=e;var i;i=go,Object.assign(ut,i),a&&(t.group&&t.group.add(a),n&&n.register&&r&&n.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",()=>{this.safeToRemove()}),a.setOptions(Oe(Oe({},a.options),{},{onExitComplete:()=>this.safeToRemove()}))),co.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:a}=this.props,i=n.projection;return i?(i.isPresent=a,r||e.layoutDependency!==t||void 0===t?i.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?i.promote():i.relegate()||wn.postRender(()=>{const e=i.getStack();e&&e.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function mo(e){const[t,n]=function(){const e=(0,i.useContext)(Fe);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,a=(0,i.useId)();return(0,i.useEffect)(()=>r(a),[]),!t&&n?[!1,()=>n&&n(a)]:[!0]}(),r=(0,i.useContext)(nt);return i.createElement(po,Oe(Oe({},e),{},{layoutGroup:r,switchLayoutGroup:(0,i.useContext)(rt),isPresent:t,safeToRemove:n}))}const go={borderRadius:Oe(Oe({},fo),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:fo,borderTopRightRadius:fo,borderBottomLeftRadius:fo,borderBottomRightRadius:fo,boxShadow:ho},yo=["TopLeft","TopRight","BottomLeft","BottomRight"],vo=yo.length,xo=e=>"string"===typeof e?parseFloat(e):e,bo=e=>"number"===typeof e||Mt.test(e);function wo(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const ko=jo(0,.5,gr),So=jo(.5,.95,vn);function jo(e,t,n){return r=>r<e?0:r>t?1:n(Kr(e,t,r))}function Eo(e,t){e.min=t.min,e.max=t.max}function No(e,t){Eo(e.x,t.x),Eo(e.y,t.y)}function Co(e,t,n,r,a){return e=Qi(e-=t,1/n,r),void 0!==a&&(e=Qi(e,1/a,r)),e}function Po(e,t,n,r,a){let[i,o,s]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,a=arguments.length>4?arguments[4]:void 0,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;Rt.test(t)&&(t=parseFloat(t),t=Tr(o.min,o.max,t/100)-o.min);if("number"!==typeof t)return;let s=Tr(i.min,i.max,r);e===i&&(s-=t),e.min=Co(e.min,t,n,s,a),e.max=Co(e.max,t,n,s,a)}(e,t[i],t[o],t[s],t.scale,r,a)}const Ao=["x","scaleX","originX"],To=["y","scaleY","originY"];function Lo(e,t,n,r){Po(e.x,t,Ao,n?n.x:void 0,r?r.x:void 0),Po(e.y,t,To,n?n.y:void 0,r?r.y:void 0)}function Ro(e){return 0===e.translate&&1===e.scale}function Mo(e){return Ro(e.x)&&Ro(e.y)}function _o(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Do(e){return Ci(e.x)/Ci(e.y)}class Oo{constructor(){this.members=[]}add(e){Wa(this.members,e),e.scheduleRender()}remove(e){if(Ha(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex(t=>e===t);if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Io(e,t,n){let r="";const a=e.x.translate/t.x,i=e.y.translate/t.y;if((a||i)&&(r="translate3d(".concat(a,"px, ").concat(i,"px, 0) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{rotate:e,rotateX:t,rotateY:a}=n;e&&(r+="rotate(".concat(e,"deg) ")),t&&(r+="rotateX(".concat(t,"deg) ")),a&&(r+="rotateY(".concat(a,"deg) "))}const o=e.x.scale*t.x,s=e.y.scale*t.y;return 1===o&&1===s||(r+="scale(".concat(o,", ").concat(s,")")),r||"none"}const Vo=(e,t)=>e.depth-t.depth;class Fo{constructor(){this.children=[],this.isDirty=!1}add(e){Wa(this.children,e),this.isDirty=!0}remove(e){Ha(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Vo),this.isDirty=!1,this.children.forEach(e)}}const Bo=["","X","Y","Z"],zo={visibility:"hidden"};let Uo=0;const Wo={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Ho(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:a,resetTransform:i}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=Uo++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Wo.totalNodes=Wo.resolvedTargetDeltas=Wo.recalculatedProjection=0,this.nodes.forEach(Qo),this.nodes.forEach(es),this.nodes.forEach(ts),this.nodes.forEach(Go),function(e){window.MotionDebug&&window.MotionDebug.record(e)}(Wo)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new Fo)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new qa),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(this.instance)return;var r;this.isSVG=(r=e)instanceof SVGElement&&"svg"!==r.tagName,this.instance=e;const{layoutId:a,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(i||a)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){const n=performance.now(),r=a=>{let{timestamp:i}=a;const o=i-n;o>=t&&(kn(r),e(o-t))};return wn.read(r,!0),()=>kn(r)}(r,250),co.hasAnimatedSinceResize&&(co.hasAnimatedSinceResize=!1,this.nodes.forEach(Jo))})}a&&this.root.registerSharedNode(a,this),!1!==this.options.animate&&o&&(a||i)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n,hasRelativeTargetChanged:r,layout:a}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const i=this.options.transition||o.getDefaultTransition()||ss,{onLayoutAnimationStart:s,onLayoutAnimationComplete:l}=o.getProps(),c=!this.targetLayout||!_o(this.targetLayout,a)||r,u=!n&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||n&&(c||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e=Oe(Oe({},Va(i,"layout")),{},{onPlay:s,onComplete:l});(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else n||Jo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=a})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,kn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ns),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){const e=this.path[a];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Ko);this.isUpdating||this.nodes.forEach(Yo),this.isUpdating=!1,this.nodes.forEach(Xo),this.nodes.forEach(qo),this.nodes.forEach($o),this.clearAllSnapshots();const e=performance.now();Sn.delta=wt(0,1e3/60,e-Sn.timestamp),Sn.timestamp=e,Sn.isProcessing=!0,jn.update.process(Sn),jn.preRender.process(Sn),jn.render.process(Sn),Sn.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Zo),this.sharedNodes.forEach(rs)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,wn.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){wn.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:a(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;const e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!Mo(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&(t||Hi(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),us((r=n).x),us(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(Xi(t.x,n.offset.x),Xi(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};No(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:a,options:i}=r;if(r!==this.root&&a&&i.layoutScroll){if(a.isRoot){No(t,e);const{scroll:n}=this.root;n&&(Xi(t.x,-n.offset.x),Xi(t.y,-n.offset.y))}Xi(t.x,a.offset.x),Xi(t.y,a.offset.y)}}return t}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};No(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&no(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Hi(e.latestValues)&&no(n,e.latestValues)}return Hi(this.latestValues)&&no(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};No(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!Hi(e.latestValues))continue;Wi(e.latestValues)&&e.updateSnapshot();const r=Fi();No(r,e.measurePageBox()),Lo(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return Hi(this.latestValues)&&Lo(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options=Oe(Oe(Oe({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Sn.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];var t;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:a,layoutId:i}=this.options;if(this.layout&&(a||i)){if(this.resolvedRelativeTargetAt=Sn.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Mi(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),No(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,l=this.relativeParent.target,Li(o.x,s.x,l.x),Li(o.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):No(this.target,this.layout.layoutBox),Ki(this.target,this.targetDelta)):No(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Mi(this.relativeTargetOrigin,this.target,e.target),No(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Wo.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Wi(this.parent.latestValues)&&!qi(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Sn.timestamp&&(r=!1),r)return;const{layout:a,layoutId:i}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!a&&!i)return;No(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const a=n.length;if(!a)return;let i,o;t.x=t.y=1;for(let s=0;s<a;s++){i=n[s],o=i.projectionDelta;const a=i.instance;a&&a.style&&"contents"===a.style.display||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&no(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Ki(e,o)),r&&Hi(i.latestValues)&&no(e,i.latestValues))}t.x=Yi(t.x),t.y=Yi(t.y)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox);const{target:l}=t;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const c=this.projectionTransform;Ti(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=Io(this.projectionDelta,this.treeScale),this.projectionTransform===c&&this.treeScale.x===o&&this.treeScale.y===s||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Wo.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.options.scheduleRender&&this.options.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,r=n?n.latestValues:{},a=Oe({},this.latestValues),i={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const o={x:{min:0,max:0},y:{min:0,max:0}},s=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,u=Boolean(s&&!c&&!0===this.options.crossfade&&!this.path.some(os));let d;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var l,f,h,p,m,g;as(i.x,e.x,n),as(i.y,e.y,n),this.setTargetDelta(i),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Mi(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=n,is(h.x,p.x,m.x,g),is(h.y,p.y,m.y,g),d&&(l=this.relativeTarget,f=d,l.x.min===f.x.min&&l.x.max===f.x.max&&l.y.min===f.y.min&&l.y.max===f.y.max)&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),No(d,this.relativeTarget)),s&&(this.animationValues=a,function(e,t,n,r,a,i){a?(e.opacity=Tr(0,void 0!==n.opacity?n.opacity:1,ko(r)),e.opacityExit=Tr(void 0!==t.opacity?t.opacity:1,0,So(r))):i&&(e.opacity=Tr(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let o=0;o<vo;o++){const a="border".concat(yo[o],"Radius");let i=wo(t,a),s=wo(n,a);void 0===i&&void 0===s||(i||(i=0),s||(s=0),0===i||0===s||bo(i)===bo(s)?(e[a]=Math.max(Tr(xo(i),xo(s),r),0),(Rt.test(s)||Rt.test(i))&&(e[a]+="%")):e[a]=s)}(t.rotate||n.rotate)&&(e.rotate=Tr(t.rotate||0,n.rotate||0,r))}(a,r,this.latestValues,n,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(kn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=wn.update(()=>{co.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){const r=pt(e)?e:Ga(e);return r.start(Ba("",r,t,n)),r.animation}(0,1e3,Oe(Oe({},e),{},{onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:a}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ds(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=Ci(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=Ci(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}No(t,n),no(t,a),Ti(this.projectionDeltaWithTransform,this.layoutCorrected,t,a)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new Oo);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let a=0;a<Bo.length;a++){const t="rotate"+Bo[a];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}e.render();for(const a in r)e.setStaticValue(a,r[a]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return zo;const r={visibility:""},a=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=pn(null===e||void 0===e?void 0:e.pointerEvents)||"",r.transform=a?a(this.latestValues,""):"none",r;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=pn(null===e||void 0===e?void 0:e.pointerEvents)||""),this.hasProjected&&!Hi(this.latestValues)&&(t.transform=a?a({},""):"none",this.hasProjected=!1),t}const o=i.animationValues||i.latestValues;this.applyTransformsToTarget(),r.transform=Io(this.projectionDeltaWithTransform,this.treeScale,o),a&&(r.transform=a(o,r.transform));const{x:s,y:l}=this.projectionDelta;r.transformOrigin="".concat(100*s.origin,"% ").concat(100*l.origin,"% 0"),i.animationValues?r.opacity=i===this?null!==(n=null!==(t=o.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=i===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0;for(const c in ut){if(void 0===o[c])continue;const{correct:e,applyTo:t}=ut[c],n="none"===r.transform?o[c]:e(o[c],i);if(t){const e=t.length;for(let a=0;a<e;a++)r[t[a]]=n}else r[c]=n}return this.options.layoutId&&(r.pointerEvents=i===this?pn(null===e||void 0===e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(Ko),this.root.sharedNodes.clear()}}}function qo(e){e.updateLayout()}function $o(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:a}=e.options,i=n.source!==e.layout.source;"size"===a?Bi(e=>{const r=i?n.measuredBox[e]:n.layoutBox[e],a=Ci(r);r.min=t[e].min,r.max=r.min+a}):ds(a,n.layoutBox,t)&&Bi(r=>{const a=i?n.measuredBox[r]:n.layoutBox[r],o=Ci(t[r]);a.max=a.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Ti(o,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};i?Ti(s,e.applyTransform(r,!0),n.measuredBox):Ti(s,t,n.layoutBox);const l=!Mo(o);let c=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:a,layout:i}=r;if(a&&i){const o={x:{min:0,max:0},y:{min:0,max:0}};Mi(o,n.layoutBox,a.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};Mi(s,t,i.layoutBox),_o(o,s)||(c=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Qo(e){Wo.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Go(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Zo(e){e.clearSnapshot()}function Ko(e){e.clearMeasurements()}function Yo(e){e.isLayoutDirty=!1}function Xo(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Jo(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function es(e){e.resolveTargetDelta()}function ts(e){e.calcProjection()}function ns(e){e.resetRotation()}function rs(e){e.removeLeadSnapshot()}function as(e,t,n){e.translate=Tr(t.translate,0,n),e.scale=Tr(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function is(e,t,n,r){e.min=Tr(t.min,n.min,r),e.max=Tr(t.max,n.max,r)}function os(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const ss={duration:.45,ease:[.4,0,.1,1]},ls=e=>"undefined"!==typeof navigator&&navigator.userAgent.toLowerCase().includes(e),cs=ls("applewebkit/")&&!ls("chrome/")?Math.round:vn;function us(e){e.min=cs(e.min),e.max=cs(e.max)}function ds(e,t,n){return"position"===e||"preserve-aspect"===e&&!Pi(Do(t),Do(n),.2)}const fs=Ho({attachResizeListener:(e,t)=>Cn(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),hs={current:void 0},ps=Ho({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!hs.current){const e=new fs({});e.mount(window),e.setOptions({layoutScroll:!0}),hs.current=e}return hs.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),ms={pan:{Feature:class extends Vn{constructor(){super(...arguments),this.removePointerDownListener=vn}onPointerDown(e){this.session=new bi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ao(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:lo(e),onStart:lo(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&wn.update(()=>r(e,t))}}}mount(){this.removePointerDownListener=Tn(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Vn{constructor(e){super(e),this.removeGroupControls=vn,this.removeListeners=vn,this.controls=new oo(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||vn}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:ps,MeasureLayout:mo}};const gs=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ys(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;Jn(n<=4,'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.'));const[r,a]=function(e){const t=gs.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const e=i.trim();return Ua(e)?parseFloat(e):e}return xt(a)?ys(a,t,n+1):a}function vs(e,t,n){let r=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(t),t));const a=e.current;if(!(a instanceof Element))return{target:r,transitionEnd:n};n&&(n=Oe({},n)),e.values.forEach(e=>{const t=e.get();if(!xt(t))return;const n=ys(t,a);n&&e.set(n)});for(const i in r){const e=r[i];if(!xt(e))continue;const t=ys(e,a);t&&(r[i]=t,n||(n={}),void 0===n[i]&&(n[i]=e))}return{target:r,transitionEnd:n}}const xs=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),bs=e=>xs.has(e),ws=e=>e===kt||e===Mt,ks=(e,t)=>parseFloat(e.split(", ")[t]),Ss=(e,t)=>(n,r)=>{let{transform:a}=r;if("none"===a||!a)return 0;const i=a.match(/^matrix3d\((.+)\)$/);if(i)return ks(i[1],t);{const t=a.match(/^matrix\((.+)\)$/);return t?ks(t[1],e):0}},js=new Set(["x","y","z"]),Es=dt.filter(e=>!js.has(e));const Ns={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:a="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(a)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:a="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(a)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:Ss(4,13),y:Ss(5,14)};Ns.translateX=Ns.x,Ns.translateY=Ns.y;const Cs=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=Oe({},t),r=Oe({},r);const a=Object.keys(t).filter(bs);let i=[],o=!1;const s=[];if(a.forEach(a=>{const l=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],u=Ya(c);const d=t[a];let f;if(fn(d)){const e=d.length,t=null===d[0]?1:0;c=d[t],u=Ya(c);for(let n=t;n<e&&null!==d[n];n++)f?Jn(Ya(d[n])===f,"All keyframes must be of the same type"):(f=Ya(d[n]),Jn(f===u||ws(u)&&ws(f),"Keyframes must be of the same dimension as the current value"))}else f=Ya(d);if(u!==f)if(ws(u)&&ws(f)){const e=l.get();"string"===typeof e&&l.set(parseFloat(e)),"string"===typeof d?t[a]=parseFloat(d):Array.isArray(d)&&f===Mt&&(t[a]=d.map(parseFloat))}else(null===u||void 0===u?void 0:u.transform)&&(null===f||void 0===f?void 0:f.transform)&&(0===c||0===d)?0===c?l.set(f.transform(c)):t[a]=u.transform(d):(o||(i=function(e){const t=[];return Es.forEach(n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),o=!0),s.push(a),r[a]=void 0!==r[a]?r[a]:t[a],l.jump(d))}),s.length){const n=s.indexOf("height")>=0?window.pageYOffset:null,a=((e,t,n)=>{const r=t.measureViewportBox(),a=t.current,i=getComputedStyle(a),{display:o}=i,s={};"none"===o&&t.setStaticValue("display",e.display||"block"),n.forEach(e=>{s[e]=Ns[e](r,i)}),t.render();const l=t.measureViewportBox();return n.forEach(n=>{const r=t.getValue(n);r&&r.jump(s[n]),e[n]=Ns[n](l,i)}),e})(t,e,s);return i.length&&i.forEach(t=>{let[n,r]=t;e.getValue(n).set(r)}),e.render(),Be&&null!==n&&window.scrollTo({top:n}),{target:a,transitionEnd:r}}return{target:t,transitionEnd:r}};function Ps(e,t,n,r){return(e=>Object.keys(e).some(bs))(t)?Cs(e,t,n,r):{target:t,transitionEnd:r}}const As={current:null},Ts={current:!1};const Ls=new WeakMap,Rs=["willChange"],Ms=["children"],_s=Object.keys(tt),Ds=_s.length,Os=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Is=Ze.length;class Vs{constructor(e){let{parent:t,props:n,presenceContext:r,reducedMotionConfig:a,visualState:i}=e,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>wn.render(this.render,!1,!0);const{latestValues:s,renderState:l}=i;this.latestValues=s,this.baseTarget=Oe({},s),this.initialValues=n.initial?Oe({},s):{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=a,this.options=o,this.isControllingVariants=Ke(n),this.isVariantNode=Ye(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const c=this.scrapeMotionValuesFromProps(n,{}),{willChange:u}=c,d=Gt(c,Rs);for(const f in d){const e=d[f];void 0!==s[f]&&pt(e)&&(e.set(s[f],!1),za(u)&&u.add(f))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,Ls.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),Ts.current||function(){if(Ts.current=!0,Be)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>As.current=e.matches;e.addListener(t),t()}else As.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||As.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ls.delete(this.current),this.projection&&this.projection.unmount(),kn(this.notifyUpdate),kn(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){const n=ft.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&wn.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{r(),a()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures(e,t,n,r){let a,i,{children:o}=e,s=Gt(e,Ms);for(let l=0;l<Ds;l++){const e=_s[l],{isEnabled:t,Feature:n,ProjectionNode:r,MeasureLayout:o}=tt[e];r&&(a=r),t(s)&&(!this.features[e]&&n&&(this.features[e]=new n(this)),o&&(i=o))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&a){this.projection=new a(this.latestValues,this.parent&&this.parent.projection);const{layoutId:e,layout:t,drag:n,dragConstraints:i,layoutScroll:o,layoutRoot:l}=s;this.projection.setOptions({layoutId:e,layout:t,alwaysMeasureLayout:Boolean(n)||i&&qe(i),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"===typeof t?t:"both",initialPromotionConfig:r,layoutScroll:o,layoutRoot:l})}return i}updateFeatures(){for(const e in this.features){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<Os.length;n++){const t=Os[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const a in t){const i=t[a],o=n[a];if(pt(i))e.addValue(a,i),za(r)&&r.add(a);else if(pt(o))e.addValue(a,Ga(i,{owner:e})),za(r)&&r.remove(a);else if(o!==i)if(e.hasValue(a)){const t=e.getValue(a);!t.hasAnimated&&t.set(i)}else{const t=e.getStaticValue(a);e.addValue(a,Ga(void 0!==t?t:i,{owner:e}))}}for(const a in n)void 0===t[a]&&e.removeValue(a);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const e={};for(let t=0;t<Is;t++){const n=Ze[t],r=this.props[n];($e(r)||!1===r)&&(e[n]=r)}return e}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=Ga(t,{owner:this}),this.addValue(e,n)),n}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"===typeof n||"object"===typeof n?null===(t=un(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const a=this.getBaseTargetFromProps(this.props,e);return void 0===a||pt(a)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:a}on(e,t){return this.events[e]||(this.events[e]=new qa),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}}const Fs=["transition","transitionEnd"];class Bs extends Vs{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}makeTargetAnimatableFromInstance(e,t,n){let{transition:r,transitionEnd:a}=e,i=Gt(e,Fs),{transformValues:o}=t,s=function(e,t,n){const r={};for(const a in e){const e=ri(a,t);if(void 0!==e)r[a]=e;else{const e=n.getValue(a);e&&(r[a]=e.get())}}return r}(i,r||{},this);if(o&&(a&&(a=o(a)),i&&(i=o(i)),s&&(s=o(s))),n){!function(e,t,n){var r,a;const i=Object.keys(t).filter(t=>!e.hasValue(t)),o=i.length;if(o)for(let s=0;s<o;s++){const o=i[s],l=t[o];let c=null;Array.isArray(l)&&(c=l[0]),null===c&&(c=null!==(a=null!==(r=n[o])&&void 0!==r?r:e.readValue(o))&&void 0!==a?a:t[o]),void 0!==c&&null!==c&&("string"===typeof c&&(Ua(c)||Da(c))?c=parseFloat(c):!Ja(c)&&Hr.test(l)&&(c=_a(o,l)),e.addValue(o,Ga(c,{owner:e})),void 0===n[o]&&(n[o]=c),null!==c&&e.setBaseTarget(o,c))}}(this,i,s);const e=((e,t,n,r)=>{const a=vs(e,t,r);return Ps(e,t=a.target,n,r=a.transitionEnd)})(this,i,s,a);a=e.transitionEnd,i=e.target}return Oe({transition:r,transitionEnd:a},i)}}class zs extends Bs{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(ft.has(t)){const e=Ma(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),a=(vt(t)?r.getPropertyValue(t):r[t])||0;return"string"===typeof a?a.trim():a}var n}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return ro(e,n)}build(e,t,n,r){Ft(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return ln(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;pt(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}renderInstance(e,t,n,r){an(e,t,n,r)}}class Us extends Bs{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(ft.has(t)){const e=Ma(t);return e&&e.default||0}return t=on.has(t)?t:We(t),e.getAttribute(t)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e,t){return cn(e,t)}build(e,t,n,r){Jt(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){sn(e,t,0,r)}mount(e){this.isSVGTag=tn(e.tagName),super.mount(e)}}const Ws=(e,t)=>ct(e)?new Us(t,{enableHardwareAcceleration:!1}):new zs(t,{enableHardwareAcceleration:!0}),Hs={layout:{ProjectionNode:ps,MeasureLayout:mo}},qs=Oe(Oe(Oe(Oe({},vi),Zn),ms),Hs),$s=st((e,t)=>function(e,t,n,r){let{forwardMotionProps:a=!1}=t;return Oe(Oe({},ct(e)?En:Nn),{},{preloadedFeatures:n,useRender:rn(a),createVisualElement:r,Component:e})}(e,t,qs,Ws));const Qs=["title","titleId"];function Gs(e,t){let{title:n,titleId:r}=e,a=Gt(e,Qs);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}const Zs=i.forwardRef(Gs),Ks=["title","titleId"];function Ys(e,t){let{title:n,titleId:r}=e,a=Gt(e,Ks);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}const Xs=i.forwardRef(Ys),Js=["title","titleId"];function el(e,t){let{title:n,titleId:r}=e,a=Gt(e,Js);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const tl=i.forwardRef(el),nl=["title","titleId"];function rl(e,t){let{title:n,titleId:r}=e,a=Gt(e,nl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const al=i.forwardRef(rl);var il=n(579);const ol=(0,i.createContext)(void 0),sl=e=>{let{children:t}=e;const[n,r]=(0,i.useState)("light");(0,i.useEffect)(()=>{const e=localStorage.getItem("theme");e?r(e):window.matchMedia("(prefers-color-scheme: dark)").matches&&r("dark")},[]),(0,i.useEffect)(()=>{"dark"===n?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("theme",n)},[n]);return(0,il.jsx)(ol.Provider,{value:{theme:n,toggleTheme:()=>{r(e=>"light"===e?"dark":"light")}},children:t})},ll=()=>{const[e,t]=(0,i.useState)(!1),[n,r]=(0,i.useState)(!1),a=te(),{theme:o,toggleTheme:s}=(()=>{const e=(0,i.useContext)(ol);if(void 0===e)throw new Error("useTheme must be used within a ThemeProvider");return e})();(0,i.useEffect)(()=>{const e=()=>{const e=window.scrollY>50;r(e)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);const l=[{name:"Home",path:"/"},{name:"About",path:"/about"},{name:"Portfolio",path:"/portfolio"},{name:"Services",path:"/services"},{name:"Resume",path:"/resume"},{name:"Blog",path:"/blog"},{name:"Contact",path:"/contact"}];return(0,il.jsx)($s.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(n?"bg-white/90 dark:bg-dark-900/90 backdrop-blur-lg shadow-lg":"bg-transparent"),children:(0,il.jsxs)("div",{className:"container-custom",children:[(0,il.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,il.jsx)(Ae,{to:"/",className:"text-2xl font-bold text-gradient",children:"Deepak"}),(0,il.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[l.map(e=>(0,il.jsx)(Ae,{to:e.path,className:"text-sm font-medium transition-colors duration-300 hover:text-primary-600 ".concat(a.pathname===e.path?"text-primary-600":"text-gray-700 dark:text-gray-300"),children:e.name},e.name)),(0,il.jsx)("button",{onClick:s,className:"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 hover:bg-gray-200 dark:hover:bg-dark-700 transition-colors duration-300",children:"light"===o?(0,il.jsx)(Zs,{className:"h-5 w-5 text-gray-700 dark:text-gray-300"}):(0,il.jsx)(Xs,{className:"h-5 w-5 text-gray-700 dark:text-gray-300"})})]}),(0,il.jsxs)("div",{className:"md:hidden flex items-center space-x-4",children:[(0,il.jsx)("button",{onClick:s,className:"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 hover:bg-gray-200 dark:hover:bg-dark-700 transition-colors duration-300",children:"light"===o?(0,il.jsx)(Zs,{className:"h-5 w-5 text-gray-700 dark:text-gray-300"}):(0,il.jsx)(Xs,{className:"h-5 w-5 text-gray-700 dark:text-gray-300"})}),(0,il.jsx)("button",{onClick:()=>t(!e),className:"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 hover:bg-gray-200 dark:hover:bg-dark-700 transition-colors duration-300",children:e?(0,il.jsx)(tl,{className:"h-6 w-6 text-gray-700 dark:text-gray-300"}):(0,il.jsx)(al,{className:"h-6 w-6 text-gray-700 dark:text-gray-300"})})]})]}),e&&(0,il.jsx)($s.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},className:"md:hidden bg-white dark:bg-dark-900 shadow-lg rounded-lg mt-2 p-4",children:(0,il.jsx)("div",{className:"flex flex-col space-y-3",children:l.map(e=>(0,il.jsx)(Ae,{to:e.path,onClick:()=>t(!1),className:"text-sm font-medium transition-colors duration-300 hover:text-primary-600 py-2 ".concat(a.pathname===e.path?"text-primary-600":"text-gray-700 dark:text-gray-300"),children:e.name},e.name))})})]})})},cl=["title","titleId"];function ul(e,t){let{title:n,titleId:r}=e,a=Gt(e,cl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}const dl=i.forwardRef(ul),fl=["title","titleId"];function hl(e,t){let{title:n,titleId:r}=e,a=Gt(e,fl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const pl=i.forwardRef(hl),ml=()=>{const e=(new Date).getFullYear(),t=[{name:"LinkedIn",url:"https://linkedin.com/in/deepak-garg-in",icon:(0,il.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,il.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})},{name:"GitHub",url:"https://github.com/mrgarg-g1",icon:(0,il.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,il.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})},{name:"Twitter",url:"https://x.com/mrgarg_g1",icon:(0,il.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,il.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{name:"Medium",url:"https://medium.com/@gargdeepak114",icon:(0,il.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,il.jsx)("path",{d:"M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"})})}];return(0,il.jsx)("footer",{className:"bg-gray-50 dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700",children:(0,il.jsxs)("div",{className:"container-custom py-12",children:[(0,il.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,il.jsxs)("div",{className:"space-y-4",children:[(0,il.jsx)("h3",{className:"text-xl font-bold text-gradient",children:"About Deepak Garg"}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm leading-relaxed",children:"Data Scientist & AI Engineer specializing in cutting-edge machine learning solutions. Transforming complex data challenges into scalable AI systems that drive business growth."}),(0,il.jsx)("div",{className:"flex space-x-4",children:t.map((e,t)=>(0,il.jsx)($s.a,{href:e.url,target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.1},whileTap:{scale:.9},className:"text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300",children:e.icon},e.name))})]}),(0,il.jsxs)("div",{className:"space-y-4",children:[(0,il.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Navigation"}),(0,il.jsx)("ul",{className:"space-y-2",children:[{name:"Home",path:"/"},{name:"About",path:"/about"},{name:"Portfolio",path:"/portfolio"},{name:"Services",path:"/services"},{name:"Resume",path:"/resume"},{name:"Blog",path:"/blog"},{name:"Contact",path:"/contact"}].map(e=>(0,il.jsx)("li",{children:(0,il.jsx)(Ae,{to:e.path,className:"text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300 text-sm",children:e.name})},e.name))})]}),(0,il.jsxs)("div",{className:"space-y-4",children:[(0,il.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Services"}),(0,il.jsx)("ul",{className:"space-y-2",children:["AI/ML Model Development","Multi-Cloud ML Architecture","Data Engineering","Document Intelligence","Predictive Analytics","AI Strategy Consulting"].map(e=>(0,il.jsx)("li",{className:"text-gray-600 dark:text-gray-400 text-sm",children:e},e))})]}),(0,il.jsxs)("div",{className:"space-y-4",children:[(0,il.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Contact Info"}),(0,il.jsxs)("div",{className:"space-y-3",children:[(0,il.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,il.jsx)(dl,{className:"h-5 w-5 text-primary-600 mt-0.5 flex-shrink-0"}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Noida, Uttar Pradesh, India"})]}),(0,il.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,il.jsx)(pl,{className:"h-5 w-5 text-primary-600 flex-shrink-0"}),(0,il.jsx)("a",{href:"mailto:<EMAIL>",className:"text-gray-600 dark:text-gray-400 text-sm hover:text-primary-600 transition-colors",children:"<EMAIL>"})]})]})]})]}),(0,il.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-dark-700",children:(0,il.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,il.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:["\xa9 ",e," Deepak Garg. All rights reserved."]}),(0,il.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,il.jsx)("span",{children:"Powered by AI Innovation"}),(0,il.jsx)("span",{className:"text-blue-500",children:"\ud83d\ude80"})]})]})})]})})},gl=["title","titleId"];function yl(e,t){let{title:n,titleId:r}=e,a=Gt(e,gl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const vl=i.forwardRef(yl),xl=()=>{const[e,t]=(0,i.useState)(!1);(0,i.useEffect)(()=>{const e=()=>{window.pageYOffset>300?t(!0):t(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);return(0,il.jsx)(il.Fragment,{children:e&&(0,il.jsx)($s.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 z-50 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300",children:(0,il.jsx)(vl,{className:"h-6 w-6"})})})},bl=["title","titleId"];function wl(e,t){let{title:n,titleId:r}=e,a=Gt(e,bl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const kl=i.forwardRef(wl),Sl=["title","titleId"];function jl(e,t){let{title:n,titleId:r}=e,a=Gt(e,Sl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))}const El=i.forwardRef(jl),Nl=["title","titleId"];function Cl(e,t){let{title:n,titleId:r}=e,a=Gt(e,Nl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))}const Pl=i.forwardRef(Cl),Al=["title","titleId"];function Tl(e,t){let{title:n,titleId:r}=e,a=Gt(e,Al);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"}))}const Ll=i.forwardRef(Tl),Rl=["title","titleId"];function Ml(e,t){let{title:n,titleId:r}=e,a=Gt(e,Rl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"}))}const _l=i.forwardRef(Ml),Dl=["title","titleId"];function Ol(e,t){let{title:n,titleId:r}=e,a=Gt(e,Dl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.53 16.122a3 3 0 0 0-5.78 1.128 2.25 2.25 0 0 1-2.4 2.245 4.5 4.5 0 0 0 8.4-2.245c0-.399-.078-.78-.22-1.128Zm0 0a15.998 15.998 0 0 0 3.388-1.62m-5.043-.025a15.994 15.994 0 0 1 1.622-3.395m3.42 3.42a15.995 15.995 0 0 0 4.764-4.648l3.876-5.814a1.151 1.151 0 0 0-1.597-1.597L14.146 6.32a15.996 15.996 0 0 0-4.649 4.763m3.42 3.42a6.776 6.776 0 0 0-3.42-3.42"}))}const Il=i.forwardRef(Ol),Vl=["title","titleId"];function Fl(e,t){let{title:n,titleId:r}=e,a=Gt(e,Vl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))}const Bl=i.forwardRef(Fl);function zl(){const e=(0,i.useRef)(!1);return ze(()=>(e.current=!0,()=>{e.current=!1}),[]),e}class Ul extends i.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Wl(e){let{children:t,isPresent:n}=e;const r=(0,i.useId)(),a=(0,i.useRef)(null),o=(0,i.useRef)({width:0,height:0,top:0,left:0});return(0,i.useInsertionEffect)(()=>{const{width:e,height:t,top:i,left:s}=o.current;if(n||!a.current||!e||!t)return;a.current.dataset.motionPopId=r;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(i,"px !important;\n            left: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[n]),i.createElement(Ul,{isPresent:n,childRef:a,sizeRef:o},i.cloneElement(t,{ref:a}))}const Hl=e=>{let{children:t,initial:n,isPresent:r,onExitComplete:a,custom:o,presenceAffectsLayout:s,mode:l}=e;const c=dn(ql),u=(0,i.useId)(),d=(0,i.useMemo)(()=>({id:u,initial:n,isPresent:r,custom:o,onExitComplete:e=>{c.set(e,!0);for(const t of c.values())if(!t)return;a&&a()},register:e=>(c.set(e,!1),()=>c.delete(e))}),s?void 0:[r]);return(0,i.useMemo)(()=>{c.forEach((e,t)=>c.set(t,!1))},[r]),i.useEffect(()=>{!r&&!c.size&&a&&a()},[r]),"popLayout"===l&&(t=i.createElement(Wl,{isPresent:r},t)),i.createElement(Fe.Provider,{value:d},t)};function ql(){return new Map}const $l=e=>e.key||"";const Ql=e=>{let{children:t,custom:n,initial:r=!0,onExitComplete:a,exitBeforeEnter:o,presenceAffectsLayout:s=!0,mode:l="sync"}=e;Jn(!o,"Replace exitBeforeEnter with mode='wait'");const c=(0,i.useContext)(nt).forceRender||function(){const e=zl(),[t,n]=(0,i.useState)(0),r=(0,i.useCallback)(()=>{e.current&&n(t+1)},[t]);return[(0,i.useCallback)(()=>wn.postRender(r),[r]),t]}()[0],u=zl(),d=function(e){const t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}(t);let f=d;const h=(0,i.useRef)(new Map).current,p=(0,i.useRef)(f),m=(0,i.useRef)(new Map).current,g=(0,i.useRef)(!0);var y;if(ze(()=>{g.current=!1,function(e,t){e.forEach(e=>{const n=$l(e);t.set(n,e)})}(d,m),p.current=f}),y=()=>{g.current=!0,m.clear(),h.clear()},(0,i.useEffect)(()=>()=>y(),[]),g.current)return i.createElement(i.Fragment,null,f.map(e=>i.createElement(Hl,{key:$l(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:s,mode:l},e)));f=[...f];const v=p.current.map($l),x=d.map($l),b=v.length;for(let i=0;i<b;i++){const e=v[i];-1!==x.indexOf(e)||h.has(e)||h.set(e,void 0)}return"wait"===l&&h.size&&(f=[]),h.forEach((e,t)=>{if(-1!==x.indexOf(t))return;const r=m.get(t);if(!r)return;const o=v.indexOf(t);let g=e;if(!g){const e=()=>{h.delete(t);const e=Array.from(m.keys()).filter(e=>!x.includes(e));if(e.forEach(e=>m.delete(e)),p.current=d.filter(n=>{const r=$l(n);return r===t||e.includes(r)}),!h.size){if(!1===u.current)return;c(),a&&a()}};g=i.createElement(Hl,{key:$l(r),isPresent:!1,onExitComplete:e,custom:n,presenceAffectsLayout:s,mode:l},r),h.set(t,g)}f.splice(o,0,g)}),f=f.map(e=>{const t=e.key;return h.has(t)?e:i.createElement(Hl,{key:$l(e),isPresent:!0,presenceAffectsLayout:s,mode:l},e)}),i.createElement(i.Fragment,null,h.size?f:f.map(e=>(0,i.cloneElement)(e)))},Gl=["title","titleId"];function Zl(e,t){let{title:n,titleId:r}=e,a=Gt(e,Gl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const Kl=i.forwardRef(Zl),Yl=["title","titleId"];function Xl(e,t){let{title:n,titleId:r}=e,a=Gt(e,Yl);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"}))}const Jl=i.forwardRef(Xl),ec=["title","titleId"];function tc(e,t){let{title:n,titleId:r}=e,a=Gt(e,ec);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const nc=i.forwardRef(tc),rc=["title","titleId"];function ac(e,t){let{title:n,titleId:r}=e,a=Gt(e,rc);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"}))}const ic=i.forwardRef(ac),oc=["title","titleId"];function sc(e,t){let{title:n,titleId:r}=e,a=Gt(e,oc);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))}const lc=i.forwardRef(sc),cc=["title","titleId"];function uc(e,t){let{title:n,titleId:r}=e,a=Gt(e,cc);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const dc=i.forwardRef(uc),fc=["title","titleId"];function hc(e,t){let{title:n,titleId:r}=e,a=Gt(e,fc);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const pc=i.forwardRef(hc),mc=["title","titleId"];function gc(e,t){let{title:n,titleId:r}=e,a=Gt(e,mc);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))}const yc=i.forwardRef(gc),vc=()=>{const[e,t]=(0,i.useState)(1),n=[{id:1,title:"AI Strategy & Discovery",subtitle:"Understanding Your Vision",description:"Deep dive into your business objectives, data landscape, and AI readiness. We identify high-impact use cases and create a comprehensive AI roadmap tailored to your enterprise needs.",icon:(0,il.jsx)(Kl,{className:"h-8 w-8"}),duration:"1-2 weeks",deliverables:["AI Readiness Assessment","Use Case Prioritization Matrix","Technical Architecture Blueprint","ROI Projections & Timeline"],technologies:["Business Intelligence","Data Audit","Stakeholder Analysis","Technical Assessment"],color:"text-blue-600",bgColor:"bg-blue-50 dark:bg-blue-900/20"},{id:2,title:"Intelligent Design & Prototyping",subtitle:"Architecting AI Solutions",description:"Design scalable AI architectures with multi-cloud considerations. Create interactive prototypes and proof-of-concepts that demonstrate real business value before full development.",icon:(0,il.jsx)(Jl,{className:"h-8 w-8"}),duration:"2-3 weeks",deliverables:["System Architecture Design","Interactive AI Prototypes","Data Pipeline Blueprints","Multi-Cloud Deployment Strategy"],technologies:["AWS/Azure/GCP","Microservices","API Design","ML Model Architecture"],color:"text-purple-600",bgColor:"bg-purple-50 dark:bg-purple-900/20"},{id:3,title:"Enterprise AI Development",subtitle:"Building Production-Ready Systems",description:"Develop robust AI systems using cutting-edge technologies. Implement LLM fine-tuning, computer vision, NLP, and predictive analytics with enterprise-grade security and scalability.",icon:(0,il.jsx)(nc,{className:"h-8 w-8"}),duration:"4-8 weeks",deliverables:["Production AI Models","Scalable ML Pipelines","Real-time Inference APIs","Comprehensive Documentation"],technologies:["Python/PyTorch/TensorFlow","LLM Fine-tuning","Docker/Kubernetes","MLOps"],color:"text-green-600",bgColor:"bg-green-50 dark:bg-green-900/20"},{id:4,title:"Rigorous Testing & Optimization",subtitle:"Ensuring Excellence",description:"Comprehensive testing including model validation, performance optimization, security audits, and A/B testing. Achieve 95%+ accuracy and enterprise-grade reliability.",icon:(0,il.jsx)(ic,{className:"h-8 w-8"}),duration:"1-2 weeks",deliverables:["Model Performance Reports","Security Audit Results","Load Testing Analysis","Optimization Recommendations"],technologies:["Model Validation","Performance Testing","Security Auditing","A/B Testing"],color:"text-orange-600",bgColor:"bg-orange-50 dark:bg-orange-900/20"},{id:5,title:"Multi-Cloud Deployment",subtitle:"Going Live with Confidence",description:"Deploy across multiple cloud platforms with automated CI/CD pipelines, monitoring, and failover capabilities. Ensure 99.9% uptime and seamless scalability.",icon:(0,il.jsx)(lc,{className:"h-8 w-8"}),duration:"1 week",deliverables:["Production Deployment","Monitoring Dashboards","Auto-scaling Configuration","Disaster Recovery Setup"],technologies:["CI/CD Pipelines","Monitoring & Alerting","Auto-scaling","Load Balancing"],color:"text-red-600",bgColor:"bg-red-50 dark:bg-red-900/20"},{id:6,title:"Ongoing Support & Evolution",subtitle:"Continuous Innovation",description:"24/7 monitoring, performance optimization, and continuous model improvement. Regular updates with latest AI advancements and proactive maintenance.",icon:(0,il.jsx)(dc,{className:"h-8 w-8"}),duration:"Ongoing",deliverables:["24/7 System Monitoring","Monthly Performance Reports","Model Retraining & Updates","Feature Enhancement Roadmap"],technologies:["Monitoring & Analytics","Model Drift Detection","Automated Retraining","Support"],color:"text-indigo-600",bgColor:"bg-indigo-50 dark:bg-indigo-900/20"}],r={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-gray-900",children:(0,il.jsxs)("div",{className:"container-custom",children:[(0,il.jsxs)($s.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"text-center mb-16",children:[(0,il.jsxs)($s.h2,{variants:r,className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6",children:["Enterprise AI ",(0,il.jsx)("span",{className:"text-gradient",children:"Methodology"})]}),(0,il.jsx)($s.p,{variants:r,className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"A proven 6-phase approach to delivering enterprise-grade AI solutions that drive measurable business impact"})]}),(0,il.jsxs)("div",{className:"relative",children:[(0,il.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 via-green-500 via-orange-500 via-red-500 to-indigo-500 rounded-full hidden lg:block"}),(0,il.jsx)("div",{className:"space-y-12",children:n.map((n,r)=>(0,il.jsxs)($s.div,{initial:{opacity:0,x:r%2===0?-50:50},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.6,delay:.1*r},className:"relative flex items-center ".concat(r%2===0?"lg:flex-row":"lg:flex-row-reverse"," flex-col lg:gap-16 gap-8"),children:[(0,il.jsx)("div",{className:"flex-1 max-w-lg",children:(0,il.jsxs)($s.div,{className:"p-8 rounded-2xl shadow-lg border-2 border-transparent hover:border-opacity-50 transition-all duration-300 cursor-pointer ".concat(n.bgColor," ").concat(e===n.id?"ring-4 ring-blue-200 dark:ring-blue-800":""),onClick:()=>t(n.id),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,il.jsxs)("div",{className:"flex items-center mb-4",children:[(0,il.jsx)("div",{className:"p-3 rounded-full ".concat(n.color," bg-white dark:bg-gray-800 shadow-md mr-4"),children:n.icon}),(0,il.jsxs)("div",{children:[(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:n.title}),(0,il.jsx)("p",{className:"text-sm font-medium ".concat(n.color),children:n.subtitle})]})]}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 leading-relaxed",children:n.description}),(0,il.jsxs)("div",{className:"flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4",children:[(0,il.jsx)(pc,{className:"h-4 w-4 mr-2"}),(0,il.jsx)("span",{children:n.duration})]}),(0,il.jsx)(Ql,{children:e===n.id&&(0,il.jsxs)($s.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"space-y-4",children:[(0,il.jsxs)("div",{children:[(0,il.jsxs)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-2 flex items-center",children:[(0,il.jsx)(dc,{className:"h-4 w-4 mr-2 text-green-500"}),"Key Deliverables"]}),(0,il.jsx)("ul",{className:"space-y-1",children:n.deliverables.map((e,t)=>(0,il.jsxs)("li",{className:"text-sm text-gray-600 dark:text-gray-400 flex items-center",children:[(0,il.jsx)(yc,{className:"h-3 w-3 mr-2 text-gray-400"}),e]},t))})]}),(0,il.jsxs)("div",{children:[(0,il.jsxs)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-2 flex items-center",children:[(0,il.jsx)(nc,{className:"h-4 w-4 mr-2 text-blue-500"}),"Technologies & Methods"]}),(0,il.jsx)("div",{className:"flex flex-wrap gap-2",children:n.technologies.map((e,t)=>(0,il.jsx)("span",{className:"px-3 py-1 bg-white dark:bg-gray-800 text-xs font-medium text-gray-700 dark:text-gray-300 rounded-full border",children:e},t))})]})]})})]})}),(0,il.jsx)("div",{className:"relative z-10",children:(0,il.jsx)($s.div,{className:"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg ".concat(e===n.id?"bg-gradient-to-r from-blue-500 to-purple-600":"bg-gray-400"),whileHover:{scale:1.1},onClick:()=>t(n.id),children:n.id})}),(0,il.jsx)("div",{className:"flex-1 max-w-lg hidden lg:block"})]},n.id))})]}),(0,il.jsxs)($s.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-center mt-16 p-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl text-white",children:[(0,il.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Transform Your Business with AI?"}),(0,il.jsx)("p",{className:"text-blue-100 mb-6 max-w-2xl mx-auto",children:"Let's discuss how our proven methodology can deliver enterprise-grade AI solutions for your organization"}),(0,il.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,il.jsx)("button",{className:"bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors",children:"Schedule Strategy Call"}),(0,il.jsx)("button",{className:"border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-3 px-8 rounded-lg transition-colors",children:"View Case Studies"})]})]})]})})},xc=()=>{const e={initial:{opacity:0,y:60},animate:{opacity:1,y:0}},t={animate:{transition:{staggerChildren:.1}}},n=[{icon:(0,il.jsx)(kl,{className:"h-8 w-8"}),name:"Large Language Models (LLMs)",level:95},{icon:(0,il.jsx)(El,{className:"h-8 w-8"}),name:"Multi-Cloud Architecture (AWS/Azure/GCP)",level:93},{icon:(0,il.jsx)(Pl,{className:"h-8 w-8"}),name:"Advanced NLP & Transformers",level:94},{icon:(0,il.jsx)(Ll,{className:"h-8 w-8"}),name:"MLOps & Production Deployment",level:90},{icon:(0,il.jsx)(_l,{className:"h-8 w-8"}),name:"Deep Learning & Neural Networks",level:92},{icon:(0,il.jsx)(Il,{className:"h-8 w-8"}),name:"Computer Vision & OCR Systems",level:88}];return(0,il.jsxs)("div",{className:"min-h-screen",children:[(0,il.jsxs)("section",{className:"hero-gradient min-h-screen flex items-center justify-center relative overflow-hidden",children:[(0,il.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-600/10 to-secondary-600/10"}),(0,il.jsx)($s.div,{initial:"initial",animate:"animate",variants:t,className:"container-custom relative z-10",children:(0,il.jsxs)("div",{className:"text-center space-y-8",children:[(0,il.jsxs)($s.div,{variants:e,className:"space-y-4",children:[(0,il.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold text-gray-900 dark:text-white",children:["I'm"," ",(0,il.jsx)("span",{className:"text-gradient",children:"Deepak Garg"})]}),(0,il.jsx)("p",{className:"text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-light",children:"Senior AI/ML Engineer & Multi-Cloud Architect"})]}),(0,il.jsx)($s.p,{variants:e,className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed",children:"Enterprise AI Architect with 5+ years building production-scale ML systems across Fortune 500 companies. Expert in LLM fine-tuning, multi-cloud deployments, and intelligent automation delivering $2M+ in cost savings."}),(0,il.jsxs)($s.div,{variants:e,className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,il.jsx)(Ae,{to:"/portfolio",className:"btn-primary",children:"View AI Portfolio"}),(0,il.jsx)(Ae,{to:"/contact",className:"btn-outline",children:"Schedule Consultation"})]}),(0,il.jsxs)($s.div,{variants:e,className:"pt-8",children:[(0,il.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:"Scroll down to explore"}),(0,il.jsx)($s.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,il.jsx)(Bl,{className:"h-6 w-6 text-primary-600 mx-auto"})})]})]})}),(0,il.jsx)("div",{className:"absolute top-20 left-10 w-20 h-20 bg-primary-200 dark:bg-primary-800 rounded-full opacity-20 animate-pulse-slow"}),(0,il.jsx)("div",{className:"absolute bottom-20 right-10 w-32 h-32 bg-secondary-200 dark:bg-secondary-800 rounded-full opacity-20 animate-bounce-slow"}),(0,il.jsx)("div",{className:"absolute top-1/2 left-1/4 w-16 h-16 bg-primary-300 dark:bg-primary-700 rounded-full opacity-30 animate-pulse-slow animation-delay-200"})]}),(0,il.jsx)("section",{className:"section-padding bg-white dark:bg-dark-900",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:t,className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,il.jsxs)($s.div,{variants:e,className:"space-y-6",children:[(0,il.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white",children:["Enterprise AI ",(0,il.jsx)("span",{className:"text-gradient",children:"Architect"})]}),(0,il.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 leading-relaxed",children:"Senior AI/ML Engineer with 5+ years architecting enterprise-grade AI solutions across Fortune 500 companies. Expert in multi-cloud ML deployments, LLM fine-tuning, and building scalable data pipelines that process millions of records daily."}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"Proven track record of delivering $2M+ in cost savings through intelligent automation, semantic search systems with 95% accuracy, and real-time ML pipelines processing 10K+ documents daily across AWS, Azure, and Google Cloud platforms."}),(0,il.jsx)(Ae,{to:"/about",className:"btn-primary inline-block",children:"Learn More About Me"})]}),(0,il.jsxs)($s.div,{variants:e,className:"space-y-6",children:[(0,il.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"My Skills"}),(0,il.jsx)("div",{className:"grid grid-cols-2 gap-4",children:n.map((t,n)=>(0,il.jsxs)($s.div,{variants:e,className:"text-center space-y-2",children:[(0,il.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full text-primary-600 dark:text-primary-400",children:t.icon}),(0,il.jsxs)("div",{children:[(0,il.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white text-sm",children:t.name}),(0,il.jsx)("div",{className:"w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2 mt-2",children:(0,il.jsx)($s.div,{className:"bg-primary-600 h-2 rounded-full",initial:{width:0},whileInView:{width:"".concat(t.level,"%")},viewport:{once:!0},transition:{duration:1,delay:.1*n}})})]})]},t.name))})]})]})})}),(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsx)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:t,className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:[{number:"50+",label:"Enterprise AI Projects"},{number:"5+",label:"Years Deep Expertise"},{number:"$2M+",label:"Cost Savings Delivered"},{number:"95%",label:"Average Model Accuracy"}].map((t,n)=>(0,il.jsxs)($s.div,{variants:e,className:"text-center space-y-2",children:[(0,il.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-gradient",children:t.number}),(0,il.jsx)("div",{className:"text-gray-600 dark:text-gray-400 font-medium",children:t.label})]},t.label))})})}),(0,il.jsx)(vc,{}),(0,il.jsx)("section",{className:"section-padding bg-primary-600 dark:bg-primary-700",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"text-center space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white",children:"Ready to Transform Your Business with AI?"}),(0,il.jsx)("p",{className:"text-xl text-primary-100 max-w-2xl mx-auto",children:"Let's collaborate to architect enterprise-grade AI solutions that deliver measurable business impact and competitive advantage."}),(0,il.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,il.jsx)(Ae,{to:"/contact",className:"bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl",children:"Schedule Strategy Call"}),(0,il.jsx)(Ae,{to:"/portfolio",className:"border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-8 rounded-lg transition-all duration-300",children:"View AI Portfolio"})]})]})})})]})},bc=()=>{const e={initial:{opacity:0,y:60},animate:{opacity:1,y:0}};return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:e,className:"text-center space-y-6",children:[(0,il.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white",children:["Enterprise AI ",(0,il.jsx)("span",{className:"text-gradient",children:"Architect"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Senior AI/ML Engineer with 5+ years architecting enterprise-grade AI solutions that deliver measurable business impact across Fortune 500 companies"})]})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-6",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Enterprise AI Leadership"}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"Senior AI/ML Engineer with 5+ years architecting enterprise-grade AI solutions across Fortune 500 companies. Expert in multi-cloud ML deployments, LLM fine-tuning, and building scalable data pipelines that process millions of records daily. My expertise spans from semantic video search systems achieving 95% accuracy to intelligent document processing handling 10K+ documents daily."}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"Currently at Appsquadz (AWS Partner), I architect AI solutions from semantic video search systems to LLM-based automation. I specialize in translating cutting-edge AI research into scalable enterprise tools that create measurable business value."}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"My technical expertise spans multi-cloud ML architectures, advanced NLP, computer vision, and predictive analytics. I've delivered solutions that reduced manual efforts by 90%, improved accuracy by 95%, and generated millions in cost savings for enterprise clients."}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"From fine-tuning LLaMA models for government policies to building real-time facial recognition systems, I transform complex AI challenges into production-ready solutions that scale across AWS, Azure, and Google Cloud platforms."}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"When I'm not architecting AI systems, you'll find me exploring the latest in generative AI, contributing to open-source ML projects, or sharing insights through technical articles on cutting-edge AI implementations."})]}),(0,il.jsx)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-6",children:(0,il.jsxs)("div",{className:"bg-white dark:bg-dark-800 p-8 rounded-xl shadow-lg",children:[(0,il.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"Quick Facts"}),(0,il.jsxs)("div",{className:"space-y-4",children:[(0,il.jsxs)("div",{className:"flex justify-between items-center",children:[(0,il.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Location"}),(0,il.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:"Noida, India"})]}),(0,il.jsxs)("div",{className:"flex justify-between items-center",children:[(0,il.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Experience"}),(0,il.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:"3+ Years"})]}),(0,il.jsxs)("div",{className:"flex justify-between items-center",children:[(0,il.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"AI/ML Projects"}),(0,il.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:"15+ Deployed"})]}),(0,il.jsxs)("div",{className:"flex justify-between items-center",children:[(0,il.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Specialization"}),(0,il.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:"AI/ML & Data Science"})]})]})]})})]})})})]})};function wc(e,t){return function(){return e.apply(t,arguments)}}const{toString:kc}=Object.prototype,{getPrototypeOf:Sc}=Object,{iterator:jc,toStringTag:Ec}=Symbol,Nc=(Cc=Object.create(null),e=>{const t=kc.call(e);return Cc[t]||(Cc[t]=t.slice(8,-1).toLowerCase())});var Cc;const Pc=e=>(e=e.toLowerCase(),t=>Nc(t)===e),Ac=e=>t=>typeof t===e,{isArray:Tc}=Array,Lc=Ac("undefined");const Rc=Pc("ArrayBuffer");const Mc=Ac("string"),_c=Ac("function"),Dc=Ac("number"),Oc=e=>null!==e&&"object"===typeof e,Ic=e=>{if("object"!==Nc(e))return!1;const t=Sc(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Ec in e)&&!(jc in e)},Vc=Pc("Date"),Fc=Pc("File"),Bc=Pc("Blob"),zc=Pc("FileList"),Uc=Pc("URLSearchParams"),[Wc,Hc,qc,$c]=["ReadableStream","Request","Response","Headers"].map(Pc);function Qc(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Tc(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),i=r.length;let o;for(n=0;n<i;n++)o=r[n],t.call(null,e[o],o,e)}}function Gc(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const Zc="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Kc=e=>!Lc(e)&&e!==Zc;const Yc=(Xc="undefined"!==typeof Uint8Array&&Sc(Uint8Array),e=>Xc&&e instanceof Xc);var Xc;const Jc=Pc("HTMLFormElement"),eu=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),tu=Pc("RegExp"),nu=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Qc(n,(n,a)=>{let i;!1!==(i=t(n,a,e))&&(r[a]=i||n)}),Object.defineProperties(e,r)};const ru=Pc("AsyncFunction"),au=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],Zc.addEventListener("message",e=>{let{source:t,data:a}=e;t===Zc&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),Zc.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,_c(Zc.postMessage)),iu="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Zc):"undefined"!==typeof process&&process.nextTick||au,ou={isArray:Tc,isArrayBuffer:Rc,isBuffer:function(e){return null!==e&&!Lc(e)&&null!==e.constructor&&!Lc(e.constructor)&&_c(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||_c(e.append)&&("formdata"===(t=Nc(e))||"object"===t&&_c(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Rc(e.buffer),t},isString:Mc,isNumber:Dc,isBoolean:e=>!0===e||!1===e,isObject:Oc,isPlainObject:Ic,isReadableStream:Wc,isRequest:Hc,isResponse:qc,isHeaders:$c,isUndefined:Lc,isDate:Vc,isFile:Fc,isBlob:Bc,isRegExp:tu,isFunction:_c,isStream:e=>Oc(e)&&_c(e.pipe),isURLSearchParams:Uc,isTypedArray:Yc,isFileList:zc,forEach:Qc,merge:function e(){const{caseless:t}=Kc(this)&&this||{},n={},r=(r,a)=>{const i=t&&Gc(n,a)||a;Ic(n[i])&&Ic(r)?n[i]=e(n[i],r):Ic(r)?n[i]=e({},r):Tc(r)?n[i]=r.slice():n[i]=r};for(let a=0,i=arguments.length;a<i;a++)arguments[a]&&Qc(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Qc(t,(t,r)=>{n&&_c(t)?e[r]=wc(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,i,o;const s={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),i=a.length;i-- >0;)o=a[i],r&&!r(o,e,t)||s[o]||(t[o]=e[o],s[o]=!0);e=!1!==n&&Sc(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Nc,kindOfTest:Pc,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Tc(e))return e;let t=e.length;if(!Dc(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[jc]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Jc,hasOwnProperty:eu,hasOwnProp:eu,reduceDescriptors:nu,freezeMethods:e=>{nu(e,(t,n)=>{if(_c(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];_c(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Tc(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Gc,global:Zc,isContextDefined:Kc,isSpecCompliantForm:function(e){return!!(e&&_c(e.append)&&"FormData"===e[Ec]&&e[jc])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Oc(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Tc(e)?[]:{};return Qc(e,(e,t)=>{const i=n(e,r+1);!Lc(i)&&(a[t]=i)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:ru,isThenable:e=>e&&(Oc(e)||_c(e))&&_c(e.then)&&_c(e.catch),setImmediate:au,asap:iu,isIterable:e=>null!=e&&_c(e[jc])};function su(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}ou.inherits(su,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ou.toJSONObject(this.config),code:this.code,status:this.status}}});const lu=su.prototype,cu={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{cu[e]={value:e}}),Object.defineProperties(su,cu),Object.defineProperty(lu,"isAxiosError",{value:!0}),su.from=(e,t,n,r,a,i)=>{const o=Object.create(lu);return ou.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),su.call(o,e.message,t,n,r,a),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const uu=su;function du(e){return ou.isPlainObject(e)||ou.isArray(e)}function fu(e){return ou.endsWith(e,"[]")?e.slice(0,-2):e}function hu(e,t,n){return e?e.concat(t).map(function(e,t){return e=fu(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const pu=ou.toFlatObject(ou,{},null,function(e){return/^is[A-Z]/.test(e)});const mu=function(e,t,n){if(!ou.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=ou.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!ou.isUndefined(t[e])})).metaTokens,a=n.visitor||c,i=n.dots,o=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&ou.isSpecCompliantForm(t);if(!ou.isFunction(a))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(ou.isDate(e))return e.toISOString();if(ou.isBoolean(e))return e.toString();if(!s&&ou.isBlob(e))throw new uu("Blob is not supported. Use a Buffer instead.");return ou.isArrayBuffer(e)||ou.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let s=e;if(e&&!a&&"object"===typeof e)if(ou.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(ou.isArray(e)&&function(e){return ou.isArray(e)&&!e.some(du)}(e)||(ou.isFileList(e)||ou.endsWith(n,"[]"))&&(s=ou.toArray(e)))return n=fu(n),s.forEach(function(e,r){!ou.isUndefined(e)&&null!==e&&t.append(!0===o?hu([n],r,i):null===o?n:n+"[]",l(e))}),!1;return!!du(e)||(t.append(hu(a,n,i),l(e)),!1)}const u=[],d=Object.assign(pu,{defaultVisitor:c,convertValue:l,isVisitable:du});if(!ou.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!ou.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),ou.forEach(n,function(n,i){!0===(!(ou.isUndefined(n)||null===n)&&a.call(t,n,ou.isString(i)?i.trim():i,r,d))&&e(n,r?r.concat(i):[i])}),u.pop()}}(e),t};function gu(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function yu(e,t){this._pairs=[],e&&mu(e,this,t)}const vu=yu.prototype;vu.append=function(e,t){this._pairs.push([e,t])},vu.toString=function(e){const t=e?function(t){return e.call(this,t,gu)}:gu;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const xu=yu;function bu(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function wu(e,t,n){if(!t)return e;const r=n&&n.encode||bu;ou.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let i;if(i=a?a(t,n):ou.isURLSearchParams(t)?t.toString():new xu(t,n).toString(r),i){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}const ku=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ou.forEach(this.handlers,function(t){null!==t&&e(t)})}},Su={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ju={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:xu,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Eu="undefined"!==typeof window&&"undefined"!==typeof document,Nu="object"===typeof navigator&&navigator||void 0,Cu=Eu&&(!Nu||["ReactNative","NativeScript","NS"].indexOf(Nu.product)<0),Pu="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Au=Eu&&window.location.href||"http://localhost",Tu=Oe(Oe({},r),ju);const Lu=function(e){function t(e,n,r,a){let i=e[a++];if("__proto__"===i)return!0;const o=Number.isFinite(+i),s=a>=e.length;if(i=!i&&ou.isArray(r)?r.length:i,s)return ou.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!o;r[i]&&ou.isObject(r[i])||(r[i]=[]);return t(e,n,r[i],a)&&ou.isArray(r[i])&&(r[i]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let i;for(r=0;r<a;r++)i=n[r],t[i]=e[i];return t}(r[i])),!o}if(ou.isFormData(e)&&ou.isFunction(e.entries)){const n={};return ou.forEachEntry(e,(e,r)=>{t(function(e){return ou.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const Ru={transitional:Su,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=ou.isObject(e);a&&ou.isHTMLForm(e)&&(e=new FormData(e));if(ou.isFormData(e))return r?JSON.stringify(Lu(e)):e;if(ou.isArrayBuffer(e)||ou.isBuffer(e)||ou.isStream(e)||ou.isFile(e)||ou.isBlob(e)||ou.isReadableStream(e))return e;if(ou.isArrayBufferView(e))return e.buffer;if(ou.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return mu(e,new Tu.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Tu.isNode&&ou.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((i=ou.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return mu(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(ou.isString(e))try{return(t||JSON.parse)(e),ou.trim(e)}catch(Ap){if("SyntaxError"!==Ap.name)throw Ap}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Ru.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(ou.isResponse(e)||ou.isReadableStream(e))return e;if(e&&ou.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Ap){if(n){if("SyntaxError"===Ap.name)throw uu.from(Ap,uu.ERR_BAD_RESPONSE,this,null,this.response);throw Ap}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Tu.classes.FormData,Blob:Tu.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ou.forEach(["delete","get","head","post","put","patch"],e=>{Ru.headers[e]={}});const Mu=Ru,_u=ou.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Du=Symbol("internals");function Ou(e){return e&&String(e).trim().toLowerCase()}function Iu(e){return!1===e||null==e?e:ou.isArray(e)?e.map(Iu):String(e)}function Vu(e,t,n,r,a){return ou.isFunction(r)?r.call(this,t,n):(a&&(t=n),ou.isString(t)?ou.isString(r)?-1!==t.indexOf(r):ou.isRegExp(r)?r.test(t):void 0:void 0)}class Fu{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=Ou(t);if(!a)throw new Error("header name must be a non-empty string");const i=ou.findKey(r,a);(!i||void 0===r[i]||!0===n||void 0===n&&!1!==r[i])&&(r[i||t]=Iu(e))}const i=(e,t)=>ou.forEach(e,(e,n)=>a(e,n,t));if(ou.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if(ou.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))i((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&_u[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(ou.isObject(e)&&ou.isIterable(e)){let n,r,a={};for(const t of e){if(!ou.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?ou.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}i(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=Ou(e)){const n=ou.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(ou.isFunction(t))return t.call(this,e,n);if(ou.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ou(e)){const n=ou.findKey(this,e);return!(!n||void 0===this[n]||t&&!Vu(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=Ou(e)){const a=ou.findKey(n,e);!a||t&&!Vu(0,n[a],a,t)||(delete n[a],r=!0)}}return ou.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Vu(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return ou.forEach(this,(r,a)=>{const i=ou.findKey(n,a);if(i)return t[i]=Iu(r),void delete t[a];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();o!==a&&delete t[a],t[o]=Iu(r),n[o]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return ou.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&ou.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[Du]=this[Du]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Ou(e);t[r]||(!function(e,t){const n=ou.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return ou.isArray(e)?e.forEach(r):r(e),this}}Fu.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ou.reduceDescriptors(Fu.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),ou.freezeMethods(Fu);const Bu=Fu;function zu(e,t){const n=this||Mu,r=t||n,a=Bu.from(r.headers);let i=r.data;return ou.forEach(e,function(e){i=e.call(n,i,a.normalize(),t?t.status:void 0)}),a.normalize(),i}function Uu(e){return!(!e||!e.__CANCEL__)}function Wu(e,t,n){uu.call(this,null==e?"canceled":e,uu.ERR_CANCELED,t,n),this.name="CanceledError"}ou.inherits(Wu,uu,{__CANCEL__:!0});const Hu=Wu;function qu(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new uu("Request failed with status code "+n.status,[uu.ERR_BAD_REQUEST,uu.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const $u=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,i=0,o=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),c=r[o];a||(a=l),n[i]=s,r[i]=l;let u=o,d=0;for(;u!==i;)d+=n[u++],u%=e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),l-a<t)return;const f=c&&l-c;return f?Math.round(1e3*d/f):void 0}};const Qu=function(e,t){let n,r,a=0,i=1e3/t;const o=function(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=i,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var s=arguments.length,l=new Array(s),c=0;c<s;c++)l[c]=arguments[c];t>=i?o(l,e):(n=l,r||(r=setTimeout(()=>{r=null,o(n)},i-t)))},()=>n&&o(n)]},Gu=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=$u(50,250);return Qu(n=>{const i=n.loaded,o=n.lengthComputable?n.total:void 0,s=i-r,l=a(s);r=i;e({loaded:i,total:o,progress:o?i/o:void 0,bytes:s,rate:l||void 0,estimated:l&&o&&i<=o?(o-i)/l:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})},n)},Zu=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ku=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return ou.asap(()=>e(...n))},Yu=Tu.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Tu.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Tu.origin),Tu.navigator&&/(msie|trident)/i.test(Tu.navigator.userAgent)):()=>!0,Xu=Tu.hasStandardBrowserEnv?{write(e,t,n,r,a,i){const o=[e+"="+encodeURIComponent(t)];ou.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),ou.isString(r)&&o.push("path="+r),ou.isString(a)&&o.push("domain="+a),!0===i&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ju(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const ed=e=>e instanceof Bu?Oe({},e):e;function td(e,t){t=t||{};const n={};function r(e,t,n,r){return ou.isPlainObject(e)&&ou.isPlainObject(t)?ou.merge.call({caseless:r},e,t):ou.isPlainObject(t)?ou.merge({},t):ou.isArray(t)?t.slice():t}function a(e,t,n,a){return ou.isUndefined(t)?ou.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function i(e,t){if(!ou.isUndefined(t))return r(void 0,t)}function o(e,t){return ou.isUndefined(t)?ou.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,a,i){return i in t?r(n,a):i in e?r(void 0,n):void 0}const l={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:s,headers:(e,t,n)=>a(ed(e),ed(t),0,!0)};return ou.forEach(Object.keys(Object.assign({},e,t)),function(r){const i=l[r]||a,o=i(e[r],t[r],r);ou.isUndefined(o)&&i!==s||(n[r]=o)}),n}const nd=e=>{const t=td({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:i,xsrfCookieName:o,headers:s,auth:l}=t;if(t.headers=s=Bu.from(s),t.url=wu(Ju(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),ou.isFormData(r))if(Tu.hasStandardBrowserEnv||Tu.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}if(Tu.hasStandardBrowserEnv&&(a&&ou.isFunction(a)&&(a=a(t)),a||!1!==a&&Yu(t.url))){const e=i&&o&&Xu.read(o);e&&s.set(i,e)}return t},rd="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=nd(e);let a=r.data;const i=Bu.from(r.headers).normalize();let o,s,l,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:h}=r;function p(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Bu.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());qu(function(e){t(e),p()},function(e){n(e),p()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new uu("Request aborted",uu.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new uu("Network Error",uu.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Su;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new uu(t,a.clarifyTimeoutError?uu.ETIMEDOUT:uu.ECONNABORTED,e,m)),m=null},void 0===a&&i.setContentType(null),"setRequestHeader"in m&&ou.forEach(i.toJSON(),function(e,t){m.setRequestHeader(t,e)}),ou.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),h&&([l,u]=Gu(h,!0),m.addEventListener("progress",l)),f&&m.upload&&([s,c]=Gu(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(o=t=>{m&&(n(!t||t.type?new Hu(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Tu.protocols.indexOf(y)?n(new uu("Unsupported protocol "+y+":",uu.ERR_BAD_REQUEST,e)):m.send(a||null)})},ad=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof uu?t:new Hu(t instanceof Error?t.message:t))}};let i=t&&setTimeout(()=>{i=null,a(new uu("timeout ".concat(t," of ms exceeded"),uu.ETIMEDOUT))},t);const o=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:s}=r;return s.unsubscribe=()=>ou.asap(o),s}};function id(e,t){this.v=e,this.k=t}function od(e){return function(){return new sd(e.apply(this,arguments))}}function sd(e){var t,n;function r(t,n){try{var i=e[t](n),o=i.value,s=o instanceof id;Promise.resolve(s?o.v:o).then(function(n){if(s){var l="return"===t?"return":"next";if(!o.k||n.done)return r(l,n);n=e[l](n).value}a(i.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(i,o){var s={key:e,arg:a,resolve:i,reject:o,next:null};n?n=n.next=s:(t=n=s,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function ld(e){return new id(e,0)}function cd(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new id(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function ud(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new dd(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function dd(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return dd=function(e){this.s=e,this.n=e.next},dd.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new dd(e)}sd.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},sd.prototype.next=function(e){return this._invoke("next",e)},sd.prototype.throw=function(e){return this._invoke("throw",e)},sd.prototype.return=function(e){return this._invoke("return",e)};const fd=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},hd=function(){var e=od(function*(e,t){var n,r=!1,a=!1;try{for(var i,o=ud(pd(e));r=!(i=yield ld(o.next())).done;r=!1){const e=i.value;yield*cd(ud(fd(e,t)))}}catch(s){a=!0,n=s}finally{try{r&&null!=o.return&&(yield ld(o.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),pd=function(){var e=od(function*(e){if(e[Symbol.asyncIterator])return void(yield*cd(ud(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield ld(t.read());if(e)break;yield n}}finally{yield ld(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),md=(e,t,n,r)=>{const a=hd(e,t);let i,o=0,s=e=>{i||(i=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return s(),void e.close();let i=r.byteLength;if(n){let e=o+=i;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw s(t),t}},cancel:e=>(s(e),a.return())},{highWaterMark:2})},gd="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,yd=gd&&"function"===typeof ReadableStream,vd=gd&&("function"===typeof TextEncoder?(xd=new TextEncoder,e=>xd.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var xd;const bd=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(Ap){return!1}},wd=yd&&bd(()=>{let e=!1;const t=new Request(Tu.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),kd=yd&&bd(()=>ou.isReadableStream(new Response("").body)),Sd={stream:kd&&(e=>e.body)};var jd;gd&&(jd=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Sd[e]&&(Sd[e]=ou.isFunction(jd[e])?t=>t[e]():(t,n)=>{throw new uu("Response type '".concat(e,"' is not supported"),uu.ERR_NOT_SUPPORT,n)})}));const Ed=async(e,t)=>{const n=ou.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(ou.isBlob(e))return e.size;if(ou.isSpecCompliantForm(e)){const t=new Request(Tu.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return ou.isArrayBufferView(e)||ou.isArrayBuffer(e)?e.byteLength:(ou.isURLSearchParams(e)&&(e+=""),ou.isString(e)?(await vd(e)).byteLength:void 0)})(t):n},Nd=gd&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:i,timeout:o,onDownloadProgress:s,onUploadProgress:l,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:f}=nd(e);c=c?(c+"").toLowerCase():"text";let h,p=ad([a,i&&i.toAbortSignal()],o);const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let g;try{if(l&&wd&&"get"!==n&&"head"!==n&&0!==(g=await Ed(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(ou.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=Zu(g,Gu(Ku(l)));r=md(n.body,65536,e,t)}}ou.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;h=new Request(t,Oe(Oe({},f),{},{signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let i=await fetch(h,f);const o=kd&&("stream"===c||"response"===c);if(kd&&(s||o&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=i[t]});const t=ou.toFiniteNumber(i.headers.get("content-length")),[n,r]=s&&Zu(t,Gu(Ku(s),!0))||[];i=new Response(md(i.body,65536,n,()=>{r&&r(),m&&m()}),e)}c=c||"text";let y=await Sd[ou.findKey(Sd,c)||"text"](i,e);return!o&&m&&m(),await new Promise((t,n)=>{qu(t,n,{data:y,headers:Bu.from(i.headers),status:i.status,statusText:i.statusText,config:e,request:h})})}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new uu("Network Error",uu.ERR_NETWORK,e,h),{cause:y.cause||y});throw uu.from(y,y&&y.code,e,h)}}),Cd={http:null,xhr:rd,fetch:Nd};ou.forEach(Cd,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Ap){}Object.defineProperty(e,"adapterName",{value:t})}});const Pd=e=>"- ".concat(e),Ad=e=>ou.isFunction(e)||null===e||!1===e,Td=e=>{e=ou.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let i=0;i<t;i++){let t;if(n=e[i],r=n,!Ad(n)&&(r=Cd[(t=String(n)).toLowerCase()],void 0===r))throw new uu("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+i]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(Pd).join("\n"):" "+Pd(e[0]):"as no adapter specified";throw new uu("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Ld(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Hu(null,e)}function Rd(e){Ld(e),e.headers=Bu.from(e.headers),e.data=zu.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Td(e.adapter||Mu.adapter)(e).then(function(t){return Ld(e),t.data=zu.call(e,e.transformResponse,t),t.headers=Bu.from(t.headers),t},function(t){return Uu(t)||(Ld(e),t&&t.response&&(t.response.data=zu.call(e,e.transformResponse,t.response),t.response.headers=Bu.from(t.response.headers))),Promise.reject(t)})}const Md="1.10.0",_d={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{_d[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Dd={};_d.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Md+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,i)=>{if(!1===e)throw new uu(r(a," has been removed"+(t?" in "+t:"")),uu.ERR_DEPRECATED);return t&&!Dd[a]&&(Dd[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,i)}},_d.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const Od={assertOptions:function(e,t,n){if("object"!==typeof e)throw new uu("options must be an object",uu.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const i=r[a],o=t[i];if(o){const t=e[i],n=void 0===t||o(t,i,e);if(!0!==n)throw new uu("option "+i+" must be "+n,uu.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new uu("Unknown option "+i,uu.ERR_BAD_OPTION)}},validators:_d},Id=Od.validators;class Vd{constructor(e){this.defaults=e||{},this.interceptors={request:new ku,response:new ku}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Ap){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=td(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&Od.assertOptions(n,{silentJSONParsing:Id.transitional(Id.boolean),forcedJSONParsing:Id.transitional(Id.boolean),clarifyTimeoutError:Id.transitional(Id.boolean)},!1),null!=r&&(ou.isFunction(r)?t.paramsSerializer={serialize:r}:Od.assertOptions(r,{encode:Id.function,serialize:Id.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Od.assertOptions(t,{baseUrl:Id.spelling("baseURL"),withXsrfToken:Id.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=a&&ou.merge(a.common,a[t.method]);a&&ou.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=Bu.concat(i,a);const o=[];let s=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,o.unshift(e.fulfilled,e.rejected))});const l=[];let c;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u,d=0;if(!s){const e=[Rd.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,l),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=o.length;let f=t;for(d=0;d<u;){const e=o[d++],t=o[d++];try{f=e(f)}catch(h){t.call(this,h);break}}try{c=Rd.call(this,f)}catch(h){return Promise.reject(h)}for(d=0,u=l.length;d<u;)c=c.then(l[d++],l[d++]);return c}getUri(e){return wu(Ju((e=td(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}ou.forEach(["delete","get","head","options"],function(e){Vd.prototype[e]=function(t,n){return this.request(td(n||{},{method:e,url:t,data:(n||{}).data}))}}),ou.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(td(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Vd.prototype[e]=t(),Vd.prototype[e+"Form"]=t(!0)});const Fd=Vd;class Bd{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new Hu(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Bd(function(t){e=t}),cancel:e}}}const zd=Bd;const Ud={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ud).forEach(e=>{let[t,n]=e;Ud[n]=t});const Wd=Ud;const Hd=function e(t){const n=new Fd(t),r=wc(Fd.prototype.request,n);return ou.extend(r,Fd.prototype,n,{allOwnKeys:!0}),ou.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(td(t,n))},r}(Mu);Hd.Axios=Fd,Hd.CanceledError=Hu,Hd.CancelToken=zd,Hd.isCancel=Uu,Hd.VERSION=Md,Hd.toFormData=mu,Hd.AxiosError=uu,Hd.Cancel=Hd.CanceledError,Hd.all=function(e){return Promise.all(e)},Hd.spread=function(e){return function(t){return e.apply(null,t)}},Hd.isAxiosError=function(e){return ou.isObject(e)&&!0===e.isAxiosError},Hd.mergeConfig=td,Hd.AxiosHeaders=Bu,Hd.formToJSON=e=>Lu(ou.isHTMLForm(e)?new FormData(e):e),Hd.getAdapter=Td,Hd.HttpStatusCode=Wd,Hd.default=Hd;const qd={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:8000/api/v1",$d=Hd.create({baseURL:qd,timeout:1e4,headers:{"Content-Type":"application/json"}});$d.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),$d.interceptors.response.use(e=>e,e=>{var t;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)});const Qd=e=>$d.post("/contact/send",e),Gd=e=>$d.get("/projects",{params:e}),Zd=["title","titleId"];function Kd(e,t){let{title:n,titleId:r}=e,a=Gt(e,Zd);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))}const Yd=i.forwardRef(Kd),Xd=()=>{const[e,t]=(0,i.useState)([]),[n,r]=(0,i.useState)("All"),[a,o]=(0,i.useState)(!0);(0,i.useEffect)(()=>{s(),l()},[]);const s=async()=>{try{const e=await Gd();t(e.data)}catch(e){console.error("Error fetching projects:",e)}finally{o(!1)}},l=async()=>{},c="All"===n?e:e.filter(e=>e.category===n),u={initial:{opacity:0,y:60},animate:{opacity:1,y:0}},d=e.length>0?c:[{id:1,title:"Semantic Video Search Engine",description:"Scalable semantic video search system combining transcription, embeddings, and vector search using AWS services",technologies:"AWS Transcribe, SageMaker, OpenSearch, Bedrock, Python",github_url:"https://github.com/mrgarg-g1/semantic-video-search",live_url:"",image_url:"/images/projects/semantic-video-search.jpg",category:"AI/ML",featured:!0,created_at:"2025-01-15"},{id:2,title:"Mentor Policy Chatbot",description:"Fine-tuned LLaMA model with RAG pipeline for government schemes and education policy assistance",technologies:"Meta LLaMA 3.1-8B, Transformers, LangChain, LoRA",github_url:"https://github.com/mrgarg-g1/policy-chatbot",live_url:"",image_url:"/images/projects/policy-chatbot.jpg",category:"AI/ML",featured:!0,created_at:"2025-01-10"},{id:3,title:"Facial Recognition Attendance System",description:"Real-time facial recognition system for employee attendance with 95% accuracy using CNN models",technologies:"OpenCV, TensorFlow, Flask, MySQL, Power BI",github_url:"https://github.com/mrgarg-g1/face-recognition-attendance",live_url:"",image_url:"/images/projects/facial-recognition.jpg",category:"AI/ML",featured:!1,created_at:"2023-06-15"},{id:4,title:"Document Classification Pipeline",description:"BERT-based document classification system for legal and insurance documents with 92% accuracy",technologies:"BERT, SpaCy, NER, Flask APIs, Python",github_url:"https://github.com/mrgarg-g1/document-classifier",live_url:"",image_url:"/images/projects/document-classification.jpg",category:"AI/ML",featured:!1,created_at:"2024-03-20"},{id:5,title:"Inventory Optimization Dashboard",description:"Dynamic dashboard for inventory optimization with time-series forecasting and procurement planning",technologies:"Power BI, Python, ARIMA, Prophet, Streamlit",github_url:"https://github.com/mrgarg-g1/inventory-optimization",live_url:"",image_url:"/images/projects/inventory-dashboard.jpg",category:"Data Science",featured:!1,created_at:"2024-01-10"},{id:6,title:"Automated Web Scraping Framework",description:"ETL pipelines with automated data validation and incremental processing using Airflow",technologies:"Selenium, BeautifulSoup, Pandas, SQLAlchemy, Airflow",github_url:"https://github.com/mrgarg-g1/web-scraping-framework",live_url:"",image_url:"/images/projects/web-scraping.jpg",category:"Data Engineering",featured:!1,created_at:"2024-08-15"}];return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:u,className:"text-center space-y-6",children:[(0,il.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white",children:["My ",(0,il.jsx)("span",{className:"text-gradient",children:"AI & Data Science Portfolio"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Explore my AI/ML projects, data science solutions, and real-world implementations that drive business value"})]})})}),(0,il.jsx)("section",{className:"py-8 bg-white dark:bg-dark-900",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsx)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:u,className:"text-center",children:(0,il.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:["All","AI/ML","Data Science","Data Engineering"].map(e=>(0,il.jsx)("button",{onClick:()=>r(e),className:"px-6 py-3 rounded-full font-medium transition-all duration-300 ".concat(n===e?"bg-primary-600 text-white shadow-lg":"bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-dark-600"),children:e},e))})})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:a?(0,il.jsx)("div",{className:"text-center py-12",children:(0,il.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"})}):(0,il.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:d.map((e,t)=>(0,il.jsxs)($s.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card overflow-hidden group",children:[(0,il.jsxs)("div",{className:"relative overflow-hidden",children:[(0,il.jsx)("img",{src:e.image_url||"/api/placeholder/600/400",alt:e.title,className:"w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"}),(0,il.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,il.jsxs)("div",{className:"absolute bottom-4 left-4 right-4 flex space-x-2",children:[e.github_url&&(0,il.jsx)("a",{href:e.github_url,target:"_blank",rel:"noopener noreferrer",className:"bg-white/20 backdrop-blur-sm p-2 rounded-full text-white hover:bg-white/30 transition-all duration-300",children:(0,il.jsx)(Ll,{className:"h-5 w-5"})}),e.live_url&&(0,il.jsx)("a",{href:e.live_url,target:"_blank",rel:"noopener noreferrer",className:"bg-white/20 backdrop-blur-sm p-2 rounded-full text-white hover:bg-white/30 transition-all duration-300",children:(0,il.jsx)(Yd,{className:"h-5 w-5"})})]})})]}),(0,il.jsxs)("div",{className:"p-6 space-y-4",children:[(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.title}),e.featured&&(0,il.jsx)("span",{className:"bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 px-2 py-1 rounded-full text-xs font-medium",children:"Featured"})]}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:e.description}),(0,il.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.split(", ").map((e,t)=>(0,il.jsx)("span",{className:"bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full text-xs",children:e},t))})]})]},e.id))})})})]})},Jd=["title","titleId"];function ef(e,t){let{title:n,titleId:r}=e,a=Gt(e,Jd);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const tf=i.forwardRef(ef),nf=["title","titleId"];function rf(e,t){let{title:n,titleId:r}=e,a=Gt(e,nf);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const af=i.forwardRef(rf),of=["title","titleId"];function sf(e,t){let{title:n,titleId:r}=e,a=Gt(e,of);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))}const lf=i.forwardRef(sf),cf=()=>{const e={initial:{opacity:0,y:60},animate:{opacity:1,y:0}},t=[{icon:(0,il.jsx)(kl,{className:"h-8 w-8"}),title:"AI/ML Model Development",description:"Custom machine learning solutions from concept to deployment. Specializing in LLM fine-tuning, computer vision, NLP, and predictive analytics.",features:["LLM Fine-tuning (LLaMA, GPT)","Computer Vision & OCR","NLP & Sentiment Analysis","Predictive Analytics"]},{icon:(0,il.jsx)(El,{className:"h-8 w-8"}),title:"Multi-Cloud ML Architecture",description:"Vendor-agnostic ML systems across AWS, Azure, and Google Cloud with automated failover and cost optimization.",features:["AWS SageMaker & Bedrock","Azure ML & Cognitive Services","Google Cloud AI Platform","Cross-cloud Deployment"]},{icon:(0,il.jsx)(ic,{className:"h-8 w-8"}),title:"Intelligent Data Engineering",description:"End-to-end data pipelines with automated validation, monitoring, and self-healing capabilities.",features:["ETL Pipeline Automation","Data Quality Validation","Real-time Processing","Airflow & Orchestration"]},{icon:(0,il.jsx)(tf,{className:"h-8 w-8"}),title:"Document Intelligence & OCR",description:"Extract, classify, and analyze documents at scale using advanced OCR, NLP, and computer vision techniques.",features:["Document Classification","Text Extraction & OCR","Entity Recognition (NER)","Compliance Automation"]},{icon:(0,il.jsx)(af,{className:"h-8 w-8"}),title:"Predictive Analytics & Forecasting",description:"Time-series forecasting, churn prediction, and demand planning using advanced statistical models and ML techniques.",features:["Time-series Forecasting","Customer Churn Prediction","Inventory Optimization","Risk Assessment"]},{icon:(0,il.jsx)(lf,{className:"h-8 w-8"}),title:"AI Strategy & Consulting",description:"Strategic AI roadmap development, technology assessment, and implementation planning for enterprise-grade solutions.",features:["AI Strategy Development","Technology Assessment","ROI Analysis","Implementation Planning"]}],n=[{icon:(0,il.jsx)(Pl,{className:"h-6 w-6"}),title:"Problem Analysis",description:"Understanding your business challenges and identifying AI/ML opportunities with measurable ROI."},{icon:(0,il.jsx)(ic,{className:"h-6 w-6"}),title:"Data Assessment & POC",description:"Evaluating data quality, building proof-of-concept models, and validating technical feasibility."},{icon:(0,il.jsx)(kl,{className:"h-6 w-6"}),title:"Model Development",description:"Building, training, and fine-tuning AI models using cutting-edge techniques and best practices."},{icon:(0,il.jsx)(El,{className:"h-6 w-6"}),title:"Deployment & Scaling",description:"Deploying models to production with monitoring, scaling, and automated retraining pipelines."},{icon:(0,il.jsx)(af,{className:"h-6 w-6"}),title:"Monitoring & Optimization",description:"Continuous monitoring, performance optimization, and iterative improvements for maximum impact."}];return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:e,className:"text-center space-y-6",children:[(0,il.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white",children:["AI/ML ",(0,il.jsx)("span",{className:"text-gradient",children:"Services"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Comprehensive AI and machine learning solutions to transform your business with intelligent automation and data-driven insights"})]})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map((e,t)=>(0,il.jsxs)($s.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card p-8 text-center space-y-6 group",children:[(0,il.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full text-primary-600 dark:text-primary-400 group-hover:bg-primary-600 group-hover:text-white transition-all duration-300",children:e.icon}),(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.title}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.description}),(0,il.jsx)("ul",{className:"space-y-2",children:e.features.map((e,t)=>(0,il.jsxs)("li",{className:"text-sm text-gray-500 dark:text-gray-400",children:["\u2713 ",e]},t))})]},e.title))})})}),(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsxs)("div",{className:"container-custom",children:[(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"text-center space-y-6 mb-12",children:[(0,il.jsxs)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white",children:["My AI/ML ",(0,il.jsx)("span",{className:"text-gradient",children:"Process"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"A proven methodology to deliver AI solutions that create measurable business value"})]}),(0,il.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8",children:n.map((e,t)=>(0,il.jsxs)($s.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"text-center space-y-4",children:[(0,il.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-primary-600 rounded-full text-white font-bold text-lg",children:t+1}),(0,il.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-white dark:bg-dark-700 rounded-full text-primary-600 dark:text-primary-400 shadow-lg",children:e.icon}),(0,il.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.title}),(0,il.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.description})]},e.title))})]})}),(0,il.jsx)("section",{className:"section-padding bg-primary-600 dark:bg-primary-700",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"text-center space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white",children:"Ready to Transform Your Business with AI?"}),(0,il.jsx)("p",{className:"text-xl text-primary-100 max-w-2xl mx-auto",children:"Let's discuss how AI and machine learning can solve your biggest challenges and drive growth."}),(0,il.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,il.jsx)("a",{href:"/contact",className:"bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl",children:"Discuss Your AI Project"}),(0,il.jsx)("a",{href:"/portfolio",className:"border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-8 rounded-lg transition-all duration-300",children:"View AI/ML Projects"})]})]})})})]})},uf=["title","titleId"];function df(e,t){let{title:n,titleId:r}=e,a=Gt(e,uf);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))}const ff=i.forwardRef(df),hf=["title","titleId"];function pf(e,t){let{title:n,titleId:r}=e,a=Gt(e,hf);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))}const mf=i.forwardRef(pf),gf=()=>{const e={initial:{opacity:0,y:60},animate:{opacity:1,y:0}};return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:e,className:"text-center space-y-6",children:[(0,il.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white",children:["My ",(0,il.jsx)("span",{className:"text-gradient",children:"Resume"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"A comprehensive overview of my professional journey and technical expertise"}),(0,il.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,il.jsx)("a",{href:"/resume/Deepak-Garg-Resume.pdf",download:"Deepak-Garg-Resume.pdf",className:"btn-primary",children:"Download PDF"}),(0,il.jsx)("button",{onClick:()=>{const e=window.open("/resume/Deepak-Garg-Resume.pdf","_blank");e?e.onload=()=>{e.print()}:window.open("/resume/Deepak-Garg-Resume.pdf","_blank")},className:"btn-outline",children:"Print Resume"})]})]})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white text-center",children:"Work Experience"}),(0,il.jsx)("div",{className:"space-y-8",children:[{company:"Tech Solutions Inc.",position:"Senior Full Stack Developer",location:"Mumbai, India",duration:"Jan 2022 - Present",current:!0,description:["Led development of scalable web applications using React, Node.js, and Python","Implemented microservices architecture reducing system response time by 40%","Mentored junior developers and conducted code reviews","Collaborated with cross-functional teams to deliver high-quality products"]},{company:"Digital Innovations Ltd.",position:"Full Stack Developer",location:"Mumbai, India",duration:"Jun 2020 - Dec 2021",current:!1,description:["Developed and maintained multiple client-facing web applications","Integrated third-party APIs and payment gateways","Optimized database queries improving application performance by 30%","Participated in agile development processes and sprint planning"]},{company:"StartupXYZ",position:"Frontend Developer",location:"Mumbai, India",duration:"Jan 2019 - May 2020",current:!1,description:["Built responsive web applications using React and modern CSS","Implemented state management solutions with Redux","Collaborated with designers to create pixel-perfect UI components","Wrote comprehensive unit tests achieving 90% code coverage"]}].map((e,t)=>(0,il.jsx)($s.div,{initial:{opacity:0,x:-60},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card p-8 relative",children:(0,il.jsxs)("div",{className:"flex flex-col md:flex-row md:items-start gap-6",children:[(0,il.jsx)("div",{className:"flex-shrink-0",children:(0,il.jsx)("div",{className:"w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center",children:(0,il.jsx)(ff,{className:"h-8 w-8 text-primary-600 dark:text-primary-400"})})}),(0,il.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,il.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.position}),(0,il.jsx)("p",{className:"text-primary-600 dark:text-primary-400 font-semibold",children:e.company})]}),e.current&&(0,il.jsx)("span",{className:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium",children:"Current"})]}),(0,il.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(mf,{className:"h-4 w-4"}),e.duration]}),(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(dl,{className:"h-4 w-4"}),e.location]})]}),(0,il.jsx)("ul",{className:"space-y-2",children:e.description.map((e,t)=>(0,il.jsxs)("li",{className:"flex items-start gap-2 text-gray-600 dark:text-gray-400",children:[(0,il.jsx)(dc,{className:"h-5 w-5 text-primary-600 dark:text-primary-400 flex-shrink-0 mt-0.5"}),e]},t))})]})]})},t))})]})})}),(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white text-center",children:"Education"}),(0,il.jsx)("div",{className:"space-y-8",children:[{institution:"Mumbai University",degree:"Bachelor of Technology",field:"Computer Science Engineering",duration:"2015 - 2019",description:"Specialized in software engineering and web technologies",achievements:["First Class with Distinction","Best Final Year Project Award","Active member of Computer Society"]},{institution:"ABC Junior College",degree:"Higher Secondary Certificate",field:"Science (PCM)",duration:"2013 - 2015",description:"Science stream with Mathematics, Physics, and Chemistry",achievements:["85% in HSC examination","Mathematics Olympiad participant","Science club member"]}].map((e,t)=>(0,il.jsx)($s.div,{initial:{opacity:0,x:60},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card p-8",children:(0,il.jsxs)("div",{className:"flex flex-col md:flex-row md:items-start gap-6",children:[(0,il.jsx)("div",{className:"flex-shrink-0",children:(0,il.jsx)("div",{className:"w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center",children:(0,il.jsx)(lf,{className:"h-8 w-8 text-secondary-600 dark:text-secondary-400"})})}),(0,il.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.degree}),(0,il.jsx)("p",{className:"text-secondary-600 dark:text-secondary-400 font-semibold",children:e.institution}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.field})]}),(0,il.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400",children:[(0,il.jsx)(mf,{className:"h-4 w-4"}),e.duration]}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.description}),(0,il.jsx)("ul",{className:"space-y-1",children:e.achievements.map((e,t)=>(0,il.jsxs)("li",{className:"flex items-start gap-2 text-gray-600 dark:text-gray-400",children:[(0,il.jsx)(dc,{className:"h-4 w-4 text-secondary-600 dark:text-secondary-400 flex-shrink-0 mt-0.5"}),e]},t))})]})]})},t))})]})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white text-center",children:"Technical Skills"}),(0,il.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{category:"Frontend",items:["React","TypeScript","Next.js","Tailwind CSS","Vue.js"]},{category:"Backend",items:["Node.js","Python","FastAPI","Express.js","Django"]},{category:"Database",items:["PostgreSQL","MongoDB","Redis","SQLite","MySQL"]},{category:"Cloud & DevOps",items:["AWS","Docker","Kubernetes","CI/CD","Terraform"]},{category:"Tools & Others",items:["Git","Jest","Webpack","Figma","Linux"]}].map((e,t)=>(0,il.jsxs)($s.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card p-6",children:[(0,il.jsx)("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-4",children:e.category}),(0,il.jsx)("div",{className:"flex flex-wrap gap-2",children:e.items.map((e,t)=>(0,il.jsx)("span",{className:"bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm",children:e},t))})]},e.category))})]})})}),(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white text-center",children:"Certifications"}),(0,il.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[{name:"AWS Certified Solutions Architect",issuer:"Amazon Web Services",date:"2023",badge:"aws"},{name:"Google Cloud Professional Developer",issuer:"Google Cloud",date:"2022",badge:"gcp"},{name:"React Developer Certification",issuer:"Meta",date:"2021",badge:"react"}].map((e,t)=>(0,il.jsxs)($s.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card p-6 text-center",children:[(0,il.jsx)("div",{className:"w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,il.jsx)(dc,{className:"h-8 w-8 text-primary-600 dark:text-primary-400"})}),(0,il.jsx)("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-2",children:e.name}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-1",children:e.issuer}),(0,il.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.date})]},t))})]})})})]})},yf=["title","titleId"];function vf(e,t){let{title:n,titleId:r}=e,a=Gt(e,yf);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const xf=i.forwardRef(vf),bf=()=>{const e={initial:{opacity:0,y:60},animate:{opacity:1,y:0}},t=[{id:1,title:"Fine-tuning LLaMA 3.1 for Government Policy Q&A: A Complete Guide",excerpt:"Deep dive into fine-tuning Meta's LLaMA 3.1-8B model for government policy understanding. Learn about LoRA techniques, RAG pipelines, and achieving 92% accuracy in policy Q&A systems.",slug:"fine-tuning-llama-government-policy-qa",image_url:"/images/blog/llama-fine-tuning.jpg",published:!0,created_at:"2024-01-20T10:00:00Z",read_time:"12 min read",category:"AI/ML",author:"Deepak Garg"},{id:2,title:"Building Semantic Video Search with AWS: From Transcription to Discovery",excerpt:"Learn how to architect a scalable semantic video search system using AWS Transcribe, SageMaker embeddings, and OpenSearch. Achieve 95% accuracy in timestamp-based video discovery.",slug:"semantic-video-search-aws-architecture",image_url:"/images/blog/semantic-video-search.jpg",published:!0,created_at:"2024-01-15T14:30:00Z",read_time:"15 min read",category:"Cloud AI",author:"Deepak Garg"},{id:3,title:"Multi-Cloud ML: Deploying Models Across AWS and Azure for Maximum Reliability",excerpt:"Explore strategies for deploying machine learning models across multiple cloud platforms. Real-world case study of a churn prediction system that saves $2M+ annually.",slug:"multi-cloud-ml-deployment-strategy",image_url:"/images/blog/multi-cloud-ml.jpg",published:!0,created_at:"2024-01-10T09:15:00Z",read_time:"18 min read",category:"MLOps",author:"Deepak Garg"},{id:4,title:"From ETL to Intelligence: Building Self-Healing Data Pipelines",excerpt:"Discover how to build robust ETL pipelines with automated validation using Great Expectations and Airflow. Reduce manual intervention by 90% and processing time by 40%.",slug:"self-healing-data-pipelines-airflow",image_url:"/images/blog/etl-pipelines.jpg",published:!0,created_at:"2024-01-05T16:45:00Z",read_time:"14 min read",category:"Data Engineering",author:"Deepak Garg"},{id:5,title:"Computer Vision in Production: Lessons from Real-time Facial Recognition",excerpt:"Building production-ready computer vision systems requires more than just model accuracy. Learn from real-world deployment experiences and optimization strategies.",slug:"computer-vision-production-lessons",image_url:"/images/blog/computer-vision-production.jpg",published:!0,created_at:"2023-12-28T11:20:00Z",read_time:"16 min read",category:"Computer Vision",author:"Deepak Garg"},{id:6,title:"Document AI: From OCR to Intelligent Classification",excerpt:"Transform unstructured documents into actionable data using advanced OCR, NLP, and classification techniques. Real-world implementations and best practices.",slug:"document-ai-ocr-classification",image_url:"/images/blog/document-ai.jpg",published:!0,created_at:"2023-12-20T13:00:00Z",read_time:"11 min read",category:"NLP",author:"Deepak Garg"}],n=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:e,className:"text-center space-y-6",children:[(0,il.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white",children:["AI/ML ",(0,il.jsx)("span",{className:"text-gradient",children:"Insights"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Deep dives into artificial intelligence, machine learning, and data science - from cutting-edge research to real-world implementations"})]})})}),(0,il.jsx)("section",{className:"py-8 bg-white dark:bg-dark-900",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsx)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"text-center",children:(0,il.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:["All","AI/ML","Cloud AI","MLOps","Data Engineering","Computer Vision","NLP"].map(e=>(0,il.jsx)("button",{className:"px-6 py-3 rounded-full font-medium transition-all duration-300 bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:bg-primary-600 hover:text-white",children:e},e))})})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"mb-12",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-8",children:"Featured Post"}),(0,il.jsxs)("div",{className:"card overflow-hidden lg:flex",children:[(0,il.jsx)("div",{className:"lg:w-1/2",children:(0,il.jsx)("img",{src:t[0].image_url,alt:t[0].title,className:"w-full h-64 lg:h-full object-cover"})}),(0,il.jsxs)("div",{className:"lg:w-1/2 p-8 flex flex-col justify-center",children:[(0,il.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,il.jsx)("span",{className:"bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 px-3 py-1 rounded-full text-sm font-medium",children:t[0].category}),(0,il.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:n(t[0].created_at)})]}),(0,il.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:t[0].title}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:t[0].excerpt}),(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400",children:[(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(xf,{className:"h-4 w-4"}),t[0].author]}),(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(pc,{className:"h-4 w-4"}),t[0].read_time]})]}),(0,il.jsx)(Ae,{to:"/blog/".concat(t[0].slug),className:"btn-primary",children:"Read More"})]})]})]})]})})}),(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Latest Posts"}),(0,il.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.slice(1).map((e,t)=>(0,il.jsxs)($s.article,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t},className:"card overflow-hidden group",children:[(0,il.jsxs)("div",{className:"relative overflow-hidden",children:[(0,il.jsx)("img",{src:e.image_url,alt:e.title,className:"w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"}),(0,il.jsx)("div",{className:"absolute top-4 left-4",children:(0,il.jsx)("span",{className:"bg-white/90 dark:bg-dark-900/90 text-gray-900 dark:text-white px-3 py-1 rounded-full text-sm font-medium",children:e.category})})]}),(0,il.jsxs)("div",{className:"p-6 space-y-4",children:[(0,il.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400",children:[(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(mf,{className:"h-4 w-4"}),n(e.created_at)]}),(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(pc,{className:"h-4 w-4"}),e.read_time]})]}),(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300",children:e.title}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:e.excerpt}),(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsxs)("div",{className:"flex items-center gap-2",children:[(0,il.jsx)("div",{className:"w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center",children:(0,il.jsx)(xf,{className:"h-4 w-4 text-primary-600 dark:text-primary-400"})}),(0,il.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.author})]}),(0,il.jsx)(Ae,{to:"/blog/".concat(e.slug),className:"text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm transition-colors duration-300",children:"Read More \u2192"})]})]})]},e.id))})]})})}),(0,il.jsx)("section",{className:"section-padding bg-primary-600 dark:bg-primary-700",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"text-center space-y-8",children:[(0,il.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white",children:"Stay Updated"}),(0,il.jsx)("p",{className:"text-xl text-primary-100 max-w-2xl mx-auto",children:"Subscribe to my newsletter to get the latest articles and insights delivered to your inbox."}),(0,il.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto",children:[(0,il.jsx)("input",{type:"email",placeholder:"Your email address",className:"flex-1 px-4 py-3 rounded-lg border-none focus:ring-2 focus:ring-primary-300 bg-white text-gray-900"}),(0,il.jsx)("button",{className:"bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-all duration-300",children:"Subscribe"})]})]})})})]})},wf=["_f"],kf=["name"],Sf=["_f"],jf=["ref","message","type"],Ef=["formControl"];var Nf=e=>"checkbox"===e.type,Cf=e=>e instanceof Date,Pf=e=>null==e;const Af=e=>"object"===typeof e;var Tf=e=>!Pf(e)&&!Array.isArray(e)&&Af(e)&&!Cf(e),Lf=e=>Tf(e)&&e.target?Nf(e.target)?e.target.checked:e.target.value:e,Rf=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),Mf="undefined"!==typeof window&&"undefined"!==typeof window.HTMLElement&&"undefined"!==typeof document;function _f(e){let t;const n=Array.isArray(e),r="undefined"!==typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(Mf&&(e instanceof Blob||r)||!n&&!Tf(e))return e;if(t=n?[]:{},n||(e=>{const t=e.constructor&&e.constructor.prototype;return Tf(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const n in e)e.hasOwnProperty(n)&&(t[n]=_f(e[n]));else t=e}return t}var Df=e=>/^\w*$/.test(e),Of=e=>void 0===e,If=e=>Array.isArray(e)?e.filter(Boolean):[],Vf=e=>If(e.replace(/["|']|\]/g,"").split(/\.|\[/)),Ff=(e,t,n)=>{if(!t||!Tf(e))return n;const r=(Df(t)?[t]:Vf(t)).reduce((e,t)=>Pf(e)?e:e[t],e);return Of(r)||r===e?Of(e[t])?n:e[t]:r},Bf=e=>"boolean"===typeof e,zf=(e,t,n)=>{let r=-1;const a=Df(t)?[t]:Vf(t),i=a.length,o=i-1;for(;++r<i;){const t=a[r];let i=n;if(r!==o){const n=e[t];i=Tf(n)||Array.isArray(n)?n:isNaN(+a[r+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const Uf="blur",Wf="focusout",Hf="onBlur",qf="onChange",$f="onSubmit",Qf="onTouched",Gf="all",Zf="max",Kf="min",Yf="maxLength",Xf="minLength",Jf="pattern",eh="required",th="validate",nh=i.createContext(null);nh.displayName="HookFormContext";var rh=function(e,t,n){let r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==Gf&&(t._proxyFormState[a]=!r||Gf),n&&(n[a]=!0),e[a]}});return a};const ah="undefined"!==typeof window?i.useLayoutEffect:i.useEffect;var ih=e=>"string"===typeof e,oh=(e,t,n,r,a)=>ih(e)?(r&&t.watch.add(e),Ff(n,e,a)):Array.isArray(e)?e.map(e=>(r&&t.watch.add(e),Ff(n,e))):(r&&(t.watchAll=!0),n);var sh=(e,t,n,r,a)=>t?Oe(Oe({},n[e]),{},{types:Oe(Oe({},n[e]&&n[e].types?n[e].types:{}),{},{[r]:a||!0})}):{},lh=e=>Array.isArray(e)?e:[e],ch=()=>{let e=[];return{get observers(){return e},next:t=>{for(const n of e)n.next&&n.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},uh=e=>Pf(e)||!Af(e);function dh(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new WeakSet;if(uh(e)||uh(t))return e===t;if(Cf(e)&&Cf(t))return e.getTime()===t.getTime();const r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;if(n.has(e)||n.has(t))return!0;n.add(e),n.add(t);for(const i of r){const r=e[i];if(!a.includes(i))return!1;if("ref"!==i){const e=t[i];if(Cf(r)&&Cf(e)||Tf(r)&&Tf(e)||Array.isArray(r)&&Array.isArray(e)?!dh(r,e,n):r!==e)return!1}}return!0}var fh=e=>Tf(e)&&!Object.keys(e).length,hh=e=>"file"===e.type,ph=e=>"function"===typeof e,mh=e=>{if(!Mf)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},gh=e=>"select-multiple"===e.type,yh=e=>"radio"===e.type,vh=e=>mh(e)&&e.isConnected;function xh(e,t){const n=Array.isArray(t)?t:Df(t)?[t]:Vf(t),r=1===n.length?e:function(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=Of(e)?r++:e[t[r++]];return e}(e,n),a=n.length-1,i=n[a];return r&&delete r[i],0!==a&&(Tf(r)&&fh(r)||Array.isArray(r)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!Of(e[t]))return!1;return!0}(r))&&xh(e,n.slice(0,-1)),e}var bh=e=>{for(const t in e)if(ph(e[t]))return!0;return!1};function wh(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=Array.isArray(e);if(Tf(e)||n)for(const r in e)Array.isArray(e[r])||Tf(e[r])&&!bh(e[r])?(t[r]=Array.isArray(e[r])?[]:{},wh(e[r],t[r])):Pf(e[r])||(t[r]=!0);return t}function kh(e,t,n){const r=Array.isArray(e);if(Tf(e)||r)for(const a in e)Array.isArray(e[a])||Tf(e[a])&&!bh(e[a])?Of(t)||uh(n[a])?n[a]=Array.isArray(e[a])?wh(e[a],[]):Oe({},wh(e[a])):kh(e[a],Pf(t)?{}:t[a],n[a]):n[a]=!dh(e[a],t[a]);return n}var Sh=(e,t)=>kh(e,t,wh(t));const jh={value:!1,isValid:!1},Eh={value:!0,isValid:!0};var Nh=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!Of(e[0].attributes.value)?Of(e[0].value)||""===e[0].value?Eh:{value:e[0].value,isValid:!0}:Eh:jh}return jh},Ch=(e,t)=>{let{valueAsNumber:n,valueAsDate:r,setValueAs:a}=t;return Of(e)?e:n?""===e?NaN:e?+e:e:r&&ih(e)?new Date(e):a?a(e):e};const Ph={isValid:!1,value:null};var Ah=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Ph):Ph;function Th(e){const t=e.ref;return hh(t)?t.files:yh(t)?Ah(e.refs).value:gh(t)?[...t.selectedOptions].map(e=>{let{value:t}=e;return t}):Nf(t)?Nh(e.refs).value:Ch(Of(t.value)?e.ref.value:t.value,e)}var Lh=e=>e instanceof RegExp,Rh=e=>Of(e)?e:Lh(e)?e.source:Tf(e)?Lh(e.value)?e.value.source:e.value:e,Mh=e=>({isOnSubmit:!e||e===$f,isOnBlur:e===Hf,isOnChange:e===qf,isOnAll:e===Gf,isOnTouch:e===Qf});const _h="AsyncFunction";var Dh=e=>!!e&&!!e.validate&&!!(ph(e.validate)&&e.validate.constructor.name===_h||Tf(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===_h)),Oh=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const Ih=(e,t,n,r)=>{for(const a of n||Object.keys(e)){const n=Ff(e,a);if(n){const{_f:e}=n,i=Gt(n,wf);if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!r)return!0;if(e.ref&&t(e.ref,e.name)&&!r)return!0;if(Ih(i,t))break}else if(Tf(i)&&Ih(i,t))break}}};function Vh(e,t,n){const r=Ff(e,n);if(r||Df(n))return{error:r,name:n};const a=n.split(".");for(;a.length;){const r=a.join("."),i=Ff(t,r),o=Ff(e,r);if(i&&!Array.isArray(i)&&n!==r)return{name:n};if(o&&o.type)return{name:r,error:o};if(o&&o.root&&o.root.type)return{name:"".concat(r,".root"),error:o.root};a.pop()}return{name:n}}var Fh=(e,t,n)=>{const r=lh(Ff(e,n));return zf(r,"root",t[n]),zf(e,n,r),e},Bh=e=>ih(e);function zh(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"validate";if(Bh(e)||Array.isArray(e)&&e.every(Bh)||Bf(e)&&!e)return{type:n,message:Bh(e)?e:"",ref:t}}var Uh=e=>Tf(e)&&!Lh(e)?e:{value:e,message:""},Wh=async(e,t,n,r,a,i)=>{const{ref:o,refs:s,required:l,maxLength:c,minLength:u,min:d,max:f,pattern:h,validate:p,name:m,valueAsNumber:g,mount:y}=e._f,v=Ff(n,m);if(!y||t.has(m))return{};const x=s?s[0]:o,b=e=>{a&&x.reportValidity&&(x.setCustomValidity(Bf(e)?"":e||""),x.reportValidity())},w={},k=yh(o),S=Nf(o),j=k||S,E=(g||hh(o))&&Of(o.value)&&Of(v)||mh(o)&&""===o.value||""===v||Array.isArray(v)&&!v.length,N=sh.bind(null,m,r,w),C=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Yf,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:Xf;const i=e?t:n;w[m]=Oe({type:e?r:a,message:i,ref:o},N(e?r:a,i))};if(i?!Array.isArray(v)||!v.length:l&&(!j&&(E||Pf(v))||Bf(v)&&!v||S&&!Nh(s).isValid||k&&!Ah(s).isValid)){const{value:e,message:t}=Bh(l)?{value:!!l,message:l}:Uh(l);if(e&&(w[m]=Oe({type:eh,message:t,ref:x},N(eh,t)),!r))return b(t),w}if(!E&&(!Pf(d)||!Pf(f))){let e,t;const n=Uh(f),a=Uh(d);if(Pf(v)||isNaN(v)){const r=o.valueAsDate||new Date(v),i=e=>new Date((new Date).toDateString()+" "+e),s="time"==o.type,l="week"==o.type;ih(n.value)&&v&&(e=s?i(v)>i(n.value):l?v>n.value:r>new Date(n.value)),ih(a.value)&&v&&(t=s?i(v)<i(a.value):l?v<a.value:r<new Date(a.value))}else{const r=o.valueAsNumber||(v?+v:v);Pf(n.value)||(e=r>n.value),Pf(a.value)||(t=r<a.value)}if((e||t)&&(C(!!e,n.message,a.message,Zf,Kf),!r))return b(w[m].message),w}if((c||u)&&!E&&(ih(v)||i&&Array.isArray(v))){const e=Uh(c),t=Uh(u),n=!Pf(e.value)&&v.length>+e.value,a=!Pf(t.value)&&v.length<+t.value;if((n||a)&&(C(n,e.message,t.message),!r))return b(w[m].message),w}if(h&&!E&&ih(v)){const{value:e,message:t}=Uh(h);if(Lh(e)&&!v.match(e)&&(w[m]=Oe({type:Jf,message:t,ref:o},N(Jf,t)),!r))return b(t),w}if(p)if(ph(p)){const e=zh(await p(v,n),x);if(e&&(w[m]=Oe(Oe({},e),N(th,e.message)),!r))return b(e.message),w}else if(Tf(p)){let e={};for(const t in p){if(!fh(e)&&!r)break;const a=zh(await p[t](v,n),x,t);a&&(e=Oe(Oe({},a),N(t,a.message)),b(a.message),r&&(w[m]=e))}if(!fh(e)&&(w[m]=Oe({ref:x},e),!r))return w}return b(!0),w};const Hh={mode:$f,reValidateMode:qf,shouldFocusError:!0};function qh(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=Oe(Oe({},Hh),t),r={submitCount:0,isDirty:!1,isReady:!1,isLoading:ph(n.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:n.errors||{},disabled:n.disabled||!1},a={},i=(Tf(n.defaultValues)||Tf(n.values))&&_f(n.defaultValues||n.values)||{},o=n.shouldUnregister?{}:_f(i),s={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c=0;const u={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let d=Oe({},u);const f={array:ch(),state:ch()},h=n.criteriaMode===Gf,p=async e=>{if(!n.disabled&&(u.isValid||d.isValid||e)){const e=n.resolver?fh((await x()).errors):await b(a,!0);e!==r.isValid&&f.state.next({isValid:e})}},m=(e,t)=>{!n.disabled&&(u.isValidating||u.validatingFields||d.isValidating||d.validatingFields)&&((e||Array.from(l.mount)).forEach(e=>{e&&(t?zf(r.validatingFields,e,t):xh(r.validatingFields,e))}),f.state.next({validatingFields:r.validatingFields,isValidating:!fh(r.validatingFields)}))},g=(e,t,n,r)=>{const l=Ff(a,e);if(l){const a=Ff(o,e,Of(n)?Ff(i,e):n);Of(a)||r&&r.defaultChecked||t?zf(o,e,t?a:Th(l._f)):S(e,a),s.mount&&p()}},y=(e,t,a,o,s)=>{let l=!1,c=!1;const h={name:e};if(!n.disabled){if(!a||o){(u.isDirty||d.isDirty)&&(c=r.isDirty,r.isDirty=h.isDirty=w(),l=c!==h.isDirty);const n=dh(Ff(i,e),t);c=!!Ff(r.dirtyFields,e),n?xh(r.dirtyFields,e):zf(r.dirtyFields,e,!0),h.dirtyFields=r.dirtyFields,l=l||(u.dirtyFields||d.dirtyFields)&&c!==!n}if(a){const t=Ff(r.touchedFields,e);t||(zf(r.touchedFields,e,a),h.touchedFields=r.touchedFields,l=l||(u.touchedFields||d.touchedFields)&&t!==a)}l&&s&&f.state.next(h)}return l?h:{}},v=(t,a,i,o)=>{const s=Ff(r.errors,t),l=(u.isValid||d.isValid)&&Bf(a)&&r.isValid!==a;var h;if(n.delayError&&i?(h=()=>((e,t)=>{zf(r.errors,e,t),f.state.next({errors:r.errors})})(t,i),e=e=>{clearTimeout(c),c=setTimeout(h,e)},e(n.delayError)):(clearTimeout(c),e=null,i?zf(r.errors,t,i):xh(r.errors,t)),(i?!dh(s,i):s)||!fh(o)||l){const e=Oe(Oe(Oe({},o),l&&Bf(a)?{isValid:a}:{}),{},{errors:r.errors,name:t});r=Oe(Oe({},r),e),f.state.next(e)}},x=async e=>{m(e,!0);const t=await n.resolver(o,n.context,((e,t,n,r)=>{const a={};for(const i of e){const e=Ff(t,i);e&&zf(a,i,e._f)}return{criteriaMode:n,names:[...e],fields:a,shouldUseNativeValidation:r}})(e||l.mount,a,n.criteriaMode,n.shouldUseNativeValidation));return m(e),t},b=async function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{valid:!0};for(const i in e){const s=e[i];if(s){const{_f:e}=s,c=Gt(s,Sf);if(e){const c=l.array.has(e.name),d=s._f&&Dh(s._f);d&&u.validatingFields&&m([i],!0);const f=await Wh(s,l.disabled,o,h,n.shouldUseNativeValidation&&!t,c);if(d&&u.validatingFields&&m([i]),f[e.name]&&(a.valid=!1,t))break;!t&&(Ff(f,e.name)?c?Fh(r.errors,f,e.name):zf(r.errors,e.name,f[e.name]):xh(r.errors,e.name))}!fh(c)&&await b(c,t,a)}}return a.valid},w=(e,t)=>!n.disabled&&(e&&t&&zf(o,e,t),!dh(A(),i)),k=(e,t,n)=>oh(e,l,Oe({},s.mount?o:Of(t)?i:ih(e)?{[e]:t}:t),n,t),S=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=Ff(a,e);let i=t;if(r){const n=r._f;n&&(!n.disabled&&zf(o,e,Ch(t,n)),i=mh(n.ref)&&Pf(t)?"":t,gh(n.ref)?[...n.ref.options].forEach(e=>e.selected=i.includes(e.value)):n.refs?Nf(n.ref)?n.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):n.refs.forEach(e=>e.checked=e.value===i):hh(n.ref)?n.ref.value="":(n.ref.value=i,n.ref.type||f.state.next({name:e,values:_f(o)})))}(n.shouldDirty||n.shouldTouch)&&y(e,i,n.shouldTouch,n.shouldDirty,!0),n.shouldValidate&&P(e)},j=(e,t,n)=>{for(const r in t){if(!t.hasOwnProperty(r))return;const i=t[r],o=e+"."+r,s=Ff(a,o);(l.array.has(e)||Tf(i)||s&&!s._f)&&!Cf(i)?j(o,i,n):S(o,i,n)}},E=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const c=Ff(a,e),h=l.array.has(e),p=_f(t);zf(o,e,p),h?(f.array.next({name:e,values:_f(o)}),(u.isDirty||u.dirtyFields||d.isDirty||d.dirtyFields)&&n.shouldDirty&&f.state.next({name:e,dirtyFields:Sh(i,o),isDirty:w(e,p)})):!c||c._f||Pf(p)?S(e,p,n):j(e,p,n),Oh(e,l)&&f.state.next(Oe({},r)),f.state.next({name:s.mount?e:void 0,values:_f(o)})},N=async t=>{s.mount=!0;const i=t.target;let c=i.name,g=!0;const w=Ff(a,c),k=e=>{g=Number.isNaN(e)||Cf(e)&&isNaN(e.getTime())||dh(e,Ff(o,c,e))},S=Mh(n.mode),j=Mh(n.reValidateMode);if(w){let s,N;const C=i.type?Th(w._f):Lf(t),A=t.type===Uf||t.type===Wf,T=!((E=w._f).mount&&(E.required||E.min||E.max||E.maxLength||E.minLength||E.pattern||E.validate))&&!n.resolver&&!Ff(r.errors,c)&&!w._f.deps||((e,t,n,r,a)=>!a.isOnAll&&(!n&&a.isOnTouch?!(t||e):(n?r.isOnBlur:a.isOnBlur)?!e:!(n?r.isOnChange:a.isOnChange)||e))(A,Ff(r.touchedFields,c),r.isSubmitted,j,S),L=Oh(c,l,A);zf(o,c,C),A?(w._f.onBlur&&w._f.onBlur(t),e&&e(0)):w._f.onChange&&w._f.onChange(t);const R=y(c,C,A),M=!fh(R)||L;if(!A&&f.state.next({name:c,type:t.type,values:_f(o)}),T)return(u.isValid||d.isValid)&&("onBlur"===n.mode?A&&p():A||p()),M&&f.state.next(Oe({name:c},L?{}:R));if(!A&&L&&f.state.next(Oe({},r)),n.resolver){const{errors:e}=await x([c]);if(k(C),g){const t=Vh(r.errors,a,c),n=Vh(e,a,t.name||c);s=n.error,c=n.name,N=fh(e)}}else m([c],!0),s=(await Wh(w,l.disabled,o,h,n.shouldUseNativeValidation))[c],m([c]),k(C),g&&(s?N=!1:(u.isValid||d.isValid)&&(N=await b(a,!0)));g&&(w._f.deps&&P(w._f.deps),v(c,N,s,R))}var E},C=(e,t)=>{if(Ff(r.errors,t)&&e.focus)return e.focus(),1},P=async function(e){let t,i,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=lh(e);if(n.resolver){const n=await(async e=>{const{errors:t}=await x(e);if(e)for(const n of e){const e=Ff(t,n);e?zf(r.errors,n,e):xh(r.errors,n)}else r.errors=t;return t})(Of(e)?e:s);t=fh(n),i=e?!s.some(e=>Ff(n,e)):t}else e?(i=(await Promise.all(s.map(async e=>{const t=Ff(a,e);return await b(t&&t._f?{[e]:t}:t)}))).every(Boolean),(i||r.isValid)&&p()):i=t=await b(a);return f.state.next(Oe(Oe(Oe({},!ih(e)||(u.isValid||d.isValid)&&t!==r.isValid?{}:{name:e}),n.resolver||!e?{isValid:t}:{}),{},{errors:r.errors})),o.shouldFocus&&!i&&Ih(a,C,e?s:l.mount),i},A=e=>{const t=Oe({},s.mount?o:i);return Of(e)?t:ih(e)?Ff(t,e):e.map(e=>Ff(t,e))},T=(e,t)=>({invalid:!!Ff((t||r).errors,e),isDirty:!!Ff((t||r).dirtyFields,e),error:Ff((t||r).errors,e),isValidating:!!Ff(r.validatingFields,e),isTouched:!!Ff((t||r).touchedFields,e)}),L=(e,t,n)=>{const i=(Ff(a,e,{_f:{}})._f||{}).ref,o=Ff(r.errors,e)||{},{ref:s,message:l,type:c}=o,u=Gt(o,jf);zf(r.errors,e,Oe(Oe(Oe({},u),t),{},{ref:i})),f.state.next({name:e,errors:r.errors,isValid:!1}),n&&n.shouldFocus&&i&&i.focus&&i.focus()},R=e=>f.state.subscribe({next:t=>{var n,a,i;n=e.name,a=t.name,i=e.exact,n&&a&&n!==a&&!lh(n).some(e=>e&&(i?e===a:e.startsWith(a)||a.startsWith(e)))||!((e,t,n,r)=>{n(e);const{name:a}=e,i=Gt(e,kf);return fh(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!r||Gf))})(t,e.formState||u,B,e.reRenderRoot)||e.callback(Oe(Oe({values:Oe({},o)},r),t))}}).unsubscribe,M=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(const s of e?lh(e):l.mount)l.mount.delete(s),l.array.delete(s),t.keepValue||(xh(a,s),xh(o,s)),!t.keepError&&xh(r.errors,s),!t.keepDirty&&xh(r.dirtyFields,s),!t.keepTouched&&xh(r.touchedFields,s),!t.keepIsValidating&&xh(r.validatingFields,s),!n.shouldUnregister&&!t.keepDefaultValue&&xh(i,s);f.state.next({values:_f(o)}),f.state.next(Oe(Oe({},r),t.keepDirty?{isDirty:w()}:{})),!t.keepIsValid&&p()},_=e=>{let{disabled:t,name:n}=e;(Bf(t)&&s.mount||t||l.disabled.has(n))&&(t?l.disabled.add(n):l.disabled.delete(n))},D=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Ff(a,e);const o=Bf(t.disabled)||Bf(n.disabled);return zf(a,e,Oe(Oe({},r||{}),{},{_f:Oe(Oe({},r&&r._f?r._f:{ref:{name:e}}),{},{name:e,mount:!0},t)})),l.mount.add(e),r?_({disabled:Bf(t.disabled)?t.disabled:n.disabled,name:e}):g(e,!0,t.value),Oe(Oe(Oe({},o?{disabled:t.disabled||n.disabled}:{}),n.progressive?{required:!!t.required,min:Rh(t.min),max:Rh(t.max),minLength:Rh(t.minLength),maxLength:Rh(t.maxLength),pattern:Rh(t.pattern)}:{}),{},{name:e,onChange:N,onBlur:N,ref:o=>{if(o){D(e,t),r=Ff(a,e);const n=Of(o.value)&&o.querySelectorAll&&o.querySelectorAll("input,select,textarea")[0]||o,s=(e=>yh(e)||Nf(e))(n),l=r._f.refs||[];if(s?l.find(e=>e===n):n===r._f.ref)return;zf(a,e,{_f:Oe(Oe({},r._f),s?{refs:[...l.filter(vh),n,...Array.isArray(Ff(i,e))?[{}]:[]],ref:{type:n.type,name:e}}:{ref:n})}),g(e,!1,void 0,n)}else r=Ff(a,e,{}),r._f&&(r._f.mount=!1),(n.shouldUnregister||t.shouldUnregister)&&(!Rf(l.array,e)||!s.action)&&l.unMount.add(e)}})},O=()=>n.shouldFocusError&&Ih(a,C,l.mount),I=(e,t)=>async i=>{let s;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let c=_f(o);if(f.state.next({isSubmitting:!0}),n.resolver){const{errors:e,values:t}=await x();r.errors=e,c=_f(t)}else await b(a);if(l.disabled.size)for(const e of l.disabled)xh(c,e);if(xh(r.errors,"root"),fh(r.errors)){f.state.next({errors:{}});try{await e(c,i)}catch(u){s=u}}else t&&await t(Oe({},r.errors),i),O(),setTimeout(O);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:fh(r.errors)&&!s,submitCount:r.submitCount+1,errors:r.errors}),s)throw s},V=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const c=e?_f(e):i,d=_f(c),h=fh(e),p=h?i:d;if(t.keepDefaultValues||(i=c),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...l.mount,...Object.keys(Sh(i,o))]);for(const t of Array.from(e))Ff(r.dirtyFields,t)?zf(p,t,Ff(o,t)):E(t,Ff(p,t))}else{if(Mf&&Of(e))for(const e of l.mount){const t=Ff(a,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(mh(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(const e of l.mount)E(e,Ff(p,e));else a={}}o=n.shouldUnregister?t.keepDefaultValues?_f(i):{}:_f(p),f.array.next({values:Oe({},p)}),f.state.next({values:Oe({},p)})}l={mount:t.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},s.mount=!u.isValid||!!t.keepIsValid||!!t.keepDirtyValues,s.watch=!!n.shouldUnregister,f.state.next({submitCount:t.keepSubmitCount?r.submitCount:0,isDirty:!h&&(t.keepDirty?r.isDirty:!(!t.keepDefaultValues||dh(e,i))),isSubmitted:!!t.keepIsSubmitted&&r.isSubmitted,dirtyFields:h?{}:t.keepDirtyValues?t.keepDefaultValues&&o?Sh(i,o):r.dirtyFields:t.keepDefaultValues&&e?Sh(i,e):t.keepDirty?r.dirtyFields:{},touchedFields:t.keepTouched?r.touchedFields:{},errors:t.keepErrors?r.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},F=(e,t)=>V(ph(e)?e(o):e,t),B=e=>{r=Oe(Oe({},r),e)},z={control:{register:D,unregister:M,getFieldState:T,handleSubmit:I,setError:L,_subscribe:R,_runSchema:x,_focusError:O,_getWatch:k,_getDirty:w,_setValid:p,_setFieldArray:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],l=arguments.length>2?arguments[2]:void 0,c=arguments.length>3?arguments[3]:void 0,h=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],p=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];if(c&&l&&!n.disabled){if(s.action=!0,p&&Array.isArray(Ff(a,e))){const t=l(Ff(a,e),c.argA,c.argB);h&&zf(a,e,t)}if(p&&Array.isArray(Ff(r.errors,e))){const t=l(Ff(r.errors,e),c.argA,c.argB);h&&zf(r.errors,e,t),((e,t)=>{!If(Ff(e,t)).length&&xh(e,t)})(r.errors,e)}if((u.touchedFields||d.touchedFields)&&p&&Array.isArray(Ff(r.touchedFields,e))){const t=l(Ff(r.touchedFields,e),c.argA,c.argB);h&&zf(r.touchedFields,e,t)}(u.dirtyFields||d.dirtyFields)&&(r.dirtyFields=Sh(i,o)),f.state.next({name:e,isDirty:w(e,t),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else zf(o,e,t)},_setDisabledField:_,_setErrors:e=>{r.errors=e,f.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>If(Ff(s.mount?o:i,e,n.shouldUnregister?Ff(i,e,[]):[])),_reset:V,_resetDefaultValues:()=>ph(n.defaultValues)&&n.defaultValues().then(e=>{F(e,n.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of l.unMount){const t=Ff(a,e);t&&(t._f.refs?t._f.refs.every(e=>!vh(e)):!vh(t._f.ref))&&M(e)}l.unMount=new Set},_disableForm:e=>{Bf(e)&&(f.state.next({disabled:e}),Ih(a,(t,n)=>{const r=Ff(a,n);r&&(t.disabled=r._f.disabled||e,Array.isArray(r._f.refs)&&r._f.refs.forEach(t=>{t.disabled=r._f.disabled||e}))},0,!1))},_subjects:f,_proxyFormState:u,get _fields(){return a},get _formValues(){return o},get _state(){return s},set _state(e){s=e},get _defaultValues(){return i},get _names(){return l},set _names(e){l=e},get _formState(){return r},get _options(){return n},set _options(e){n=Oe(Oe({},n),e)}},subscribe:e=>(s.mount=!0,d=Oe(Oe({},d),e.formState),R(Oe(Oe({},e),{},{formState:d}))),trigger:P,register:D,handleSubmit:I,watch:(e,t)=>ph(e)?f.state.subscribe({next:n=>e(k(void 0,t),n)}):k(e,t,!0),setValue:E,getValues:A,reset:F,resetField:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ff(a,e)&&(Of(t.defaultValue)?E(e,_f(Ff(i,e))):(E(e,t.defaultValue),zf(i,e,_f(t.defaultValue))),t.keepTouched||xh(r.touchedFields,e),t.keepDirty||(xh(r.dirtyFields,e),r.isDirty=t.defaultValue?w(e,_f(Ff(i,e))):w()),t.keepError||(xh(r.errors,e),u.isValid&&p()),f.state.next(Oe({},r)))},clearErrors:e=>{e&&lh(e).forEach(e=>xh(r.errors,e)),f.state.next({errors:e?r.errors:{}})},unregister:M,setError:L,setFocus:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=Ff(a,e),r=n&&n._f;if(r){const e=r.refs?r.refs[0]:r.ref;e.focus&&(e.focus(),t.shouldSelect&&ph(e.select)&&e.select())}},getFieldState:T};return Oe(Oe({},z),{},{formControl:z})}const $h=["title","titleId"];function Qh(e,t){let{title:n,titleId:r}=e,a=Gt(e,$h);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const Gh=i.forwardRef(Qh),Zh=["title","titleId"];function Kh(e,t){let{title:n,titleId:r}=e,a=Gt(e,Zh);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}const Yh=i.forwardRef(Kh),Xh=()=>{const[e,t]=(0,i.useState)(!1),[n,r]=(0,i.useState)(null),{register:a,handleSubmit:o,formState:{errors:s},reset:l}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=i.useRef(void 0),n=i.useRef(void 0),[r,a]=i.useState({isDirty:!1,isValidating:!1,isLoading:ph(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:ph(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current=Oe(Oe({},e.formControl),{},{formState:r}),e.defaultValues&&!ph(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const n=qh(e),{formControl:a}=n,i=Gt(n,Ef);t.current=Oe(Oe({},i),{},{formState:r})}const o=t.current.control;return o._options=e,ah(()=>{const e=o._subscribe({formState:o._proxyFormState,callback:()=>a(Oe({},o._formState)),reRenderRoot:!0});return a(e=>Oe(Oe({},e),{},{isReady:!0})),o._formState.isReady=!0,e},[o]),i.useEffect(()=>o._disableForm(e.disabled),[o,e.disabled]),i.useEffect(()=>{e.mode&&(o._options.mode=e.mode),e.reValidateMode&&(o._options.reValidateMode=e.reValidateMode)},[o,e.mode,e.reValidateMode]),i.useEffect(()=>{e.errors&&(o._setErrors(e.errors),o._focusError())},[o,e.errors]),i.useEffect(()=>{e.shouldUnregister&&o._subjects.state.next({values:o._getWatch()})},[o,e.shouldUnregister]),i.useEffect(()=>{if(o._proxyFormState.isDirty){const e=o._getDirty();e!==r.isDirty&&o._subjects.state.next({isDirty:e})}},[o,r.isDirty]),i.useEffect(()=>{e.values&&!dh(e.values,n.current)?(o._reset(e.values,Oe({keepFieldsRef:!0},o._options.resetOptions)),n.current=e.values,a(e=>Oe({},e))):o._resetDefaultValues()},[o,e.values]),i.useEffect(()=>{o._state.mount||(o._setValid(),o._state.mount=!0),o._state.watch&&(o._state.watch=!1,o._subjects.state.next(Oe({},o._formState))),o._removeUnmounted()}),t.current.formState=rh(r,o),t.current}(),c={initial:{opacity:0,y:60},animate:{opacity:1,y:0}},u=[{icon:(0,il.jsx)(pl,{className:"h-6 w-6"}),title:"Email",value:"<EMAIL>",link:"mailto:<EMAIL>"},{icon:(0,il.jsx)(Gh,{className:"h-6 w-6"}),title:"Phone",value:"+91 7838630502",link:"tel:+************"},{icon:(0,il.jsx)(dl,{className:"h-6 w-6"}),title:"Location",value:"Noida, Uttar Pradesh, India",link:"https://maps.google.com/?q=Noida,India"}];return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:c,className:"text-center space-y-6",children:[(0,il.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white",children:["Get In ",(0,il.jsx)("span",{className:"text-gradient",children:"Touch"})]}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Have a project in mind? Let's discuss how we can bring your ideas to life"})]})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:c,className:"space-y-8",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Send Message"}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Fill out the form below and I'll get back to you as soon as possible."})]}),(0,il.jsxs)("form",{onSubmit:o(async e=>{t(!0),r(null);try{await Qd(e),r("success"),l()}catch(n){r("error")}finally{t(!1)}}),className:"space-y-6",children:[(0,il.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Name *"}),(0,il.jsx)("input",Oe(Oe({},a("name",{required:"Name is required"})),{},{type:"text",id:"name",className:"input-field",placeholder:"Your name"})),s.name&&(0,il.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.name.message})]}),(0,il.jsxs)("div",{children:[(0,il.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email *"}),(0,il.jsx)("input",Oe(Oe({},a("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})),{},{type:"email",id:"email",className:"input-field",placeholder:"<EMAIL>"})),s.email&&(0,il.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.email.message})]})]}),(0,il.jsxs)("div",{children:[(0,il.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Subject *"}),(0,il.jsx)("input",Oe(Oe({},a("subject",{required:"Subject is required"})),{},{type:"text",id:"subject",className:"input-field",placeholder:"Project discussion"})),s.subject&&(0,il.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.subject.message})]}),(0,il.jsxs)("div",{children:[(0,il.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Message *"}),(0,il.jsx)("textarea",Oe(Oe({},a("message",{required:"Message is required"})),{},{id:"message",rows:6,className:"input-field",placeholder:"Tell me about your project..."})),s.message&&(0,il.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s.message.message})]}),n&&(0,il.jsxs)("div",{className:"p-4 rounded-lg flex items-center space-x-2 ".concat("success"===n?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===n?(0,il.jsx)(dc,{className:"h-5 w-5"}):(0,il.jsx)(Yh,{className:"h-5 w-5"}),(0,il.jsx)("span",{children:"success"===n?"Message sent successfully! I'll get back to you soon.":"Failed to send message. Please try again."})]}),(0,il.jsx)("button",{type:"submit",disabled:e,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:e?"Sending...":"Send Message"})]})]}),(0,il.jsxs)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:c,className:"space-y-8",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Contact Information"}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Feel free to reach out to me through any of these channels."})]}),(0,il.jsx)("div",{className:"space-y-6",children:u.map((e,t)=>(0,il.jsx)($s.a,{href:e.link,target:e.link.startsWith("http")?"_blank":"_self",rel:e.link.startsWith("http")?"noopener noreferrer":"",className:"block p-6 bg-white dark:bg-dark-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",whileHover:{scale:1.02},children:(0,il.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,il.jsx)("div",{className:"bg-primary-100 dark:bg-primary-900 p-3 rounded-lg text-primary-600 dark:text-primary-400",children:e.icon}),(0,il.jsxs)("div",{children:[(0,il.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:e.title}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.value})]})]})},e.title))}),(0,il.jsxs)("div",{className:"bg-gradient-to-r from-primary-600 to-secondary-600 p-6 rounded-xl text-white",children:[(0,il.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Let's Work Together"}),(0,il.jsx)("p",{className:"text-primary-100 mb-4",children:"I'm always interested in new opportunities and exciting projects. Whether you're a startup or an established company, let's discuss how we can bring your vision to life."}),(0,il.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,il.jsx)("span",{children:"\u2713 Quick Response"}),(0,il.jsx)("span",{children:"\u2713 Professional Service"}),(0,il.jsx)("span",{children:"\u2713 Quality Guaranteed"})]})]})]})]})})})]})},Jh=["title","titleId"];function ep(e,t){let{title:n,titleId:r}=e,a=Gt(e,Jh);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const tp=i.forwardRef(ep),np=["title","titleId"];function rp(e,t){let{title:n,titleId:r}=e,a=Gt(e,np);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"}))}const ap=i.forwardRef(rp),ip=()=>{const e={initial:{opacity:0,y:60},animate:{opacity:1,y:0}},t={id:1,title:"Building Scalable Web Applications with React and Node.js",content:"\n      <p>Building scalable web applications is a crucial skill for modern developers. In this comprehensive guide, we'll explore how to create robust, maintainable applications using React for the frontend and Node.js for the backend.</p>\n      \n      <h2>Why Choose React and Node.js?</h2>\n      <p>React and Node.js form a powerful combination for full-stack development. React provides a component-based architecture that makes building complex user interfaces manageable, while Node.js offers excellent performance for server-side applications.</p>\n      \n      <h3>Key Benefits:</h3>\n      <ul>\n        <li>JavaScript everywhere - one language for frontend and backend</li>\n        <li>Large ecosystem with extensive libraries and tools</li>\n        <li>Strong community support and regular updates</li>\n        <li>Excellent performance and scalability</li>\n      </ul>\n      \n      <h2>Architecture Overview</h2>\n      <p>A well-structured application architecture is essential for scalability. Here's the recommended approach:</p>\n      \n      <h3>Frontend (React)</h3>\n      <ul>\n        <li>Component-based structure</li>\n        <li>State management with Redux or Context API</li>\n        <li>Routing with React Router</li>\n        <li>API integration with Axios or Fetch</li>\n      </ul>\n      \n      <h3>Backend (Node.js)</h3>\n      <ul>\n        <li>Express.js for server setup</li>\n        <li>RESTful API design</li>\n        <li>Database integration (MongoDB, PostgreSQL)</li>\n        <li>Authentication and authorization</li>\n      </ul>\n      \n      <h2>Best Practices</h2>\n      <p>Following best practices ensures your application remains maintainable and scalable:</p>\n      \n      <h3>Code Organization</h3>\n      <ul>\n        <li>Separate concerns with proper folder structure</li>\n        <li>Use TypeScript for better type safety</li>\n        <li>Implement proper error handling</li>\n        <li>Write comprehensive tests</li>\n      </ul>\n      \n      <h3>Performance Optimization</h3>\n      <ul>\n        <li>Implement code splitting and lazy loading</li>\n        <li>Optimize bundle size with tree shaking</li>\n        <li>Use caching strategies</li>\n        <li>Implement proper database indexing</li>\n      </ul>\n      \n      <h2>Deployment Considerations</h2>\n      <p>Proper deployment is crucial for production applications:</p>\n      \n      <ul>\n        <li>Use environment variables for configuration</li>\n        <li>Implement CI/CD pipelines</li>\n        <li>Monitor application performance</li>\n        <li>Set up proper logging and error tracking</li>\n      </ul>\n      \n      <h2>Conclusion</h2>\n      <p>Building scalable web applications with React and Node.js requires careful planning and following best practices. By implementing proper architecture, optimization techniques, and deployment strategies, you can create applications that scale efficiently and provide excellent user experiences.</p>\n    ",excerpt:"Learn how to build modern, scalable web applications using React for the frontend and Node.js for the backend. This comprehensive guide covers best practices, architecture patterns, and deployment strategies.",slug:"building-scalable-web-applications-react-nodejs",image_url:"/api/placeholder/1200/600",published:!0,created_at:"2024-01-15T10:00:00Z",read_time:"8 min read",category:"Web Development",author:"Deepak Kumar",tags:["React","Node.js","JavaScript","Full Stack","Web Development"]};return(0,il.jsxs)("div",{className:"min-h-screen pt-16",children:[(0,il.jsx)("div",{className:"container-custom py-8",children:(0,il.jsxs)(Ae,{to:"/blog",className:"inline-flex items-center gap-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-300",children:[(0,il.jsx)(tp,{className:"h-4 w-4"}),"Back to Blog"]})}),(0,il.jsx)("section",{className:"section-padding bg-gray-50 dark:bg-dark-800",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsxs)($s.div,{initial:"initial",animate:"animate",variants:e,className:"max-w-4xl mx-auto text-center space-y-6",children:[(0,il.jsxs)("div",{className:"flex items-center justify-center gap-4 mb-6",children:[(0,il.jsx)("span",{className:"bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 px-3 py-1 rounded-full text-sm font-medium",children:t.category}),(0,il.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400",children:[(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(mf,{className:"h-4 w-4"}),(n=t.created_at,new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}))]}),(0,il.jsxs)("div",{className:"flex items-center gap-1",children:[(0,il.jsx)(pc,{className:"h-4 w-4"}),t.read_time]})]})]}),(0,il.jsx)("h1",{className:"text-3xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight",children:t.title}),(0,il.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:t.excerpt}),(0,il.jsxs)("div",{className:"flex items-center justify-center gap-6 pt-4",children:[(0,il.jsxs)("div",{className:"flex items-center gap-2",children:[(0,il.jsx)("div",{className:"w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center",children:(0,il.jsx)(xf,{className:"h-5 w-5 text-primary-600 dark:text-primary-400"})}),(0,il.jsxs)("div",{className:"text-left",children:[(0,il.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:t.author}),(0,il.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Full Stack Developer"})]})]}),(0,il.jsxs)("button",{className:"flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-300",children:[(0,il.jsx)(ap,{className:"h-4 w-4"}),"Share"]})]})]})})}),(0,il.jsx)("section",{className:"py-8",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsx)($s.div,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"max-w-4xl mx-auto",children:(0,il.jsx)("img",{src:t.image_url,alt:t.title,className:"w-full h-96 object-cover rounded-xl shadow-lg"})})})}),(0,il.jsx)("section",{className:"section-padding",children:(0,il.jsx)("div",{className:"container-custom",children:(0,il.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,il.jsxs)("div",{className:"flex flex-col lg:flex-row gap-12",children:[(0,il.jsxs)($s.article,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"lg:w-2/3",children:[(0,il.jsx)("div",{className:"prose prose-lg dark:prose-invert max-w-none",dangerouslySetInnerHTML:{__html:t.content}}),(0,il.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-dark-700",children:[(0,il.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Tags"}),(0,il.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map((e,t)=>(0,il.jsxs)("span",{className:"bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300 cursor-pointer",children:["#",e]},t))})]})]}),(0,il.jsxs)($s.aside,{initial:"initial",whileInView:"animate",viewport:{once:!0},variants:e,className:"lg:w-1/3 space-y-8",children:[(0,il.jsxs)("div",{className:"card p-6 text-center",children:[(0,il.jsx)("div",{className:"w-20 h-20 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,il.jsx)(xf,{className:"h-10 w-10 text-primary-600 dark:text-primary-400"})}),(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:t.author}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:"Full Stack Developer passionate about creating innovative solutions and sharing knowledge through code."}),(0,il.jsx)(Ae,{to:"/contact",className:"btn-primary text-sm",children:"Get In Touch"})]}),(0,il.jsxs)("div",{className:"card p-6",children:[(0,il.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-6",children:"Related Posts"}),(0,il.jsx)("div",{className:"space-y-4",children:[{id:2,title:"The Future of Mobile Development: React Native vs Flutter",slug:"react-native-vs-flutter-comparison",image_url:"/api/placeholder/400/300",category:"Mobile Development",read_time:"12 min read"},{id:3,title:"Mastering API Design: RESTful vs GraphQL",slug:"api-design-restful-vs-graphql",image_url:"/api/placeholder/400/300",category:"Backend Development",read_time:"10 min read"},{id:4,title:"DevOps Best Practices for Modern Web Applications",slug:"devops-best-practices-modern-web-apps",image_url:"/api/placeholder/400/300",category:"DevOps",read_time:"15 min read"}].map(e=>(0,il.jsx)(Ae,{to:"/blog/".concat(e.slug),className:"block group",children:(0,il.jsxs)("div",{className:"flex gap-4",children:[(0,il.jsx)("img",{src:e.image_url,alt:e.title,className:"w-20 h-20 object-cover rounded-lg flex-shrink-0"}),(0,il.jsxs)("div",{className:"flex-1",children:[(0,il.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 text-sm leading-tight",children:e.title}),(0,il.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,il.jsx)("span",{children:e.category}),(0,il.jsx)("span",{children:"\u2022"}),(0,il.jsx)("span",{children:e.read_time})]})]})]})},e.id))})]})]})]})})})})]});var n},op=["title","titleId"];function sp(e,t){let{title:n,titleId:r}=e,a=Gt(e,op);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const lp=i.forwardRef(sp),cp=["title","titleId"];function up(e,t){let{title:n,titleId:r}=e,a=Gt(e,cp);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const dp=i.forwardRef(up),fp=["title","titleId"];function hp(e,t){let{title:n,titleId:r}=e,a=Gt(e,fp);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const pp=i.forwardRef(hp),mp=["title","titleId"];function gp(e,t){let{title:n,titleId:r}=e,a=Gt(e,mp);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))}const yp=i.forwardRef(gp),vp=["title","titleId"];function xp(e,t){let{title:n,titleId:r}=e,a=Gt(e,vp);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))}const bp=i.forwardRef(xp),wp=["title","titleId"];function kp(e,t){let{title:n,titleId:r}=e,a=Gt(e,wp);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const Sp=i.forwardRef(kp),jp=["title","titleId"];function Ep(e,t){let{title:n,titleId:r}=e,a=Gt(e,jp);return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}const Np=i.forwardRef(Ep),Cp=()=>{var e;const[t,n]=(0,i.useState)("overview"),[r,a]=(0,i.useState)(!1),[o,s]=(0,i.useState)(null),[l,c]=(0,i.useState)([]),u=[{id:"overview",label:"Overview",icon:(0,il.jsx)(lp,{className:"w-4 h-4"})},{id:"content",label:"Content Manager",icon:(0,il.jsx)(dp,{className:"w-4 h-4"})},{id:"database",label:"Database Tools",icon:(0,il.jsx)(pp,{className:"w-4 h-4"})},{id:"sync",label:"Sync & Deploy",icon:(0,il.jsx)(yp,{className:"w-4 h-4"})},{id:"settings",label:"Settings",icon:(0,il.jsx)(nc,{className:"w-4 h-4"})}];(0,i.useEffect)(()=>{d()},[]);const d=async()=>{a(!0);try{c([{id:"hero",title:"Hero Section",description:"Main landing section with title, subtitle, and CTA buttons",editable:!0,data:{title:"I'm Deepak Garg",subtitle:"Senior AI/ML Engineer & Multi-Cloud Architect",description:"Transforming enterprises with cutting-edge AI solutions across AWS, Azure, and Google Cloud. Specialized in LLM fine-tuning, semantic search, and scalable ML pipelines.",primaryCTA:"View AI Portfolio",secondaryCTA:"Schedule Consultation"}},{id:"about",title:"About Section",description:"Professional summary and key highlights",editable:!0,data:{title:"Enterprise AI Architect & Data Science Leader",content:"Senior AI/ML Engineer with 5+ years architecting enterprise-grade AI solutions. Expert in multi-cloud ML deployments, LLM fine-tuning, and building scalable data pipelines that process millions of records daily. Proven track record of delivering $2M+ in cost savings through intelligent automation and predictive analytics.",highlights:["Led 15+ enterprise AI transformations","Expert in AWS, Azure, Google Cloud ML platforms","Fine-tuned LLaMA, BERT, and custom transformer models","Built semantic search systems with 95% accuracy","Architected real-time ML pipelines processing 10K+ documents daily"]}},{id:"skills",title:"Skills & Expertise",description:"Technical skills with proficiency levels",editable:!0,data:[{name:"Large Language Models (LLMs)",level:95,category:"AI/ML",icon:"brain"},{name:"Multi-Cloud Architecture (AWS/Azure/GCP)",level:93,category:"Cloud",icon:"cloud"},{name:"Deep Learning & Neural Networks",level:92,category:"AI/ML",icon:"network"},{name:"MLOps & Model Deployment",level:90,category:"Engineering",icon:"deploy"},{name:"Natural Language Processing",level:94,category:"AI/ML",icon:"language"},{name:"Computer Vision & OCR",level:88,category:"AI/ML",icon:"vision"},{name:"Python & Advanced Analytics",level:96,category:"Programming",icon:"python"},{name:"Distributed Systems & Big Data",level:87,category:"Engineering",icon:"database"}]},{id:"stats",title:"Key Statistics",description:"Impressive metrics and achievements",editable:!0,data:[{number:"50+",label:"Enterprise AI Projects",description:"Successfully delivered across Fortune 500 companies"},{number:"5+",label:"Years Deep Expertise",description:"In AI/ML and cloud architecture"},{number:"$2M+",label:"Cost Savings Delivered",description:"Through intelligent automation and optimization"},{number:"95%",label:"Average Model Accuracy",description:"Across production ML systems"},{number:"10K+",label:"Documents Processed Daily",description:"By our intelligent document systems"},{number:"3",label:"Cloud Platforms Mastered",description:"AWS, Azure, and Google Cloud certified"}]}])}catch(e){f("error","Failed to load content sections")}finally{a(!1)}},f=(e,t)=>{s({type:e,text:t}),setTimeout(()=>s(null),5e3)},h=async()=>{a(!0);try{if(!(await fetch("/api/admin/sync-database",{method:"POST",headers:{"Content-Type":"application/json"}})).ok)throw new Error("Sync failed");f("success","Database synced successfully! Frontend will update automatically.")}catch(e){f("error","Failed to sync database. Please check your backend connection.")}finally{a(!1)}},p=async()=>{a(!0);try{if(!(await fetch("/api/admin/seed-database",{method:"POST",headers:{"Content-Type":"application/json"}})).ok)throw new Error("Seeding failed");f("success","Database seeded with professional AI/ML content!"),await d()}catch(e){f("error","Failed to seed database. Make sure your backend is running.")}finally{a(!1)}};return(0,il.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,il.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,il.jsxs)("div",{className:"mb-8",children:[(0,il.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Portfolio Admin Panel"}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage your AI/ML portfolio content with ease"})]}),o&&(0,il.jsxs)($s.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"mb-6 p-4 rounded-lg flex items-center space-x-2 ".concat("success"===o.type?"bg-green-100 text-green-800 border border-green-200":"error"===o.type?"bg-red-100 text-red-800 border border-red-200":"bg-blue-100 text-blue-800 border border-blue-200"),children:["success"===o.type&&(0,il.jsx)(dc,{className:"w-5 h-5"}),"error"===o.type&&(0,il.jsx)(Sp,{className:"w-5 h-5"}),"info"===o.type&&(0,il.jsx)(Np,{className:"w-5 h-5"}),(0,il.jsx)("span",{children:o.text})]}),(0,il.jsx)("div",{className:"mb-8",children:(0,il.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,il.jsx)("nav",{className:"-mb-px flex space-x-8",children:u.map(e=>(0,il.jsxs)("button",{onClick:()=>n(e.id),className:"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ".concat(t===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[e.icon,(0,il.jsx)("span",{children:e.label})]},e.id))})})}),(0,il.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6",children:[r&&(0,il.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,il.jsx)(yp,{className:"w-8 h-8 animate-spin text-blue-500"}),(0,il.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:"Loading..."})]}),!r&&"overview"===t&&(0,il.jsxs)("div",{className:"space-y-6",children:[(0,il.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white",children:[(0,il.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Ultimate Portfolio Management"}),(0,il.jsx)("p",{className:"text-blue-100",children:"Complete control over your AI/ML portfolio. Edit content, sync data, and deploy changes with one click."})]}),(0,il.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,il.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border",children:(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total Projects"}),(0,il.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"8"})]}),(0,il.jsx)(pp,{className:"w-8 h-8 text-blue-500"})]})}),(0,il.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border",children:(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Skills Listed"}),(0,il.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"20+"})]}),(0,il.jsx)(bp,{className:"w-8 h-8 text-green-500"})]})}),(0,il.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border",children:(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Blog Posts"}),(0,il.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"4"})]}),(0,il.jsx)(dp,{className:"w-8 h-8 text-purple-500"})]})}),(0,il.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border",children:(0,il.jsxs)("div",{className:"flex items-center justify-between",children:[(0,il.jsxs)("div",{children:[(0,il.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Last Updated"}),(0,il.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Now"})]}),(0,il.jsx)(yp,{className:"w-8 h-8 text-orange-500"})]})})]}),(0,il.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border",children:[(0,il.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Quick Actions"}),(0,il.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,il.jsxs)("button",{onClick:p,disabled:r,className:"flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors disabled:opacity-50",children:[(0,il.jsx)(pp,{className:"w-4 h-4"}),(0,il.jsx)("span",{children:"Load Professional Content"})]}),(0,il.jsxs)("button",{onClick:h,disabled:r,className:"flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg transition-colors disabled:opacity-50",children:[(0,il.jsx)(yp,{className:"w-4 h-4"}),(0,il.jsx)("span",{children:"Sync & Deploy"})]}),(0,il.jsxs)("button",{onClick:()=>window.open("/","_blank"),className:"flex items-center justify-center space-x-2 bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg transition-colors",children:[(0,il.jsx)(lp,{className:"w-4 h-4"}),(0,il.jsx)("span",{children:"Preview Portfolio"})]})]})]})]}),!r&&"overview"!==t&&(0,il.jsxs)("div",{className:"text-center py-12",children:[(0,il.jsx)("div",{className:"text-gray-400 mb-4",children:(0,il.jsx)(nc,{className:"w-16 h-16 mx-auto"})}),(0,il.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:[null===(e=u.find(e=>e.id===t))||void 0===e?void 0:e.label," Coming Soon"]}),(0,il.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"This section is being developed. Use the Overview tab for now."})]})]})]})})};const Pp=function(){return(0,il.jsx)(sl,{children:(0,il.jsx)(Ne,{children:(0,il.jsxs)("div",{className:"min-h-screen bg-white dark:bg-dark-900 transition-colors duration-300",children:[(0,il.jsx)(ll,{}),(0,il.jsx)($s.main,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:(0,il.jsxs)(be,{children:[(0,il.jsx)(ve,{path:"/",element:(0,il.jsx)(xc,{})}),(0,il.jsx)(ve,{path:"/about",element:(0,il.jsx)(bc,{})}),(0,il.jsx)(ve,{path:"/portfolio",element:(0,il.jsx)(Xd,{})}),(0,il.jsx)(ve,{path:"/services",element:(0,il.jsx)(cf,{})}),(0,il.jsx)(ve,{path:"/resume",element:(0,il.jsx)(gf,{})}),(0,il.jsx)(ve,{path:"/blog",element:(0,il.jsx)(bf,{})}),(0,il.jsx)(ve,{path:"/blog/:slug",element:(0,il.jsx)(ip,{})}),(0,il.jsx)(ve,{path:"/contact",element:(0,il.jsx)(Xh,{})}),(0,il.jsx)(ve,{path:"/admin",element:(0,il.jsx)(Cp,{})})]})}),(0,il.jsx)(ml,{}),(0,il.jsx)(xl,{})]})})})};s.createRoot(document.getElementById("root")).render((0,il.jsx)(i.StrictMode,{children:(0,il.jsx)(Pp,{})}))})();
//# sourceMappingURL=main.5ad70058.js.map