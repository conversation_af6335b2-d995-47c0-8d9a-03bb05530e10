import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Database, 
  Upload, 
  Download, 
  RefreshCw, 
  Save, 
  Eye, 
  Edit3, 
  Plus, 
  Trash2,
  CheckCircle,
  AlertCircle,
  Info,
  Zap
} from 'lucide-react';
import { api } from '../services/api';

interface ContentSection {
  id: string;
  title: string;
  description: string;
  editable: boolean;
  data: any;
}

const AdminPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error' | 'info', text: string} | null>(null);
  const [contentSections, setContentSections] = useState<ContentSection[]>([]);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: <Eye className="w-4 h-4" /> },
    { id: 'content', label: 'Content Manager', icon: <Edit3 className="w-4 h-4" /> },
    { id: 'database', label: 'Database Tools', icon: <Database className="w-4 h-4" /> },
    { id: 'sync', label: 'Sync & Deploy', icon: <RefreshCw className="w-4 h-4" /> },
    { id: 'settings', label: 'Settings', icon: <Settings className="w-4 h-4" /> }
  ];

  useEffect(() => {
    loadContentSections();
  }, []);

  const loadContentSections = async () => {
    setLoading(true);
    try {
      // Load all content sections
      const sections = [
        {
          id: 'hero',
          title: 'Hero Section',
          description: 'Main landing section with title, subtitle, and CTA buttons',
          editable: true,
          data: {
            title: "I'm Deepak Garg",
            subtitle: "Senior AI/ML Engineer & Multi-Cloud Architect",
            description: "Transforming enterprises with cutting-edge AI solutions across AWS, Azure, and Google Cloud. Specialized in LLM fine-tuning, semantic search, and scalable ML pipelines.",
            primaryCTA: "View AI Portfolio",
            secondaryCTA: "Schedule Consultation"
          }
        },
        {
          id: 'about',
          title: 'About Section',
          description: 'Professional summary and key highlights',
          editable: true,
          data: {
            title: "Enterprise AI Architect & Data Science Leader",
            content: "Senior AI/ML Engineer with 5+ years architecting enterprise-grade AI solutions. Expert in multi-cloud ML deployments, LLM fine-tuning, and building scalable data pipelines that process millions of records daily. Proven track record of delivering $2M+ in cost savings through intelligent automation and predictive analytics.",
            highlights: [
              "Led 15+ enterprise AI transformations",
              "Expert in AWS, Azure, Google Cloud ML platforms",
              "Fine-tuned LLaMA, BERT, and custom transformer models",
              "Built semantic search systems with 95% accuracy",
              "Architected real-time ML pipelines processing 10K+ documents daily"
            ]
          }
        },
        {
          id: 'skills',
          title: 'Skills & Expertise',
          description: 'Technical skills with proficiency levels',
          editable: true,
          data: [
            { name: 'Large Language Models (LLMs)', level: 95, category: 'AI/ML', icon: 'brain' },
            { name: 'Multi-Cloud Architecture (AWS/Azure/GCP)', level: 93, category: 'Cloud', icon: 'cloud' },
            { name: 'Deep Learning & Neural Networks', level: 92, category: 'AI/ML', icon: 'network' },
            { name: 'MLOps & Model Deployment', level: 90, category: 'Engineering', icon: 'deploy' },
            { name: 'Natural Language Processing', level: 94, category: 'AI/ML', icon: 'language' },
            { name: 'Computer Vision & OCR', level: 88, category: 'AI/ML', icon: 'vision' },
            { name: 'Python & Advanced Analytics', level: 96, category: 'Programming', icon: 'python' },
            { name: 'Distributed Systems & Big Data', level: 87, category: 'Engineering', icon: 'database' }
          ]
        },
        {
          id: 'stats',
          title: 'Key Statistics',
          description: 'Impressive metrics and achievements',
          editable: true,
          data: [
            { number: '50+', label: 'Enterprise AI Projects', description: 'Successfully delivered across Fortune 500 companies' },
            { number: '5+', label: 'Years Deep Expertise', description: 'In AI/ML and cloud architecture' },
            { number: '$2M+', label: 'Cost Savings Delivered', description: 'Through intelligent automation and optimization' },
            { number: '95%', label: 'Average Model Accuracy', description: 'Across production ML systems' },
            { number: '10K+', label: 'Documents Processed Daily', description: 'By our intelligent document systems' },
            { number: '3', label: 'Cloud Platforms Mastered', description: 'AWS, Azure, and Google Cloud certified' }
          ]
        }
      ];
      setContentSections(sections);
    } catch (error) {
      showMessage('error', 'Failed to load content sections');
    } finally {
      setLoading(false);
    }
  };

  const showMessage = (type: 'success' | 'error' | 'info', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleSyncDatabase = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/sync-database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (response.ok) {
        showMessage('success', 'Database synced successfully! Frontend will update automatically.');
      } else {
        throw new Error('Sync failed');
      }
    } catch (error) {
      showMessage('error', 'Failed to sync database. Please check your backend connection.');
    } finally {
      setLoading(false);
    }
  };

  const handleRunSeedScript = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/seed-database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (response.ok) {
        showMessage('success', 'Database seeded with professional AI/ML content!');
        await loadContentSections();
      } else {
        throw new Error('Seeding failed');
      }
    } catch (error) {
      showMessage('error', 'Failed to seed database. Make sure your backend is running.');
    } finally {
      setLoading(false);
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">Ultimate Portfolio Management</h2>
        <p className="text-blue-100">
          Complete control over your AI/ML portfolio. Edit content, sync data, and deploy changes with one click.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">8</p>
            </div>
            <Database className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Skills Listed</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">20+</p>
            </div>
            <Zap className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Blog Posts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">4</p>
            </div>
            <Edit3 className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Last Updated</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">Now</p>
            </div>
            <RefreshCw className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleRunSeedScript}
            disabled={loading}
            className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors disabled:opacity-50"
          >
            <Database className="w-4 h-4" />
            <span>Load Professional Content</span>
          </button>
          
          <button
            onClick={handleSyncDatabase}
            disabled={loading}
            className="flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Sync & Deploy</span>
          </button>
          
          <button
            onClick={() => window.open('/', '_blank')}
            className="flex items-center justify-center space-x-2 bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg transition-colors"
          >
            <Eye className="w-4 h-4" />
            <span>Preview Portfolio</span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Portfolio Admin Panel
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your AI/ML portfolio content with ease
          </p>
        </div>

        {/* Message Display */}
        {message && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              message.type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
              message.type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
              'bg-blue-100 text-blue-800 border border-blue-200'
            }`}
          >
            {message.type === 'success' && <CheckCircle className="w-5 h-5" />}
            {message.type === 'error' && <AlertCircle className="w-5 h-5" />}
            {message.type === 'info' && <Info className="w-5 h-5" />}
            <span>{message.text}</span>
          </motion.div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  {tab.icon}
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">Loading...</span>
            </div>
          )}

          {!loading && activeTab === 'overview' && renderOverview()}
          
          {!loading && activeTab !== 'overview' && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Settings className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {tabs.find(t => t.id === activeTab)?.label} Coming Soon
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                This section is being developed. Use the Overview tab for now.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;
