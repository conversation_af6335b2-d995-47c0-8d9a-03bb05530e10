# Portfolio Clean Restart Script - PowerShell Version
# Author: <PERSON><PERSON> Garg
# Description: Script to gracefully stop all development servers

Write-Host "🛑 Stopping Portfolio Development Servers..." -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Cyan

# Function to kill processes on a specific port
function Stop-ProcessOnPort {
    param($Port)
    $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($connections) {
        Write-Host "   Stopping processes on port $Port..." -ForegroundColor Yellow
        $connections | ForEach-Object { 
            try {
                Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue
                Write-Host "   ✅ Stopped process $($_.OwningProcess)" -ForegroundColor Green
            } catch {
                Write-Host "   ⚠️  Could not stop process $($_.OwningProcess)" -ForegroundColor Yellow
            }
        }
    }
}

# Stop processes on common ports
$ports = @(3000, 3001, 5000, 5173, 8000, 8080, 4000)
foreach ($port in $ports) {
    Stop-ProcessOnPort -Port $port
}

# Kill Node.js processes
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "   Stopping Node.js processes..." -ForegroundColor Yellow
    $nodeProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "   ✅ Node.js processes stopped" -ForegroundColor Green
}

# Kill npm processes
$npmProcesses = Get-Process -Name "npm" -ErrorAction SilentlyContinue
if ($npmProcesses) {
    Write-Host "   Stopping npm processes..." -ForegroundColor Yellow
    $npmProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "   ✅ npm processes stopped" -ForegroundColor Green
}

# Kill Python processes (uvicorn)
$pythonProcesses = Get-Process | Where-Object { $_.ProcessName -like "*python*" -or $_.CommandLine -like "*uvicorn*" } -ErrorAction SilentlyContinue
if ($pythonProcesses) {
    Write-Host "   Stopping Python/uvicorn processes..." -ForegroundColor Yellow
    $pythonProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "   ✅ Python processes stopped" -ForegroundColor Green
}

Write-Host ""
Write-Host "✅ All development servers stopped successfully!" -ForegroundColor Green
Write-Host "   You can now restart using .\restart-clean.ps1" -ForegroundColor Blue
Write-Host ""
