# Image Requirements for <PERSON><PERSON> Garg's Portfolio

## Directory Structure
```
frontend/public/images/
├── profile/
├── projects/
├── certificates/
└── companies/
```

## Required Images by Category

### 1. Profile Images (`/images/profile/`)
- **deepak-profile.jpg** (400x400px, square)
  - Professional headshot photo
  - Good lighting, clear background
  - Business casual or formal attire
  - Used in About section and header

- **deepak-hero.jpg** (1200x600px, landscape)
  - Professional photo for hero section
  - Can be same as profile but wider crop
  - Modern, clean background

### 2. Project Images (`/images/projects/`)
All project images should be **1200x800px** (3:2 ratio) for consistency

#### AI/ML Projects:
- **semantic-video-search.jpg**
  - Screenshot of video search interface
  - Or diagram showing AWS architecture
  - Should show search results with timestamps

- **policy-chatbot.jpg**
  - Chat interface screenshot
  - Or LLaMA model architecture diagram
  - Government schemes/policy related visual

- **facial-recognition.jpg**
  - Face detection interface screenshot
  - Or attendance dashboard
  - Should show real-time recognition

- **document-classification.jpg**
  - Document processing interface
  - Or BERT model visualization
  - Legal/insurance documents theme

#### Data Science Projects:
- **inventory-dashboard.jpg**
  - Power BI dashboard screenshot
  - Charts showing inventory trends
  - Professional business intelligence look

#### Data Engineering Projects:
- **web-scraping.jpg**
  - Data pipeline visualization
  - ETL process diagram
  - Airflow workflow screenshot

### 3. Company Logos (`/images/companies/`)
Size: **200x100px** (transparent background PNG preferred)

- **appsquadz-logo.png** - Current employer
- **areness-logo.png** - Previous employer  
- **ds-projects-logo.png** - Previous employer
- **golden-metal-logo.png** - Freelance client
- **so-infotech-logo.png** - Freelance client

### 4. Certificates (`/images/certificates/`)
Size: **800x600px** (4:3 ratio)

- **aws-certificates.jpg** - AWS certifications
- **ai-ml-certificates.jpg** - AI/ML course certificates
- **data-science-certificates.jpg** - Data science certifications
- **university-degree.jpg** - MCA/BCA degree certificates

## Technical Specifications

### Image Formats:
- **Photos**: JPG (optimized, 80-90% quality)
- **Logos**: PNG (transparent background)
- **Screenshots**: PNG (for UI clarity)

### Optimization:
- Compress images for web (use tools like TinyPNG)
- Use WebP format if possible for better performance
- Ensure responsive loading

### Fallback Images:
If you don't have specific project images, you can use:
- Architecture diagrams
- Technology stack visuals
- Dashboard screenshots
- Code snippets with syntax highlighting
- Abstract tech-themed graphics

## Where Images Are Used

### Portfolio Page:
- Project thumbnails in grid layout
- Hover effects reveal GitHub/Live links
- Filter by category (AI/ML, Data Science, etc.)

### About Page:
- Profile photo
- Company logos in experience timeline
- Skills visualization

### Hero Section:
- Background hero image
- Professional profile photo

### Testimonials:
- Client/colleague photos (if available)

## Tips for Creating/Finding Images

1. **Project Screenshots**:
   - Take high-quality screenshots of your dashboards
   - Use browser developer tools to get clean captures
   - Remove sensitive data before capturing

2. **Architecture Diagrams**:
   - Use tools like Lucidchart, Draw.io, or AWS Architecture Center
   - Keep consistent color schemes
   - Use official technology logos

3. **Professional Photos**:
   - Use good lighting (natural light preferred)
   - Clean, simple background
   - Professional attire
   - High resolution (can be cropped later)

4. **Placeholder Strategy**:
   - Start with technology-related stock photos
   - Replace with actual project screenshots gradually
   - Use consistent aspect ratios for professional look

## Image Optimization Commands (if needed)
```bash
# Install ImageMagick for batch processing
# Resize images to specific dimensions
magick input.jpg -resize 1200x800^ -gravity center -extent 1200x800 output.jpg

# Compress JPG images
magick input.jpg -quality 85 output.jpg
```

## Current Status
- ✅ Directory structure created
- ✅ Portfolio component updated with your projects
- ✅ Project categories updated to match your expertise
- ⏳ Need to add actual images
- ⏳ Update other components with your details

## Next Steps
1. Gather/create the required images
2. Add them to respective directories
3. Update other components (About, Contact, etc.) with your details
4. Test image loading and responsiveness
