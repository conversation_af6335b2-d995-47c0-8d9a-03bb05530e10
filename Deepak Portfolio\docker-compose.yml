services:
  # MySQL Database
  db:
    image: mysql:8.0
    container_name: portfolio_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: portfolio_db
      MYSQL_USER: portfolio_user
      MYSQL_PASSWORD: root
      MYSQL_ROOT_PASSWORD: root
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3307:3306"
    networks:
      - portfolio_network

  # Backend API
  backend:
    build: 
      context: .
      dockerfile: Dockerfile.backend
    container_name: portfolio_backend
    restart: unless-stopped
    environment:
      DATABASE_URL: mysql+pymysql://root:root@db:3306/portfolio_db
      SECRET_KEY: your-super-secret-key-change-in-production
      CORS_ORIGINS: '["http://localhost:3000"]'
    ports:
      - "8000:8000"
    depends_on:
      - db
    volumes:
      - ./backend:/app
    networks:
      - portfolio_network

  # Frontend React App
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: portfolio_frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:8000/api/v1
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - portfolio_network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: portfolio_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - portfolio_network

volumes:
  mysql_data:

networks:
  portfolio_network:
    driver: bridge
