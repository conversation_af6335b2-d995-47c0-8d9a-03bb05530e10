#!/bin/bash
# Portfolio Clean Restart Script - Bash Version
# Author: <PERSON><PERSON> Garg
# Description: Comprehensive script to clean and restart the portfolio application

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to kill processes on a specific port
kill_port() {
    local port=$1
    local pids=$(lsof -t -i:$port 2>/dev/null)
    if [ ! -z "$pids" ]; then
        print_warning "   Killing processes on port $port..."
        echo $pids | xargs kill -9 2>/dev/null
    fi
}

echo ""
print_status "🧹 Starting Portfolio Clean Restart Process..."
print_header "======================================="

# Step 1: Stop all running processes
print_warning "🛑 Step 1: Stopping all running processes..."

# Kill processes on common ports
for port in 3000 3001 5000 5173 8000 8080 4000; do
    kill_port $port
done

# Kill Node.js processes
pkill -f "node" 2>/dev/null
pkill -f "npm" 2>/dev/null
pkill -f "yarn" 2>/dev/null
pkill -f "pnpm" 2>/dev/null

print_status "✅ All processes stopped"

# Step 2: Clean project root
print_warning "🧼 Step 2: Cleaning project root..."

if [ -d "node_modules" ]; then
    print_error "   Removing root node_modules..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    print_error "   Removing root package-lock.json..."
    rm -f package-lock.json
fi

if [ -f "yarn.lock" ]; then
    print_error "   Removing root yarn.lock..."
    rm -f yarn.lock
fi

if [ -f "pnpm-lock.yaml" ]; then
    print_error "   Removing root pnpm-lock.yaml..."
    rm -f pnpm-lock.yaml
fi

# Step 3: Clean frontend directory
print_warning "🧼 Step 3: Cleaning frontend directory..."

if [ -d "frontend" ]; then
    cd frontend
    
    # Remove node_modules and lock files
    if [ -d "node_modules" ]; then
        print_error "   Removing frontend node_modules..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        print_error "   Removing frontend package-lock.json..."
        rm -f package-lock.json
    fi
    
    if [ -f "yarn.lock" ]; then
        print_error "   Removing frontend yarn.lock..."
        rm -f yarn.lock
    fi
    
    if [ -f "pnpm-lock.yaml" ]; then
        print_error "   Removing frontend pnpm-lock.yaml..."
        rm -f pnpm-lock.yaml
    fi
    
    # Remove build directories
    for dir in dist build .next .vite .turbo out; do
        if [ -d "$dir" ]; then
            print_error "   Removing frontend $dir..."
            rm -rf "$dir"
        fi
    done
    
    # Remove cache directories
    for dir in .cache node_modules/.cache .vite-cache; do
        if [ -d "$dir" ]; then
            print_error "   Removing frontend cache $dir..."
            rm -rf "$dir"
        fi
    done
    
    cd ..
fi

# Step 4: Clean backend directory
print_warning "🧼 Step 4: Cleaning backend directory..."

if [ -d "backend" ]; then
    cd backend
    
    # Remove Python cache
    print_error "   Removing Python cache files..."
    find . -type d -name "__pycache__" -delete 2>/dev/null
    find . -type d -name ".pytest_cache" -delete 2>/dev/null
    find . -name "*.pyc" -delete 2>/dev/null
    rm -rf .coverage htmlcov
    
    # Remove virtual environment if exists
    if [ -d "venv" ]; then
        print_error "   Removing virtual environment..."
        rm -rf venv
    fi
    
    if [ -d ".env" ]; then
        print_warning "   Virtual environment found in .env"
    fi
    
    cd ..
fi

# Step 5: Clear system caches
print_warning "🧼 Step 5: Clearing system caches..."

if command_exists npm; then
    print_info "   Clearing npm cache..."
    npm cache clean --force 2>/dev/null
fi

if command_exists yarn; then
    print_info "   Clearing yarn cache..."
    yarn cache clean 2>/dev/null
fi

if command_exists pnpm; then
    print_info "   Clearing pnpm cache..."
    pnpm store prune 2>/dev/null
fi

# Clear temp files
if [ -d "/tmp" ]; then
    print_info "   Clearing temp files..."
    rm -rf /tmp/npm-* 2>/dev/null
fi

print_status "✅ All caches cleared"

# Step 6: Fresh installation
print_warning "📦 Step 6: Fresh installation..."

# Install frontend dependencies
if [ -f "frontend/package.json" ]; then
    cd frontend
    print_info "   Installing frontend dependencies..."
    
    if command_exists npm; then
        npm install --legacy-peer-deps
        if [ $? -eq 0 ]; then
            print_status "✅ Frontend dependencies installed successfully"
        else
            print_error "❌ Frontend installation failed"
        fi
    else
        print_error "❌ npm not found. Please install Node.js"
    fi
    
    cd ..
fi

# Install backend dependencies
if [ -f "backend/requirements.txt" ]; then
    cd backend
    print_info "   Installing backend dependencies..."
    
    if command_exists python3; then
        PYTHON_CMD="python3"
    elif command_exists python; then
        PYTHON_CMD="python"
    else
        print_error "❌ Python not found. Please install Python"
        cd ..
        exit 1
    fi
    
    # Create virtual environment
    $PYTHON_CMD -m venv venv
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate
    pip install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        print_status "✅ Backend dependencies installed successfully"
    else
        print_error "❌ Backend installation failed"
    fi
    
    cd ..
fi

# Step 7: Start development servers
print_warning "🚀 Step 7: Starting development servers..."

# Start backend server in background
if [ -d "backend" ]; then
    print_info "   Starting backend server..."
    cd backend
    source venv/bin/activate
    nohup python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 > backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > backend.pid
    cd ..
    sleep 3
fi

# Start frontend server in background
if [ -d "frontend" ]; then
    print_info "   Starting frontend server..."
    cd frontend
    nohup npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > frontend.pid
    cd ..
    sleep 3
fi

# Step 8: Final checks and information
print_warning "🔍 Step 8: Final checks..."

echo ""
print_header "======================================="
print_status "🎉 Portfolio Clean Restart Complete!"
print_header "======================================="
echo ""
print_warning "🌐 Your application should be running on:"
print_info "   Frontend: http://localhost:3000 (or check terminal for actual port)"
print_info "   Backend:  http://localhost:8000"
echo ""
print_warning "📊 Next steps:"
echo -e "${WHITE}   1. Check the log files for any errors (frontend.log, backend.log)${NC}"
echo -e "${WHITE}   2. Open your browser to the frontend URL${NC}"
echo -e "${WHITE}   3. Test the API endpoints at /docs (FastAPI)${NC}"
echo ""
print_warning "🔧 If you encounter issues:"
echo -e "${WHITE}   - Check Node.js version: node --version (recommended: 18+)${NC}"
echo -e "${WHITE}   - Check Python version: python --version (recommended: 3.8+)${NC}"
echo -e "${WHITE}   - Ensure all ports are available${NC}"
echo -e "${WHITE}   - Check firewall settings${NC}"
echo ""
print_warning "💡 Useful commands:"
echo -e "${WHITE}   - Check processes: ps aux | grep node${NC}"
echo -e "${WHITE}   - Check ports: lsof -i :3000${NC}"
echo -e "${WHITE}   - Kill process: kill -9 <PID>${NC}"
echo -e "${WHITE}   - Stop servers: ./stop-servers.sh${NC}"
echo ""
print_warning "📝 Log files:"
echo -e "${WHITE}   - Frontend: frontend/frontend.log${NC}"
echo -e "${WHITE}   - Backend: backend/backend.log${NC}"
echo ""
print_status "Happy coding! 🚀"
