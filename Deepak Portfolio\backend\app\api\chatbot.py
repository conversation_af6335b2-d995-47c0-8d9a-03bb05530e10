"""
Chatbot API endpoints
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from ..services.chatbot_service import chatbot_service

router = APIRouter()

class ChatMessage(BaseModel):
    message: str
    timestamp: Optional[datetime] = None

class ChatResponse(BaseModel):
    response: str
    timestamp: datetime
    suggested_questions: Optional[List[str]] = None

@router.post("/chat", response_model=ChatResponse)
async def chat_with_bot(message: ChatMessage):
    """
    Send a message to the AI chatbot and get a response
    """
    try:
        # Get response from chatbot service
        bot_response = chatbot_service.get_response(message.message)
        
        # Get suggested questions (optional, can be used for first interaction)
        suggested_questions = None
        if any(word in message.message.lower() for word in ["hello", "hi", "hey", "start"]):
            suggested_questions = chatbot_service.get_suggested_questions()
        
        return ChatResponse(
            response=bot_response,
            timestamp=datetime.now(),
            suggested_questions=suggested_questions
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chatbot error: {str(e)}")

@router.get("/chat/suggestions", response_model=List[str])
async def get_suggested_questions():
    """
    Get suggested questions for the chatbot
    """
    try:
        return chatbot_service.get_suggested_questions()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting suggestions: {str(e)}")

@router.get("/chat/health")
async def chatbot_health():
    """
    Health check for chatbot service
    """
    return {
        "status": "healthy",
        "service": "AI Chatbot",
        "timestamp": datetime.now(),
        "version": "1.0.0"
    }
