from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import Skill
from app.schemas import SkillResponse
from typing import List

router = APIRouter()

@router.get("/", response_model=List[SkillResponse])
async def get_skills(db: Session = Depends(get_db)):
    """Get all skills"""
    skills = db.query(Skill).all()
    return skills
