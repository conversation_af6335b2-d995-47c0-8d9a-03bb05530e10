# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL=mysql+pymysql://root:root@localhost:3307/portfolio_db
DATABASE_HOST=localhost
DATABASE_PORT=3307
DATABASE_NAME=portfolio_db
DATABASE_USER=root
DATABASE_PASSWORD=root

# Backend Configuration
BACKEND_PORT=8000
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Frontend Configuration
FRONTEND_PORT=3000
REACT_APP_API_URL=http://localhost:8000/api/v1

# Email Configuration (for contact form)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# Security
CORS_ORIGINS=["http://localhost:3000", "https://your-domain.com"]
ALLOWED_HOSTS=["localhost", "127.0.0.1", "your-domain.com"]

# Cloud Storage (optional for file uploads)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-s3-bucket

# Social Media Links
LINKEDIN_URL=https://linkedin.com/in/your-profile
GITHUB_URL=https://github.com/your-username
TWITTER_URL=https://twitter.com/your-handle
MEDIUM_URL=https://medium.com/@your-username

# Personal Information
FULL_NAME=Deepak Kumar
PROFESSION=Full Stack Developer
LOCATION=India
PHONE=+91-XXXXXXXXXX
EMAIL=<EMAIL>
