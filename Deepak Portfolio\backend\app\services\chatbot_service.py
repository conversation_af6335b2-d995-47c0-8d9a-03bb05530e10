"""
Simple AI Chatbot Service for Deepak's Portfolio
Uses rule-based responses with keyword matching
"""

import re
from typing import Dict, List, Optional
from datetime import datetime
import json

class ChatbotService:
    def __init__(self):
        self.knowledge_base = {
            "skills": {
                "keywords": ["skill", "technology", "tech", "programming", "language", "framework", "tool"],
                "responses": [
                    "<PERSON><PERSON> specializes in AI/ML with expertise in Python, TensorFlow, PyTorch, and cloud platforms like AWS, Azure, and Google Cloud. He's also skilled in LLMs, computer vision, NLP, and agentic AI systems.",
                    "His technical stack includes Python, TensorFlow, PyTorch, Scikit-learn, LangChain, N8n for agentic AI, and multi-cloud platforms (AWS SageMaker, Azure OpenAI, Google Cloud AI).",
                    "<PERSON><PERSON> has 95% proficiency in AI & Machine Learning, 93% in Large Language Models, and 92% in Computer Vision & NLP based on his project portfolio."
                ]
            },
            "experience": {
                "keywords": ["experience", "work", "job", "career", "position", "role", "company"],
                "responses": [
                    "<PERSON><PERSON> currently works as a Data Scientist & AI Engineer at Appsquadz Technologies. He has 3+ years of experience in data science and AI, previously working at Areness and as a freelance consultant.",
                    "His career progression: Data Analyst at D.S. Projects (2022-2023) → Freelance AI Consultant (2023-2024) → Data Engineer at Areness (2024) → Data Scientist & AI Engineer at Appsquadz (2025-Present).",
                    "At Appsquadz, he's improved model accuracy by 25%, reduced data processing time by 60%, and leads a team of 3 junior data scientists."
                ]
            },
            "projects": {
                "keywords": ["project", "work", "build", "develop", "create", "portfolio"],
                "responses": [
                    "Some of Deepak's notable projects include a Semantic Video Search Engine using AWS, a fine-tuned LLaMA Policy Chatbot, real-time facial recognition systems, and BERT-based document classification. He's completed 25+ AI/ML projects.",
                    "His flagship project is a Semantic Video Search Engine with 95% accuracy using AWS Transcribe and SageMaker. He also built a LLaMA 3.1 Policy Chatbot with 92% accuracy using LoRA fine-tuning.",
                    "Recent projects include: Real-time Facial Recognition (95% accuracy), BERT Document Classification (92% accuracy), Multi-Cloud Churn Prediction ($2M+ savings), and OCR Document Intelligence (10K+ docs daily)."
                ]
            },
            "education": {
                "keywords": ["education", "study", "degree", "university", "college", "academic", "cgpa", "gpa"],
                "responses": [
                    "Deepak is pursuing his MCA in Data Science from Bennett University (9.1 CGPA) and completed his BCA from J.C. Bose University (8.5 CGPA).",
                    "He's a consistent top performer with 9.1 CGPA in his Master's program, actively involved in research initiatives and university AI club leadership.",
                    "His academic focus is on advanced data science, machine learning, and AI research with practical project implementations."
                ]
            },
            "publications": {
                "keywords": ["publication", "research", "paper", "publish", "journal", "conference"],
                "responses": [
                    "Deepak has published 5 research papers including work on tuberculosis prediction using CNN, Parkinson's disease classification, and generative AI in healthcare. His work has been published in IEEE conferences and Taylor & Francis.",
                    "Recent publications: 'Tuberculosis Prediction Using CNN and Transfer Learning' (2025), 'Evaluating Parkinson's Disease Severity Using ML Classifiers' (2025), and 'Generative AI in Healthcare' book chapter (Taylor & Francis).",
                    "His IEEE publications include 'Financial Data and Risk Management using ML' and 'Comparative Analysis of ML Algorithms in Parkinson Disease Diagnosis' with practical healthcare applications."
                ]
            },
            "contact": {
                "keywords": ["contact", "hire", "email", "reach", "connect", "collaboration", "opportunity"],
                "responses": [
                    "You can contact <NAME_EMAIL> for collaboration opportunities, consulting, or full-time positions in AI/ML and data science.",
                    "Deepak is open to AI/ML consulting, data science projects, research collaborations, and full-time opportunities. Reach <NAME_EMAIL>",
                    "For business inquiries, AI consulting, or career opportunities, contact Deepak <NAME_EMAIL>. He responds within 24 hours."
                ]
            },
            "achievements": {
                "keywords": ["achievement", "award", "recognition", "success", "accomplishment"],
                "responses": [
                    "Key achievements: 25% model accuracy improvement, 60% processing time reduction, $2M+ cost savings through churn prediction, and 100% client satisfaction rate as freelance consultant.",
                    "Deepak has completed 75+ successful projects, published 5 research papers, maintains 9.1 CGPA, and leads AI research initiatives at university level.",
                    "Professional highlights: Led 3-person data science team, processed 10K+ documents daily with OCR system, and achieved 95% accuracy in facial recognition systems."
                ]
            }
        }
        
        self.greetings = {
            "keywords": ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"],
            "responses": [
                "Hello! I'm Deepak's AI assistant. I can help you learn about his expertise in AI, machine learning, and data science. What would you like to know?",
                "Hi there! I'm here to answer questions about Deepak's skills, projects, experience, and research. How can I help you today?",
                "Greetings! I can provide information about Deepak's AI/ML expertise, career journey, and technical achievements. What interests you most?"
            ]
        }
        
        self.thanks = {
            "keywords": ["thank", "thanks", "appreciate", "helpful"],
            "responses": [
                "You're welcome! Feel free to ask me anything else about Deepak's background, projects, or expertise.",
                "Happy to help! Is there anything specific about Deepak's AI/ML experience you'd like to explore further?",
                "Glad I could assist! Don't hesitate to ask more questions about his technical skills or career journey."
            ]
        }

    def get_response(self, user_input: str) -> str:
        """
        Generate response based on user input using keyword matching
        """
        user_input_lower = user_input.lower()
        
        # Check for greetings
        if any(keyword in user_input_lower for keyword in self.greetings["keywords"]):
            import random
            return random.choice(self.greetings["responses"])
        
        # Check for thanks
        if any(keyword in user_input_lower for keyword in self.thanks["keywords"]):
            import random
            return random.choice(self.thanks["responses"])
        
        # Check knowledge base
        best_match = None
        max_matches = 0
        
        for category, data in self.knowledge_base.items():
            matches = sum(1 for keyword in data["keywords"] if keyword in user_input_lower)
            if matches > max_matches:
                max_matches = matches
                best_match = category
        
        if best_match and max_matches > 0:
            import random
            return random.choice(self.knowledge_base[best_match]["responses"])
        
        # Default response
        default_responses = [
            "That's an interesting question! Deepak has extensive experience in AI/ML, data science, and cloud technologies. You can ask me about his skills, projects, experience, education, or publications. What would you like to know more about?",
            "I'd be happy to help you learn more about Deepak! You can ask about his technical expertise, career journey, research publications, or specific projects. What aspect interests you most?",
            "Great question! Deepak specializes in AI/ML with 3+ years of experience. Feel free to ask about his skills (Python, TensorFlow, AWS), projects (25+ completed), or career progression. How can I assist you?"
        ]
        
        import random
        return random.choice(default_responses)

    def get_suggested_questions(self) -> List[str]:
        """
        Return suggested questions users can ask
        """
        return [
            "What are Deepak's main technical skills?",
            "Tell me about his work experience",
            "What projects has he worked on?",
            "What is his educational background?",
            "Has he published any research papers?",
            "How can I contact him for opportunities?",
            "What are his key achievements?"
        ]

# Global instance
chatbot_service = ChatbotService()
