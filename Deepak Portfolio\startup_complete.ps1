# Complete Portfolio Project Startup Script for Windows
# This script builds containers, starts services, and verifies everything is working

param(
    [switch]$ShowLogs
)

Write-Host "=" * 70 -ForegroundColor Green
Write-Host "PORTFOLIO PROJECT - COMPLETE STARTUP" -ForegroundColor Green
Write-Host "=" * 70 -ForegroundColor Green

function Wait-ForService {
    param(
        [string]$ServiceName,
        [scriptblock]$CheckScript,
        [int]$MaxWaitSeconds = 60
    )
    
    Write-Host "`nWaiting for $ServiceName to be ready..." -ForegroundColor Yellow
    
    for ($i = 0; $i -lt $MaxWaitSeconds; $i++) {
        try {
            if (& $CheckScript) {
                Write-Host "✓ $ServiceName is ready" -ForegroundColor Green
                return $true
            }
            
            if ($i % 10 -eq 0) {
                Write-Host "Waiting for $ServiceName... ($i/$MaxWaitSeconds s)" -ForegroundColor Yellow
            }
            
            Start-Sleep -Seconds 1
        }
        catch {
            if ($i % 10 -eq 0) {
                Write-Host "Still waiting for $ServiceName... ($i/$MaxWaitSeconds s)" -ForegroundColor Yellow
            }
            Start-Sleep -Seconds 1
        }
    }
    
    Write-Host "✗ $ServiceName did not become ready within $MaxWaitSeconds seconds" -ForegroundColor Red
    return $false
}

function Test-MySQL {
    try {
        $connection = New-Object System.Data.Odbc.OdbcConnection
        $connection.ConnectionString = "DRIVER={MySQL ODBC 8.0 Driver};SERVER=localhost;DATABASE=portfolio_db;UID=root;PWD=root;"
        $connection.Open()
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

function Test-Backend {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -TimeoutSec 5 -UseBasicParsing
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Test-Frontend {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -UseBasicParsing
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Show-ContainerLogs {
    Write-Host "`n" + "=" * 70 -ForegroundColor Blue
    Write-Host "CONTAINER LOGS" -ForegroundColor Blue
    Write-Host "=" * 70 -ForegroundColor Blue
    
    $containers = @('portfolio_db', 'portfolio_backend', 'portfolio_frontend')
    
    foreach ($container in $containers) {
        Write-Host "`n--- $container logs ---" -ForegroundColor Cyan
        try {
            docker logs $container --tail=20
        }
        catch {
            Write-Host "Could not get logs for $container`: $_" -ForegroundColor Red
        }
    }
}

if ($ShowLogs) {
    Show-ContainerLogs
    exit 0
}

try {
    # Step 1: Stop any existing containers
    Write-Host "`n1. Stopping existing containers..." -ForegroundColor Yellow
    docker-compose down
    Start-Sleep -Seconds 3
    
    # Step 2: Build and start containers
    Write-Host "`n2. Building and starting containers..." -ForegroundColor Yellow
    $buildResult = docker-compose up --build -d
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Failed to build and start containers" -ForegroundColor Red
        exit 1
    }
    Write-Host "✓ Containers built and started successfully" -ForegroundColor Green
    
    # Step 3: Wait for MySQL to be ready
    Write-Host "`n3. Waiting for MySQL..." -ForegroundColor Yellow
    $mysqlReady = Wait-ForService -ServiceName "MySQL" -CheckScript { 
        try {
            # Simple TCP connection test
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            $tcpClient.Connect("localhost", 3306)
            $tcpClient.Close()
            return $true
        }
        catch {
            return $false
        }
    } -MaxWaitSeconds 90
    
    if (-not $mysqlReady) {
        Write-Host "✗ MySQL failed to start" -ForegroundColor Red
        exit 1
    }
    
    # Step 4: Wait for backend to be ready
    Write-Host "`n4. Waiting for Backend API..." -ForegroundColor Yellow
    $backendReady = Wait-ForService -ServiceName "Backend API" -CheckScript { Test-Backend } -MaxWaitSeconds 60
    
    if (-not $backendReady) {
        Write-Host "✗ Backend API failed to start" -ForegroundColor Red
        exit 1
    }
    
    # Step 5: Wait for frontend to be ready
    Write-Host "`n5. Waiting for Frontend..." -ForegroundColor Yellow
    $frontendReady = Wait-ForService -ServiceName "Frontend" -CheckScript { Test-Frontend } -MaxWaitSeconds 60
    
    if (-not $frontendReady) {
        Write-Host "✗ Frontend failed to start" -ForegroundColor Red
        exit 1
    }
    
    # Step 6: Run database tests
    Write-Host "`n6. Running database tests..." -ForegroundColor Yellow
    try {
        docker exec portfolio_backend python test_db_complete.py
        Write-Host "✓ Database tests completed" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Database tests failed, but services are running" -ForegroundColor Yellow
    }
    
    # Step 7: Seed sample data
    Write-Host "`n7. Seeding sample data..." -ForegroundColor Yellow
    try {
        docker exec portfolio_backend python seed_sample_data.py
        Write-Host "✓ Database seeded successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Database seeding failed, but services are running" -ForegroundColor Yellow
    }
    
    # Success message
    Write-Host "`n" + "=" * 70 -ForegroundColor Green
    Write-Host "PROJECT STARTUP COMPLETE" -ForegroundColor Green
    Write-Host "=" * 70 -ForegroundColor Green
    Write-Host "✓ All services are running and ready!" -ForegroundColor Green
    Write-Host "`nService URLs:" -ForegroundColor Cyan
    Write-Host "- Frontend: http://localhost:3000" -ForegroundColor White
    Write-Host "- Backend API: http://localhost:8000" -ForegroundColor White
    Write-Host "- API Documentation: http://localhost:8000/docs" -ForegroundColor White
    Write-Host "- MySQL Database: localhost:3306 (root/root)" -ForegroundColor White
    Write-Host "`nService Status:" -ForegroundColor Cyan
    Write-Host "- MySQL: ✓ Running" -ForegroundColor Green
    Write-Host "- Backend: ✓ Running" -ForegroundColor Green
    Write-Host "- Frontend: ✓ Running" -ForegroundColor Green
    Write-Host "`n🎉 Project started successfully!" -ForegroundColor Green
    Write-Host "You can now access your portfolio at http://localhost:3000" -ForegroundColor White
    
}
catch {
    Write-Host "`n✗ Startup failed: $_" -ForegroundColor Red
    Write-Host "Run 'powershell .\startup_complete.ps1 -ShowLogs' to see container logs" -ForegroundColor Yellow
    exit 1
}
