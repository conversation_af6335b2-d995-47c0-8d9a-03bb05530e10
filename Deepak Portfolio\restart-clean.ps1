#!/usr/bin/env pwsh
# Portfolio Clean Restart Script - PowerShell Version
# Author: Deepak Garg
# Description: Comprehensive script to clean and restart the portfolio application

Write-Host "🧹 Starting Portfolio Clean Restart Process..." -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Cyan

# Function to check if a command exists
function Test-Command {
    param($Command)
    $null = Get-Command $Command -ErrorAction SilentlyContinue
    return $?
}

# Step 1: Stop all running processes
Write-Host "🛑 Step 1: Stopping all running processes..." -ForegroundColor Yellow

# Kill processes on common ports
$ports = @(3000, 3001, 5000, 5173, 8000, 8080, 4000)
foreach ($port in $ports) {
    $processes = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($processes) {
        Write-Host "   Stopping processes on port $port..." -ForegroundColor Red
        $processes | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }
    }
}

# Kill Node.js processes
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "npm" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "yarn" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

Write-Host "✅ All processes stopped" -ForegroundColor Green

# Step 2: Clean project root
Write-Host "🧼 Step 2: Cleaning project root..." -ForegroundColor Yellow

if (Test-Path "node_modules") {
    Write-Host "   Removing root node_modules..." -ForegroundColor Red
    Remove-Item -Path "node_modules" -Recurse -Force
}

if (Test-Path "package-lock.json") {
    Write-Host "   Removing root package-lock.json..." -ForegroundColor Red
    Remove-Item -Path "package-lock.json" -Force
}

if (Test-Path "yarn.lock") {
    Write-Host "   Removing root yarn.lock..." -ForegroundColor Red
    Remove-Item -Path "yarn.lock" -Force
}

# Step 3: Clean frontend directory
Write-Host "🧼 Step 3: Cleaning frontend directory..." -ForegroundColor Yellow

if (Test-Path "frontend") {
    Set-Location "frontend"
    
    # Remove node_modules and lock files
    if (Test-Path "node_modules") {
        Write-Host "   Removing frontend node_modules..." -ForegroundColor Red
        Remove-Item -Path "node_modules" -Recurse -Force
    }
    
    if (Test-Path "package-lock.json") {
        Write-Host "   Removing frontend package-lock.json..." -ForegroundColor Red
        Remove-Item -Path "package-lock.json" -Force
    }
    
    if (Test-Path "yarn.lock") {
        Write-Host "   Removing frontend yarn.lock..." -ForegroundColor Red
        Remove-Item -Path "yarn.lock" -Force
    }
    
    # Remove build directories
    $buildDirs = @("dist", "build", ".next", ".vite", ".turbo", "out")
    foreach ($dir in $buildDirs) {
        if (Test-Path $dir) {
            Write-Host "   Removing frontend $dir..." -ForegroundColor Red
            Remove-Item -Path $dir -Recurse -Force
        }
    }
    
    # Remove cache directories
    $cacheDirs = @(".cache", "node_modules/.cache", ".vite-cache")
    foreach ($dir in $cacheDirs) {
        if (Test-Path $dir) {
            Write-Host "   Removing frontend cache $dir..." -ForegroundColor Red
            Remove-Item -Path $dir -Recurse -Force
        }
    }
    
    Set-Location ".."
}

# Step 4: Clean backend directory
Write-Host "🧼 Step 4: Cleaning backend directory..." -ForegroundColor Yellow

if (Test-Path "backend") {
    Set-Location "backend"
    
    # Remove Python cache
    $pythonCaches = @("__pycache__", ".pytest_cache", "*.pyc", ".coverage", "htmlcov")
    foreach ($cache in $pythonCaches) {
        if (Test-Path $cache) {
            Write-Host "   Removing backend $cache..." -ForegroundColor Red
            Remove-Item -Path $cache -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    # Remove virtual environment if exists
    if (Test-Path "venv") {
        Write-Host "   Removing virtual environment..." -ForegroundColor Red
        Remove-Item -Path "venv" -Recurse -Force
    }
    
    if (Test-Path ".env") {
        Write-Host "   Virtual environment found in .env" -ForegroundColor Yellow
    }
    
    Set-Location ".."
}

# Step 5: Clear system caches
Write-Host "🧼 Step 5: Clearing system caches..." -ForegroundColor Yellow

if (Test-Command "npm") {
    Write-Host "   Clearing npm cache..." -ForegroundColor Blue
    npm cache clean --force 2>$null
}

if (Test-Command "yarn") {
    Write-Host "   Clearing yarn cache..." -ForegroundColor Blue
    yarn cache clean 2>$null
}

if (Test-Command "pnpm") {
    Write-Host "   Clearing pnpm cache..." -ForegroundColor Blue
    pnpm store prune 2>$null
}

# Clear temp files
$tempPath = $env:TEMP
if (Test-Path "$tempPath\npm-*") {
    Write-Host "   Clearing npm temp files..." -ForegroundColor Blue
    Remove-Item -Path "$tempPath\npm-*" -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "✅ All caches cleared" -ForegroundColor Green

# Step 6: Fresh installation
Write-Host "📦 Step 6: Fresh installation..." -ForegroundColor Yellow

# Install frontend dependencies
if (Test-Path "frontend/package.json") {
    Set-Location "frontend"
    Write-Host "   Installing frontend dependencies..." -ForegroundColor Blue
    
    if (Test-Command "npm") {
        npm install --legacy-peer-deps
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Frontend dependencies installed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Frontend installation failed" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ npm not found. Please install Node.js" -ForegroundColor Red
    }
    
    Set-Location ".."
}

# Install backend dependencies
if (Test-Path "backend/requirements.txt") {
    Set-Location "backend"
    Write-Host "   Installing backend dependencies..." -ForegroundColor Blue
    
    if (Test-Command "python") {
        # Create virtual environment
        python -m venv venv
        
        # Activate virtual environment and install dependencies
        if ($IsWindows) {
            & ".\venv\Scripts\Activate.ps1"
        } else {
            & "source venv/bin/activate"
        }
        
        pip install -r requirements.txt
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Backend dependencies installed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Backend installation failed" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Python not found. Please install Python" -ForegroundColor Red
    }
    
    Set-Location ".."
}

# Step 7: Start development servers
Write-Host "🚀 Step 7: Starting development servers..." -ForegroundColor Yellow

# Start backend server
if (Test-Path "backend") {
    Write-Host "   Starting backend server..." -ForegroundColor Blue
    Start-Process -FilePath "powershell" -ArgumentList "-Command", "cd 'backend'; if (Test-Path 'venv/Scripts/Activate.ps1') { & '.\venv\Scripts\Activate.ps1' }; python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    Start-Sleep -Seconds 3
}

# Start frontend server
if (Test-Path "frontend") {
    Write-Host "   Starting frontend server..." -ForegroundColor Blue
    Start-Process -FilePath "powershell" -ArgumentList "-Command", "cd 'frontend'; npm run dev"
    Start-Sleep -Seconds 3
}

# Step 8: Final checks and information
Write-Host "🔍 Step 8: Final checks..." -ForegroundColor Yellow

Write-Host ""
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host "🎉 Portfolio Clean Restart Complete!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🌐 Your application should be running on:" -ForegroundColor Yellow
Write-Host "   Frontend: http://localhost:3000 (or check terminal for actual port)" -ForegroundColor Cyan
Write-Host "   Backend:  http://localhost:8000" -ForegroundColor Cyan
Write-Host ""
Write-Host "📊 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Check the terminal windows for any errors" -ForegroundColor White
Write-Host "   2. Open your browser to the frontend URL" -ForegroundColor White
Write-Host "   3. Test the API endpoints at /docs (FastAPI)" -ForegroundColor White
Write-Host ""
Write-Host "🔧 If you encounter issues:" -ForegroundColor Yellow
Write-Host "   - Check Node.js version (recommended: 18+)" -ForegroundColor White
Write-Host "   - Check Python version (recommended: 3.8+)" -ForegroundColor White
Write-Host "   - Ensure all ports are available" -ForegroundColor White
Write-Host "   - Check firewall settings" -ForegroundColor White
Write-Host ""
Write-Host "💡 Useful commands:" -ForegroundColor Yellow
Write-Host "   - Check processes: Get-Process -Name 'node'" -ForegroundColor White
Write-Host "   - Check ports: netstat -ano | findstr :3000" -ForegroundColor White
Write-Host "   - Kill process: taskkill /PID <PID> /F" -ForegroundColor White
Write-Host ""
Write-Host "Happy coding! 🚀" -ForegroundColor Green
