import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import ProcessVisualization from '../components/ProcessVisualization';
import AnimatedSkills from '../components/AnimatedSkills';
import AINetworkVisualization from '../components/AINetworkVisualization';


const Home: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  };

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };



  const stats = [
    { number: '15+', label: 'AI/ML Projects Completed' },
    { number: '3+', label: 'Years Experience' },
    { number: '92%', label: 'Average Model Accuracy' },
    { number: '5+', label: 'Technologies Mastered' },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="hero-gradient min-h-screen flex items-center justify-center relative overflow-hidden pt-20">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-secondary-600/10"></div>
        <AINetworkVisualization />
        
        <motion.div
          initial="initial"
          animate="animate"
          variants={staggerChildren}
          className="container-custom relative z-10"
        >
          <div className="text-center space-y-8">
            <motion.div variants={fadeInUp} className="space-y-4">
              <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white">
                I'm{' '}
                <span className="text-gradient">
                  Deepak Garg
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-light">
                Data Scientist & AI Engineer
              </p>
            </motion.div>

            <motion.p 
              variants={fadeInUp}
              className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed"
            >
              Passionate Data Scientist with 3+ years of experience building intelligent AI/ML solutions for real-world problems.
              Specialized in machine learning, data analysis, and creating scalable systems that drive business value and innovation.
            </motion.p>

            <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/portfolio" className="btn-primary">
                View AI Portfolio
              </Link>
              <Link to="/contact" className="btn-outline">
                Schedule Consultation
              </Link>
            </motion.div>

            <motion.div variants={fadeInUp} className="pt-8">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">Scroll down to explore</p>
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ArrowDownIcon className="h-6 w-6 text-primary-600 mx-auto" />
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary-200 dark:bg-primary-800 rounded-full opacity-20 animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-secondary-200 dark:bg-secondary-800 rounded-full opacity-20 animate-bounce-slow"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-primary-300 dark:bg-primary-700 rounded-full opacity-30 animate-pulse-slow animation-delay-200"></div>
      </section>

      {/* About Preview Section */}
      <section className="section-padding bg-white dark:bg-dark-900">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
            className="text-center space-y-8"
          >
            <motion.div variants={fadeInUp} className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Data Science <span className="text-gradient">Expert</span>
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                Passionate Data Scientist with 3+ years of experience transforming complex data into actionable insights. Specialized in machine learning, predictive analytics, and building intelligent systems that solve real-world business challenges.
              </p>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-8">
                Proven expertise in developing recommendation systems, fraud detection models, and automated data pipelines. Successfully delivered projects with 92% accuracy rates and significant business impact through data-driven solutions.
              </p>
              <Link to="/about" className="btn-primary inline-block">
                Learn More About Me
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Advanced Skills Section */}
      <AnimatedSkills />

      {/* Stats Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                variants={fadeInUp}
                className="text-center space-y-2"
              >
                <div className="text-4xl md:text-5xl font-bold text-gradient">
                  {stat.number}
                </div>
                <div className="text-gray-600 dark:text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Process Visualization */}
      <ProcessVisualization />

      {/* CTA Section */}
      <section className="section-padding bg-primary-600 dark:bg-primary-700">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center space-y-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Ready to Start Your Next Data Science Project?
            </h2>
            <p className="text-xl text-primary-100 max-w-2xl mx-auto">
              Let's collaborate and bring your ideas to life with cutting-edge data science and machine learning solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Start Your AI Project
              </Link>
              <Link
                to="/resume"
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-8 rounded-lg transition-all duration-300"
              >
                Download Resume
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
