import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowDownIcon, 
  CodeBracketIcon, 
  DevicePhoneMobileIcon, 
  CloudIcon,
  CpuChipIcon,
  PaintBrushIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const Home: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const skills = [
    { icon: <CpuChipIcon className="h-8 w-8" />, name: 'Machine Learning', level: 95 },
    { icon: <CloudIcon className="h-8 w-8" />, name: 'AWS Cloud Services', level: 92 },
    { icon: <ChatBubbleLeftRightIcon className="h-8 w-8" />, name: 'NLP & LLMs', level: 90 },
    { icon: <CodeBracketIcon className="h-8 w-8" />, name: 'Python & Data Science', level: 95 },
    { icon: <DevicePhoneMobileIcon className="h-8 w-8" />, name: 'Deep Learning', level: 88 },
    { icon: <PaintBrushIcon className="h-8 w-8" />, name: 'Data Visualization', level: 85 },
  ];

  const stats = [
    { number: '15+', label: 'AI Projects Completed' },
    { number: '3+', label: 'Years Experience' },
    { number: '5+', label: 'Enterprise Clients' },
    { number: '+93%', label: 'Model Accuracy' },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="hero-gradient min-h-screen flex items-center justify-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-secondary-600/10"></div>
        
        <motion.div
          initial="initial"
          animate="animate"
          variants={staggerChildren}
          className="container-custom relative z-10"
        >
          <div className="text-center space-y-8">
            <motion.div variants={fadeInUp} className="space-y-4">
              <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white">
                I'm{' '}
                <span className="text-gradient">
                  Deepak Garg
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-light">
                Data Scientist & AI Engineer
              </p>
            </motion.div>

            <motion.p 
              variants={fadeInUp}
              className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed"
            >
              Innovative AI Engineer specializing in NLP, predictive modeling, and cloud-based ML pipelines. 
              Transforming cutting-edge AI research into scalable enterprise solutions that drive real business value.
            </motion.p>

            <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/portfolio" className="btn-primary">
                View My Work
              </Link>
              <Link to="/contact" className="btn-outline">
                Get In Touch
              </Link>
            </motion.div>

            <motion.div variants={fadeInUp} className="pt-8">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">Scroll down to explore</p>
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ArrowDownIcon className="h-6 w-6 text-primary-600 mx-auto" />
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary-200 dark:bg-primary-800 rounded-full opacity-20 animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-secondary-200 dark:bg-secondary-800 rounded-full opacity-20 animate-bounce-slow"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-primary-300 dark:bg-primary-700 rounded-full opacity-30 animate-pulse-slow animation-delay-200"></div>
      </section>

      {/* About Preview Section */}
      <section className="section-padding bg-white dark:bg-dark-900">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
                About <span className="text-gradient">Me</span>
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed">
                I'm an innovative Data Scientist and AI Engineer with 3+ years of experience delivering real-world AI and machine learning solutions across startups, enterprise environments, and freelance projects.
              </p>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                Specialized in NLP, predictive modeling, and cloud-based ML pipelines (AWS, Azure), with expertise in transforming cutting-edge AI research into scalable enterprise tools that drive business value.
              </p>
              <Link to="/about" className="btn-primary inline-block">
                Learn More About Me
              </Link>
            </motion.div>

            <motion.div variants={fadeInUp} className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">My Skills</h3>
              <div className="grid grid-cols-2 gap-4">
                {skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    variants={fadeInUp}
                    className="text-center space-y-2"
                  >
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full text-primary-600 dark:text-primary-400">
                      {skill.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm">{skill.name}</h4>
                      <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2 mt-2">
                        <motion.div
                          className="bg-primary-600 h-2 rounded-full"
                          initial={{ width: 0 }}
                          whileInView={{ width: `${skill.level}%` }}
                          viewport={{ once: true }}
                          transition={{ duration: 1, delay: index * 0.1 }}
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerChildren}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                variants={fadeInUp}
                className="text-center space-y-2"
              >
                <div className="text-4xl md:text-5xl font-bold text-gradient">
                  {stat.number}
                </div>
                <div className="text-gray-600 dark:text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary-600 dark:bg-primary-700">
        <div className="container-custom">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center space-y-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl text-primary-100 max-w-2xl mx-auto">
              Let's collaborate and bring your ideas to life with cutting-edge technology and innovative solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/contact" 
                className="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Start a Project
              </Link>
              <Link 
                to="/portfolio" 
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-8 rounded-lg transition-all duration-300"
              >
                View Portfolio
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
