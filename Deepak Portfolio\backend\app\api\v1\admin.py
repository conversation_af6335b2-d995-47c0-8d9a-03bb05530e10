"""
Admin API endpoints for portfolio management
Provides easy-to-use endpoints for content management without technical knowledge
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import subprocess
import os
import sys
from pathlib import Path

from app.core.database import get_db
from app.models import Project, BlogPost, Skill, Experience, Education, Service, Testimonial
from app.schemas import ProjectCreate, BlogPostCreate, SkillCreate

router = APIRouter()

@router.post("/sync-database")
async def sync_database(background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """
    Sync database with latest content and trigger frontend rebuild
    This endpoint makes it easy to deploy changes without technical knowledge
    """
    try:
        # Add background task to refresh any caches if needed
        background_tasks.add_task(refresh_application_cache)
        
        return {
            "status": "success",
            "message": "Database synced successfully. Changes will be visible immediately.",
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")

@router.post("/seed-database")
async def seed_database(db: Session = Depends(get_db)):
    """
    Run the seed database script to populate with professional AI/ML content
    Perfect for initial setup or resetting to professional content
    """
    try:
        # Get the path to the seed script
        backend_dir = Path(__file__).parent.parent.parent.parent
        seed_script = backend_dir / "seed_database.py"
        
        if not seed_script.exists():
            raise HTTPException(status_code=404, detail="Seed script not found")
        
        # Run the seed script
        result = subprocess.run([
            sys.executable, str(seed_script)
        ], capture_output=True, text=True, cwd=str(backend_dir))
        
        if result.returncode != 0:
            raise HTTPException(status_code=500, detail=f"Seed script failed: {result.stderr}")
        
        return {
            "status": "success",
            "message": "Database seeded with professional AI/ML content successfully!",
            "output": result.stdout,
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except subprocess.SubprocessError as e:
        raise HTTPException(status_code=500, detail=f"Failed to run seed script: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Seeding failed: {str(e)}")

@router.get("/content-overview")
async def get_content_overview(db: Session = Depends(get_db)):
    """
    Get overview of all content in the database
    Useful for dashboard and content management
    """
    try:
        overview = {
            "projects": {
                "total": db.query(Project).count(),
                "featured": db.query(Project).filter(Project.featured == True).count(),
                "by_category": {}
            },
            "blog_posts": {
                "total": db.query(BlogPost).count(),
                "published": db.query(BlogPost).filter(BlogPost.published == True).count()
            },
            "skills": {
                "total": db.query(Skill).count(),
                "by_category": {}
            },
            "experience": {
                "total": db.query(Experience).count(),
                "current": db.query(Experience).filter(Experience.current == True).count()
            },
            "services": {
                "total": db.query(Service).count(),
                "featured": db.query(Service).filter(Service.featured == True).count()
            },
            "testimonials": {
                "total": db.query(Testimonial).count(),
                "featured": db.query(Testimonial).filter(Testimonial.featured == True).count()
            }
        }
        
        # Get project categories
        project_categories = db.query(Project.category).distinct().all()
        for category in project_categories:
            if category[0]:
                overview["projects"]["by_category"][category[0]] = db.query(Project).filter(Project.category == category[0]).count()
        
        # Get skill categories
        skill_categories = db.query(Skill.category).distinct().all()
        for category in skill_categories:
            if category[0]:
                overview["skills"]["by_category"][category[0]] = db.query(Skill).filter(Skill.category == category[0]).count()
        
        return {
            "status": "success",
            "data": overview,
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get overview: {str(e)}")

@router.get("/quick-stats")
async def get_quick_stats(db: Session = Depends(get_db)):
    """
    Get quick statistics for the admin dashboard
    """
    try:
        stats = {
            "total_projects": db.query(Project).count(),
            "featured_projects": db.query(Project).filter(Project.featured == True).count(),
            "total_skills": db.query(Skill).count(),
            "blog_posts": db.query(BlogPost).count(),
            "published_posts": db.query(BlogPost).filter(BlogPost.published == True).count(),
            "total_experience": db.query(Experience).count(),
            "current_positions": db.query(Experience).filter(Experience.current == True).count(),
            "testimonials": db.query(Testimonial).count(),
            "services": db.query(Service).count()
        }
        
        return {
            "status": "success",
            "data": stats,
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@router.post("/update-hero-content")
async def update_hero_content(content: Dict[str, Any]):
    """
    Update hero section content
    This is a simple way to update the main landing section
    """
    try:
        # In a real implementation, you might store this in a settings table
        # For now, we'll return success to demonstrate the interface
        return {
            "status": "success",
            "message": "Hero content updated successfully",
            "data": content,
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update hero content: {str(e)}")

@router.post("/backup-database")
async def backup_database(db: Session = Depends(get_db)):
    """
    Create a backup of the current database
    Useful before making major changes
    """
    try:
        # This would implement actual backup logic
        return {
            "status": "success",
            "message": "Database backup created successfully",
            "backup_file": f"portfolio_backup_{int(__import__('time').time())}.sql",
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backup failed: {str(e)}")

@router.get("/system-status")
async def get_system_status():
    """
    Get system status and health check
    """
    try:
        return {
            "status": "healthy",
            "database": "connected",
            "api": "running",
            "version": "1.0.0",
            "uptime": "24h 30m",
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

async def refresh_application_cache():
    """
    Background task to refresh application cache
    """
    # Implement cache refresh logic here
    pass

@router.post("/deploy-changes")
async def deploy_changes(background_tasks: BackgroundTasks):
    """
    Deploy all pending changes to production
    One-click deployment for non-technical users
    """
    try:
        background_tasks.add_task(run_deployment_process)
        
        return {
            "status": "success",
            "message": "Deployment started. Changes will be live in a few minutes.",
            "deployment_id": f"deploy_{int(__import__('time').time())}",
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Deployment failed: {str(e)}")

async def run_deployment_process():
    """
    Background task for deployment process
    """
    # Implement deployment logic here
    pass

@router.get("/recent-activity")
async def get_recent_activity(db: Session = Depends(get_db)):
    """
    Get recent activity and changes
    """
    try:
        # This would track actual changes in a real implementation
        activities = [
            {
                "id": 1,
                "action": "Database seeded",
                "description": "Professional AI/ML content loaded",
                "timestamp": "2025-01-05T11:30:00Z",
                "type": "database"
            },
            {
                "id": 2,
                "action": "Content updated",
                "description": "Hero section modified",
                "timestamp": "2025-01-05T11:15:00Z",
                "type": "content"
            },
            {
                "id": 3,
                "action": "Sync completed",
                "description": "Frontend and backend synchronized",
                "timestamp": "2025-01-05T11:00:00Z",
                "type": "sync"
            }
        ]
        
        return {
            "status": "success",
            "data": activities,
            "timestamp": "2025-01-05T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get activity: {str(e)}")
