<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2193b0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6dd5ed;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chart" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bg)"/>
  
  <!-- Dashboard Frame -->
  <rect x="50" y="80" width="700" height="450" rx="20" fill="white" opacity="0.95"/>
  
  <!-- Header -->
  <rect x="50" y="80" width="700" height="60" rx="20" fill="#2193b0" opacity="0.8"/>
  <text x="400" y="120" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Inventory Optimization Dashboard</text>
  
  <!-- KPI Cards -->
  <rect x="80" y="160" width="150" height="80" rx="10" fill="#4CAF50" opacity="0.8"/>
  <text x="155" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Total Items</text>
  <text x="155" y="220" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">12,450</text>
  
  <rect x="250" y="160" width="150" height="80" rx="10" fill="#FF9800" opacity="0.8"/>
  <text x="325" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Low Stock</text>
  <text x="325" y="220" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">234</text>
  
  <rect x="420" y="160" width="150" height="80" rx="10" fill="#2196F3" opacity="0.8"/>
  <text x="495" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Forecast Accuracy</text>
  <text x="495" y="220" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">94.2%</text>
  
  <rect x="590" y="160" width="150" height="80" rx="10" fill="#9C27B0" opacity="0.8"/>
  <text x="665" y="185" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Cost Savings</text>
  <text x="665" y="220" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">$125K</text>
  
  <!-- Time Series Chart -->
  <rect x="80" y="270" width="320" height="200" rx="10" fill="#f8f9fa"/>
  <text x="240" y="295" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Demand Forecasting</text>
  
  <!-- Chart Lines -->
  <polyline points="100,450 130,420 160,440 190,400 220,380 250,390 280,370 310,360 340,350 370,340" 
            fill="none" stroke="#4CAF50" stroke-width="3"/>
  <polyline points="100,460 130,430 160,450 190,410 220,390 250,400 280,380 310,370 340,360 370,350" 
            fill="none" stroke="#FF9800" stroke-width="3" stroke-dasharray="5,5"/>
  
  <!-- Legend -->
  <rect x="100" y="310" width="15" height="3" fill="#4CAF50"/>
  <text x="120" y="318" fill="#333" font-family="Arial, sans-serif" font-size="10">Actual</text>
  <rect x="160" y="310" width="15" height="3" fill="#FF9800"/>
  <text x="180" y="318" fill="#333" font-family="Arial, sans-serif" font-size="10">Forecast</text>
  
  <!-- Inventory Levels -->
  <rect x="420" y="270" width="320" height="200" rx="10" fill="#f8f9fa"/>
  <text x="580" y="295" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Inventory Levels</text>
  
  <!-- Bar Chart -->
  <rect x="440" y="400" width="30" height="50" fill="#4CAF50"/>
  <rect x="480" y="380" width="30" height="70" fill="#FF9800"/>
  <rect x="520" y="420" width="30" height="30" fill="#F44336"/>
  <rect x="560" y="390" width="30" height="60" fill="#2196F3"/>
  <rect x="600" y="410" width="30" height="40" fill="#9C27B0"/>
  <rect x="640" y="370" width="30" height="80" fill="#4CAF50"/>
  <rect x="680" y="395" width="30" height="55" fill="#FF9800"/>
  
  <!-- Tech Stack -->
  <text x="400" y="560" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Power BI • Python • ARIMA • Prophet • Streamlit</text>
</svg>
