/**
 * Analytics tracking service for comprehensive visitor analytics
 */

interface SessionData {
  screen_resolution?: string;
  language?: string;
  referrer?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

interface PageViewData {
  session_id: string;
  url: string;
  title: string;
  time_on_page?: number;
  scroll_depth?: number;
  interactions?: Record<string, any>;
  exit_page?: boolean;
}

interface InteractionData {
  session_id: string;
  type: string;
  element_id?: string;
  element_class?: string;
  element_text?: string;
  page_url: string;
  x?: number;
  y?: number;
  additional_data?: Record<string, any>;
}

interface ContactSubmissionData {
  session_id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  form_type?: string;
}

interface DownloadData {
  session_id: string;
  file_name: string;
  file_type: string;
  file_size?: number;
  source: string;
}

interface ChatbotInteractionData {
  session_id: string;
  user_message: string;
  bot_response: string;
  response_time?: number;
  conversation_length?: number;
  resolved?: boolean;
}

class AnalyticsService {
  private sessionId: string | null = null;
  private pageStartTime: number = Date.now();
  private maxScrollDepth: number = 0;
  private interactions: Array<any> = [];
  private apiBaseUrl: string;

  constructor() {
    this.apiBaseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';
    this.initializeSession();
    this.setupEventListeners();
  }

  private async initializeSession(): Promise<void> {
    try {
      // Check if we already have a session ID
      const existingSessionId = localStorage.getItem('analytics_session_id');
      if (existingSessionId && existingSessionId.startsWith('fallback_')) {
        this.sessionId = existingSessionId;
        return;
      }

      const sessionData: SessionData = {
        screen_resolution: `${window.screen.width}x${window.screen.height}`,
        language: navigator.language,
        referrer: document.referrer,
        utm_source: this.getUrlParameter('utm_source'),
        utm_medium: this.getUrlParameter('utm_medium'),
        utm_campaign: this.getUrlParameter('utm_campaign')
      };

      // Set a timeout for the request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`${this.apiBaseUrl}/analytics/session/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sessionData),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const result = await response.json();
        this.sessionId = result.session_id;
        if (this.sessionId) {
          localStorage.setItem('analytics_session_id', this.sessionId);
        }

        // Track initial page view
        this.trackPageView();
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.debug('Analytics service unavailable, using fallback mode:', error);
      // Generate fallback session ID
      this.sessionId = 'fallback_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('analytics_session_id', this.sessionId);
    }
  }

  private setupEventListeners(): void {
    // Track scroll depth
    let ticking = false;
    const updateScrollDepth = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;
      
      if (scrollPercent > this.maxScrollDepth) {
        this.maxScrollDepth = scrollPercent;
      }
      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollDepth);
        ticking = true;
      }
    });

    // Track clicks
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      this.trackInteraction({
        type: 'click',
        element_id: target.id,
        element_class: target.className,
        element_text: target.textContent?.substring(0, 100),
        x: event.clientX,
        y: event.clientY
      });
    });

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackPageView(true); // Mark as exit
      }
    });

    // Track before unload
    window.addEventListener('beforeunload', () => {
      this.trackPageView(true); // Mark as exit
    });

    // Track performance metrics
    window.addEventListener('load', () => {
      setTimeout(() => {
        this.trackPerformanceMetrics();
      }, 1000);
    });

    // Track errors
    window.addEventListener('error', (event) => {
      this.trackError({
        error_type: 'javascript_error',
        error_message: event.message,
        stack_trace: event.error?.stack,
        page_url: window.location.href,
        severity: 'medium'
      });
    });

    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError({
        error_type: 'unhandled_promise_rejection',
        error_message: event.reason?.toString() || 'Unknown promise rejection',
        page_url: window.location.href,
        severity: 'high'
      });
    });
  }

  public trackPageView(exitPage: boolean = false): void {
    if (!this.sessionId) return;

    const timeOnPage = exitPage ? Date.now() - this.pageStartTime : 0;
    
    const pageData: PageViewData = {
      session_id: this.sessionId,
      url: window.location.href,
      title: document.title,
      time_on_page: Math.floor(timeOnPage / 1000),
      scroll_depth: this.maxScrollDepth,
      interactions: this.interactions,
      exit_page: exitPage
    };

    this.sendAnalytics('/analytics/track/page-view', pageData);

    if (!exitPage) {
      // Reset for new page
      this.pageStartTime = Date.now();
      this.maxScrollDepth = 0;
      this.interactions = [];
    }
  }

  public trackInteraction(data: Partial<InteractionData>): void {
    if (!this.sessionId) return;

    const interactionData: InteractionData = {
      session_id: this.sessionId,
      page_url: window.location.href,
      ...data
    } as InteractionData;

    this.interactions.push({
      type: data.type,
      timestamp: Date.now(),
      element: data.element_id || data.element_class
    });

    this.sendAnalytics('/analytics/track/interaction', interactionData);
  }

  public trackContactSubmission(data: Omit<ContactSubmissionData, 'session_id'>): void {
    if (!this.sessionId) return;

    const submissionData: ContactSubmissionData = {
      session_id: this.sessionId,
      ...data
    };

    this.sendAnalytics('/analytics/track/contact-submission', submissionData);
  }

  public trackDownload(data: Omit<DownloadData, 'session_id'>): void {
    if (!this.sessionId) return;

    const downloadData: DownloadData = {
      session_id: this.sessionId,
      ...data
    };

    this.sendAnalytics('/analytics/track/download', downloadData);
  }

  public trackChatbotInteraction(data: Omit<ChatbotInteractionData, 'session_id'>): void {
    if (!this.sessionId) return;

    const chatData: ChatbotInteractionData = {
      session_id: this.sessionId,
      ...data
    };

    this.sendAnalytics('/analytics/track/chatbot-interaction', chatData);
  }

  private trackPerformanceMetrics(): void {
    if (!this.sessionId || !window.performance) return;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');

    const performanceData = {
      session_id: this.sessionId,
      page_url: window.location.href,
      load_time: navigation.loadEventEnd - navigation.loadEventStart,
      first_contentful_paint: paint.find(p => p.name === 'first-contentful-paint')?.startTime,
      connection_type: (navigator as any).connection?.effectiveType || 'unknown'
    };

    this.sendAnalytics('/analytics/track/performance', performanceData);
  }

  private trackError(errorData: any): void {
    if (!this.sessionId) return;

    const errorInfo = {
      session_id: this.sessionId,
      browser_info: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled
      },
      ...errorData
    };

    this.sendAnalytics('/analytics/track/error', errorInfo);
  }

  private async sendAnalytics(endpoint: string, data: any): Promise<void> {
    try {
      // Skip if using fallback session (backend unavailable)
      if (this.sessionId?.startsWith('fallback_')) {
        return;
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

      await fetch(`${this.apiBaseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
    } catch (error) {
      // Silently fail analytics to not disrupt user experience
      console.debug('Analytics tracking failed:', error);
    }
  }

  private getUrlParameter(name: string): string | undefined {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name) || undefined;
  }

  // Public method to get session ID for other components
  public getSessionId(): string | null {
    return this.sessionId;
  }
}

// Create global analytics instance
export const analytics = new AnalyticsService();

// Export for use in components
export default analytics;
