<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#11998e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38ef7d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="face" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bg)"/>
  
  <!-- Camera Frame -->
  <rect x="150" y="150" width="500" height="300" rx="20" fill="white" opacity="0.95"/>
  <rect x="160" y="160" width="480" height="280" rx="10" fill="#000" opacity="0.8"/>
  
  <!-- Face Detection -->
  <circle cx="400" cy="280" r="80" fill="url(#face)" opacity="0.7"/>
  <circle cx="380" cy="260" r="8" fill="#333"/>
  <circle cx="420" cy="260" r="8" fill="#333"/>
  <path d="M 390 290 Q 400 300 410 290" stroke="#333" stroke-width="2" fill="none"/>
  
  <!-- Detection Box -->
  <rect x="320" y="200" width="160" height="160" rx="5" fill="none" stroke="#00ff00" stroke-width="3"/>
  
  <!-- Recognition Points -->
  <circle cx="380" cy="260" r="3" fill="#00ff00"/>
  <circle cx="420" cy="260" r="3" fill="#00ff00"/>
  <circle cx="400" cy="280" r="3" fill="#00ff00"/>
  <circle cx="390" cy="300" r="3" fill="#00ff00"/>
  <circle cx="410" cy="300" r="3" fill="#00ff00"/>
  
  <!-- Status Indicator -->
  <rect x="500" y="180" width="120" height="30" rx="15" fill="#00ff00" opacity="0.8"/>
  <text x="560" y="200" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">RECOGNIZED</text>
  
  <!-- Accuracy Display -->
  <rect x="500" y="220" width="120" height="25" rx="12" fill="#38ef7d" opacity="0.8"/>
  <text x="560" y="238" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">95% Accuracy</text>
  
  <!-- Tech Stack -->
  <text x="400" y="500" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">OpenCV • TensorFlow • CNN • Real-time Processing</text>
  
  <!-- Attendance System -->
  <rect x="50" y="480" width="200" height="80" rx="10" fill="white" opacity="0.9"/>
  <text x="150" y="505" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Attendance System</text>
  <text x="150" y="525" text-anchor="middle" fill="#666" font-family="Arial, sans-serif" font-size="12">Employee: John Doe</text>
  <text x="150" y="545" text-anchor="middle" fill="#666" font-family="Arial, sans-serif" font-size="12">Time: 09:15 AM</text>
</svg>
