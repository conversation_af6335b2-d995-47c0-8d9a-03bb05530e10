<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="video" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bg)"/>
  
  <!-- Search Interface -->
  <rect x="100" y="80" width="600" height="60" rx="30" fill="white" opacity="0.95"/>
  <text x="130" y="115" fill="#666" font-family="Arial, sans-serif" font-size="16">Search videos by content...</text>
  <circle cx="650" cy="110" r="20" fill="#4facfe"/>
  <path d="M 645 105 L 655 110 L 645 115 Z" fill="white"/>
  
  <!-- Video Results -->
  <rect x="80" y="180" width="200" height="150" rx="10" fill="white" opacity="0.9"/>
  <rect x="90" y="190" width="180" height="100" rx="5" fill="#333"/>
  <circle cx="180" cy="240" r="15" fill="white" opacity="0.8"/>
  <path d="M 175 235 L 185 240 L 175 245 Z" fill="#333"/>
  <text x="180" y="310" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">AI Tutorial</text>
  <text x="180" y="325" text-anchor="middle" fill="#ccc" font-family="Arial, sans-serif" font-size="10">95% match</text>
  
  <rect x="300" y="180" width="200" height="150" rx="10" fill="white" opacity="0.9"/>
  <rect x="310" y="190" width="180" height="100" rx="5" fill="#333"/>
  <circle cx="400" cy="240" r="15" fill="white" opacity="0.8"/>
  <path d="M 395 235 L 405 240 L 395 245 Z" fill="#333"/>
  <text x="400" y="310" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">ML Basics</text>
  <text x="400" y="325" text-anchor="middle" fill="#ccc" font-family="Arial, sans-serif" font-size="10">89% match</text>
  
  <rect x="520" y="180" width="200" height="150" rx="10" fill="white" opacity="0.9"/>
  <rect x="530" y="190" width="180" height="100" rx="5" fill="#333"/>
  <circle cx="620" cy="240" r="15" fill="white" opacity="0.8"/>
  <path d="M 615 235 L 625 240 L 615 245 Z" fill="#333"/>
  <text x="620" y="310" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Deep Learning</text>
  <text x="620" y="325" text-anchor="middle" fill="#ccc" font-family="Arial, sans-serif" font-size="10">87% match</text>
  
  <!-- AWS Architecture -->
  <rect x="150" y="380" width="500" height="120" rx="15" fill="white" opacity="0.9"/>
  <text x="400" y="405" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="16" font-weight="bold">AWS Architecture</text>
  
  <!-- AWS Services -->
  <rect x="170" y="420" width="80" height="30" rx="5" fill="#FF9900"/>
  <text x="210" y="440" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">S3</text>
  
  <rect x="270" y="420" width="80" height="30" rx="5" fill="#FF9900"/>
  <text x="310" y="440" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Transcribe</text>
  
  <rect x="370" y="420" width="80" height="30" rx="5" fill="#FF9900"/>
  <text x="410" y="440" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">SageMaker</text>
  
  <rect x="470" y="420" width="80" height="30" rx="5" fill="#FF9900"/>
  <text x="510" y="440" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Lambda</text>
  
  <rect x="570" y="420" width="80" height="30" rx="5" fill="#FF9900"/>
  <text x="610" y="440" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">OpenSearch</text>
  
  <!-- Arrows -->
  <path d="M 250 435 L 265 435" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 350 435 L 365 435" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 450 435 L 465 435" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 550 435 L 565 435" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>
  
  <!-- Accuracy -->
  <rect x="320" y="460" width="160" height="25" rx="12" fill="#4CAF50" opacity="0.8"/>
  <text x="400" y="477" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">95% Search Accuracy</text>
  
  <!-- Tech Stack -->
  <text x="400" y="540" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">AWS Transcribe • SageMaker • OpenSearch • Semantic Embeddings</text>
</svg>
