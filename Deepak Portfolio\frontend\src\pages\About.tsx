import React from 'react';
import { motion } from 'framer-motion';

const About: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="section-padding bg-gray-50 dark:bg-dark-800">
        <div className="container-custom">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
              Data Science <span className="text-gradient">Professional</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Passionate Data Scientist with 3+ years of experience creating intelligent solutions that transform data into valuable business insights
            </p>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                My Data Science Journey
              </h2>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                Passionate Data Scientist with 3+ years of experience transforming complex datasets into actionable business insights.
                My journey began with curiosity about data patterns and evolved into expertise in machine learning, predictive analytics,
                and building intelligent systems. I specialize in recommendation systems, fraud detection, and automated data pipelines
                that deliver real business value with consistently high accuracy rates.
              </p>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                Currently at Appsquadz (AWS Partner), I architect AI solutions from semantic video search 
                systems to LLM-based automation. I specialize in translating cutting-edge AI research 
                into scalable enterprise tools that create measurable business value.
              </p>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                My technical expertise spans multi-cloud ML architectures, advanced NLP, computer vision, 
                and predictive analytics. I've delivered solutions that reduced manual efforts by 90%, 
                improved accuracy by 95%, and generated millions in cost savings for enterprise clients.
              </p>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                From fine-tuning LLaMA models for government policies to building real-time facial recognition 
                systems, I transform complex AI challenges into production-ready solutions that scale across 
                AWS, Azure, and Google Cloud platforms.
              </p>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                When I'm not architecting AI systems, you'll find me exploring the latest in generative AI, 
                contributing to open-source ML projects, or sharing insights through technical articles on 
                cutting-edge AI implementations.
              </p>
            </motion.div>

            <motion.div
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="space-y-6"
            >
              <div className="bg-white dark:bg-dark-800 p-8 rounded-xl shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Quick Facts
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Location</span>
                    <span className="font-semibold text-gray-900 dark:text-white">Noida, India</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Experience</span>
                    <span className="font-semibold text-gray-900 dark:text-white">3+ Years</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">AI/ML Projects</span>
                    <span className="font-semibold text-gray-900 dark:text-white">15+ Deployed</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Specialization</span>
                    <span className="font-semibold text-gray-900 dark:text-white">AI/ML & Data Science</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
