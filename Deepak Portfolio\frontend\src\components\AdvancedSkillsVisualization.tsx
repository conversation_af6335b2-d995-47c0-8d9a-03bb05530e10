import React, { useRef, useMemo, useState, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Text, Sphere, Box, Plane, Line } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import { scaleLinear } from 'd3-scale';
import { interpolateViridis, interpolatePlasma, interpolateInferno } from 'd3-scale-chromatic';

interface SkillData {
  category: string;
  proficiency: number;
  projects: number;
  experience: string;
  color: string;
  items: string[];
}

interface AdvancedSkillsVisualizationProps {
  skills: SkillData[];
  isDarkMode: boolean;
  activeChart: string;
  onChartChange: (chart: string) => void;
}

// 3D Skill Sphere Component
const SkillSphere: React.FC<{ skill: SkillData; position: [number, number, number]; isDarkMode: boolean }> = ({ 
  skill, 
  position, 
  isDarkMode 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  
  const size = useMemo(() => {
    return scaleLinear().domain([0, 100]).range([0.5, 2])(skill.proficiency);
  }, [skill.proficiency]);

  const color = useMemo(() => {
    const intensity = skill.proficiency / 100;
    return isDarkMode 
      ? interpolatePlasma(intensity)
      : interpolateViridis(intensity);
  }, [skill.proficiency, isDarkMode]);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      meshRef.current.rotation.y += 0.01;
      
      if (hovered) {
        meshRef.current.scale.setScalar(1.2);
      } else {
        meshRef.current.scale.setScalar(1);
      }
    }
  });

  return (
    <group position={position}>
      <Sphere
        ref={meshRef}
        args={[size, 32, 32]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color={color}
          emissive={color}
          emissiveIntensity={0.2}
          roughness={0.3}
          metalness={0.7}
        />
      </Sphere>
      
      {hovered && (
        <Text
          position={[0, size + 1, 0]}
          fontSize={0.3}
          color={isDarkMode ? '#ffffff' : '#000000'}
          anchorX="center"
          anchorY="middle"
        >
          {skill.category}
          {'\n'}
          {skill.proficiency}%
        </Text>
      )}
      
      {/* Particle effects around sphere */}
      <ParticleRing radius={size + 0.5} count={20} color={color} />
    </group>
  );
};

// Particle Ring Component
const ParticleRing: React.FC<{ radius: number; count: number; color: string }> = ({ 
  radius, 
  count, 
  color 
}) => {
  const particlesRef = useRef<THREE.Group>(null);
  
  const particles = useMemo(() => {
    const temp = [];
    for (let i = 0; i < count; i++) {
      const angle = (i / count) * Math.PI * 2;
      temp.push({
        position: [
          Math.cos(angle) * radius,
          Math.sin(angle * 2) * 0.2,
          Math.sin(angle) * radius
        ] as [number, number, number],
        scale: Math.random() * 0.1 + 0.05
      });
    }
    return temp;
  }, [radius, count]);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y += 0.02;
    }
  });

  return (
    <group ref={particlesRef}>
      {particles.map((particle, index) => (
        <Sphere key={index} position={particle.position} args={[particle.scale, 8, 8]}>
          <meshBasicMaterial color={color} transparent opacity={0.6} />
        </Sphere>
      ))}
    </group>
  );
};

// 3D Skills Galaxy Component
const SkillsGalaxy: React.FC<{ skills: SkillData[]; isDarkMode: boolean }> = ({ skills, isDarkMode }) => {
  const positions = useMemo(() => {
    return skills.map((_, index) => {
      const angle = (index / skills.length) * Math.PI * 2;
      const radius = 5;
      return [
        Math.cos(angle) * radius,
        (Math.random() - 0.5) * 2,
        Math.sin(angle) * radius
      ] as [number, number, number];
    });
  }, [skills]);

  return (
    <>
      {skills.map((skill, index) => (
        <SkillSphere
          key={skill.category}
          skill={skill}
          position={positions[index]}
          isDarkMode={isDarkMode}
        />
      ))}
    </>
  );
};

// Neural Network Visualization
const NeuralNetwork: React.FC<{ isDarkMode: boolean }> = ({ isDarkMode }) => {
  const networkRef = useRef<THREE.Group>(null);
  
  const nodes = useMemo(() => {
    const layers = [8, 12, 16, 12, 6, 3]; // Neural network architecture
    const nodePositions: Array<{ position: [number, number, number]; layer: number }> = [];
    
    layers.forEach((nodeCount, layerIndex) => {
      for (let i = 0; i < nodeCount; i++) {
        const y = (i - nodeCount / 2) * 0.8;
        const x = (layerIndex - layers.length / 2) * 3;
        const z = (Math.random() - 0.5) * 2;
        nodePositions.push({
          position: [x, y, z],
          layer: layerIndex
        });
      }
    });
    
    return nodePositions;
  }, []);

  const connections = useMemo(() => {
    const lines: Array<{ start: [number, number, number]; end: [number, number, number] }> = [];
    
    // Connect nodes between adjacent layers
    for (let layer = 0; layer < 5; layer++) {
      const currentLayerNodes = nodes.filter(n => n.layer === layer);
      const nextLayerNodes = nodes.filter(n => n.layer === layer + 1);
      
      currentLayerNodes.forEach(currentNode => {
        nextLayerNodes.forEach(nextNode => {
          if (Math.random() > 0.3) { // Random connections
            lines.push({
              start: currentNode.position,
              end: nextNode.position
            });
          }
        });
      });
    }
    
    return lines;
  }, [nodes]);

  useFrame((state) => {
    if (networkRef.current) {
      networkRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.2;
    }
  });

  return (
    <group ref={networkRef}>
      {/* Neural Network Nodes */}
      {nodes.map((node, index) => (
        <Sphere key={index} position={node.position} args={[0.1, 16, 16]}>
          <meshStandardMaterial
            color={isDarkMode ? '#00ff88' : '#0066cc'}
            emissive={isDarkMode ? '#004422' : '#001133'}
            emissiveIntensity={0.3}
          />
        </Sphere>
      ))}
      
      {/* Neural Network Connections */}
      {connections.map((connection, index) => {
        const points = [
          new THREE.Vector3(...connection.start),
          new THREE.Vector3(...connection.end)
        ];

        return (
          <Line
            key={index}
            points={points}
            color={isDarkMode ? '#00ff88' : '#0066cc'}
            lineWidth={2}
            transparent
            opacity={0.3}
          />
        );
      })}
    </group>
  );
};

// Main Advanced Skills Visualization Component
const AdvancedSkillsVisualization: React.FC<AdvancedSkillsVisualizationProps> = ({
  skills,
  isDarkMode,
  activeChart,
  onChartChange
}) => {
  const [currentView, setCurrentView] = useState<'galaxy' | 'neural' | 'matrix'>('galaxy');

  const handleViewChange = (view: 'galaxy' | 'neural' | 'matrix') => {
    setCurrentView(view);
    onChartChange(view);
  };

  return (
    <div className="w-full h-full">
      {/* Control Panel */}
      <div className="flex justify-center gap-4 mb-6">
        {[
          { key: 'galaxy', label: 'Skills Galaxy', icon: '🌌' },
          { key: 'neural', label: 'Neural Network', icon: '🧠' },
          { key: 'matrix', label: 'Data Matrix', icon: '📊' }
        ].map((view) => (
          <motion.button
            key={view.key}
            onClick={() => handleViewChange(view.key as any)}
            className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-2 ${
              currentView === view.key
                ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="text-xl">{view.icon}</span>
            {view.label}
          </motion.button>
        ))}
      </div>

      {/* 3D Visualization Canvas */}
      <div className="w-full h-96 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 dark:from-gray-800 dark:via-gray-900 dark:to-black">
        <Canvas
          camera={{ position: [0, 0, 10], fov: 75 }}
          style={{ background: 'transparent' }}
        >
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} intensity={1} />
          <pointLight position={[-10, -10, -10]} intensity={0.5} color="#ff6b6b" />
          
          {currentView === 'galaxy' && <SkillsGalaxy skills={skills} isDarkMode={isDarkMode} />}
          {currentView === 'neural' && <NeuralNetwork isDarkMode={isDarkMode} />}
          
          <OrbitControls
            enableZoom={true}
            enablePan={true}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.5}
          />
        </Canvas>
      </div>

      {/* Skills Legend */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {skills.map((skill, index) => (
          <motion.div
            key={skill.category}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg"
          >
            <div className="flex items-center gap-2 mb-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: skill.color }}
              />
              <span className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                {skill.category.split(' ')[0]}
              </span>
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              {skill.proficiency}% • {skill.projects} projects
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default AdvancedSkillsVisualization;
