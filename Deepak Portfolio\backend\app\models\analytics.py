"""
Analytics models for tracking website visitors and interactions
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, JSON
from sqlalchemy.sql import func
from app.models.base import Base

class VisitorSession(Base):
    __tablename__ = "visitor_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), unique=True, index=True)
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    country = Column(String(100))
    city = Column(String(100))
    region = Column(String(100))
    timezone = Column(String(100))
    device_type = Column(String(50))  # desktop, mobile, tablet
    browser = Column(String(100))
    os = Column(String(100))
    screen_resolution = Column(String(20))
    language = Column(String(10))
    referrer = Column(Text)
    utm_source = Column(String(100))
    utm_medium = Column(String(100))
    utm_campaign = Column(String(100))
    first_visit = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    total_page_views = Column(Integer, default=0)
    session_duration = Column(Integer, default=0)  # in seconds
    is_bot = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class PageView(Base):
    __tablename__ = "page_views"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    page_url = Column(String(500))
    page_title = Column(String(200))
    time_on_page = Column(Integer, default=0)  # in seconds
    scroll_depth = Column(Float, default=0.0)  # percentage
    interactions = Column(JSON)  # clicks, hovers, etc.
    exit_page = Column(Boolean, default=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class UserInteraction(Base):
    __tablename__ = "user_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    interaction_type = Column(String(50))  # click, hover, scroll, download, etc.
    element_id = Column(String(100))
    element_class = Column(String(100))
    element_text = Column(Text)
    page_url = Column(String(500))
    x_coordinate = Column(Integer)
    y_coordinate = Column(Integer)
    additional_data = Column(JSON)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class ContactFormSubmission(Base):
    __tablename__ = "contact_form_submissions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    name = Column(String(100))
    email = Column(String(255))
    subject = Column(String(200))
    message = Column(Text)
    form_type = Column(String(50))  # contact, newsletter, etc.
    ip_address = Column(String(45))
    user_agent = Column(Text)
    spam_score = Column(Float, default=0.0)
    is_spam = Column(Boolean, default=False)
    responded = Column(Boolean, default=False)
    response_time = Column(Integer)  # hours to respond
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class DownloadTracking(Base):
    __tablename__ = "download_tracking"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    file_name = Column(String(255))
    file_type = Column(String(50))
    file_size = Column(Integer)
    download_source = Column(String(100))  # resume, portfolio, etc.
    ip_address = Column(String(45))
    user_agent = Column(Text)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class SearchQuery(Base):
    __tablename__ = "search_queries"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    query = Column(String(500))
    results_count = Column(Integer)
    clicked_result = Column(String(500))
    search_source = Column(String(50))  # site_search, chatbot, etc.
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class ChatbotInteraction(Base):
    __tablename__ = "chatbot_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    user_message = Column(Text)
    bot_response = Column(Text)
    response_time = Column(Float)  # seconds
    user_satisfaction = Column(Integer)  # 1-5 rating
    conversation_length = Column(Integer)  # number of messages
    resolved = Column(Boolean, default=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class PerformanceMetrics(Base):
    __tablename__ = "performance_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    page_url = Column(String(500))
    load_time = Column(Float)  # seconds
    first_contentful_paint = Column(Float)
    largest_contentful_paint = Column(Float)
    cumulative_layout_shift = Column(Float)
    first_input_delay = Column(Float)
    connection_type = Column(String(50))
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class ErrorLog(Base):
    __tablename__ = "error_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    error_type = Column(String(100))
    error_message = Column(Text)
    stack_trace = Column(Text)
    page_url = Column(String(500))
    user_agent = Column(Text)
    browser_info = Column(JSON)
    severity = Column(String(20))  # low, medium, high, critical
    resolved = Column(Boolean, default=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
