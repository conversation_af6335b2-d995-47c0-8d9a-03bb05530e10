import React, { useRef, useMemo, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Cylinder, Box } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import { scaleLinear } from 'd3-scale';
import { interpolateSpectral, interpolateViridis } from 'd3-scale-chromatic';

interface ProjectData {
  name: string;
  value: number;
  color: string;
  description?: string;
}

interface Advanced3DChartsProps {
  data: ProjectData[];
  isDarkMode: boolean;
  type: '3d-pie' | '3d-bar' | 'holographic';
}

// 3D Pie Slice Component
const PieSlice3D: React.FC<{
  startAngle: number;
  endAngle: number;
  value: number;
  total: number;
  color: string;
  label: string;
  isDarkMode: boolean;
}> = ({ startAngle, endAngle, value, total, color, label, isDarkMode }) => {
  const meshRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);
  
  const radius = 3;
  const height = (value / total) * 2 + 0.5;
  const midAngle = (startAngle + endAngle) / 2;
  
  useFrame((state) => {
    if (meshRef.current) {
      const targetY = hovered ? 0.5 : 0;
      meshRef.current.position.y = THREE.MathUtils.lerp(meshRef.current.position.y, targetY, 0.1);
      
      // Subtle rotation animation
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.02;
    }
  });

  // Create pie slice geometry
  const geometry = useMemo(() => {
    const shape = new THREE.Shape();
    shape.moveTo(0, 0);
    shape.lineTo(radius, 0);
    shape.absarc(0, 0, radius, 0, endAngle - startAngle, false);
    shape.lineTo(0, 0);
    
    const extrudeSettings = {
      depth: height,
      bevelEnabled: true,
      bevelSegments: 8,
      steps: 2,
      bevelSize: 0.1,
      bevelThickness: 0.1
    };
    
    return new THREE.ExtrudeGeometry(shape, extrudeSettings);
  }, [startAngle, endAngle, height, radius]);

  return (
    <group
      ref={meshRef}
      rotation={[0, startAngle, 0]}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <mesh geometry={geometry}>
        <meshStandardMaterial
          color={color}
          emissive={color}
          emissiveIntensity={hovered ? 0.3 : 0.1}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>
      
      {hovered && (
        <Text
          position={[
            Math.cos(midAngle) * (radius + 1),
            height / 2,
            Math.sin(midAngle) * (radius + 1)
          ]}
          fontSize={0.3}
          color={isDarkMode ? '#ffffff' : '#000000'}
          anchorX="center"
          anchorY="middle"
        >
          {label}
          {'\n'}
          {value} projects
        </Text>
      )}
    </group>
  );
};

// 3D Bar Component
const Bar3D: React.FC<{
  position: [number, number, number];
  height: number;
  color: string;
  label: string;
  value: number;
  isDarkMode: boolean;
}> = ({ position, height, color, label, value, isDarkMode }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  
  useFrame((state) => {
    if (meshRef.current) {
      const targetScale = hovered ? 1.1 : 1;
      meshRef.current.scale.x = THREE.MathUtils.lerp(meshRef.current.scale.x, targetScale, 0.1);
      meshRef.current.scale.z = THREE.MathUtils.lerp(meshRef.current.scale.z, targetScale, 0.1);
      
      // Pulsing effect
      const pulse = Math.sin(state.clock.elapsedTime * 2) * 0.05 + 1;
      meshRef.current.scale.y = pulse;
    }
  });

  return (
    <group position={position}>
      <Box
        ref={meshRef}
        args={[0.8, height, 0.8]}
        position={[0, height / 2, 0]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color={color}
          emissive={color}
          emissiveIntensity={hovered ? 0.4 : 0.2}
          roughness={0.2}
          metalness={0.8}
        />
      </Box>
      
      {/* Particle effects */}
      <ParticleColumn height={height} color={color} active={hovered} />
      
      {hovered && (
        <Text
          position={[0, height + 1, 0]}
          fontSize={0.25}
          color={isDarkMode ? '#ffffff' : '#000000'}
          anchorX="center"
          anchorY="middle"
        >
          {label}
          {'\n'}
          {value} projects
        </Text>
      )}
      
      {/* Base label */}
      <Text
        position={[0, -0.5, 0]}
        fontSize={0.2}
        color={isDarkMode ? '#cccccc' : '#666666'}
        anchorX="center"
        anchorY="middle"
        rotation={[-Math.PI / 2, 0, 0]}
      >
        {label.split(' ')[0]}
      </Text>
    </group>
  );
};

// Particle Column Effect
const ParticleColumn: React.FC<{ height: number; color: string; active: boolean }> = ({ 
  height, 
  color, 
  active 
}) => {
  const particlesRef = useRef<THREE.Group>(null);
  
  const particles = useMemo(() => {
    const count = active ? 20 : 10;
    const temp = [];
    for (let i = 0; i < count; i++) {
      temp.push({
        position: [
          (Math.random() - 0.5) * 2,
          Math.random() * height,
          (Math.random() - 0.5) * 2
        ] as [number, number, number],
        scale: Math.random() * 0.05 + 0.02
      });
    }
    return temp;
  }, [height, active]);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.children.forEach((child, index) => {
        child.position.y += 0.02;
        if (child.position.y > height + 1) {
          child.position.y = -0.5;
        }
        child.rotation.x += 0.02;
        child.rotation.y += 0.01;
      });
    }
  });

  return (
    <group ref={particlesRef}>
      {particles.map((particle, index) => (
        <Box key={index} position={particle.position} args={[particle.scale, particle.scale, particle.scale]}>
          <meshBasicMaterial color={color} transparent opacity={0.6} />
        </Box>
      ))}
    </group>
  );
};

// Holographic Display Component
const HolographicDisplay: React.FC<{ data: ProjectData[]; isDarkMode: boolean }> = ({ data, isDarkMode }) => {
  const groupRef = useRef<THREE.Group>(null);
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;
    }
  });

  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <group ref={groupRef}>
      {data.map((item, index) => {
        const angle = (index / data.length) * Math.PI * 2;
        const radius = 4;
        const height = (item.value / maxValue) * 3;
        
        return (
          <group key={item.name}>
            {/* Holographic cylinder */}
            <Cylinder
              position={[
                Math.cos(angle) * radius,
                height / 2,
                Math.sin(angle) * radius
              ]}
              args={[0.3, 0.3, height, 16]}
            >
              <meshStandardMaterial
                color={item.color}
                emissive={item.color}
                emissiveIntensity={0.5}
                transparent
                opacity={0.8}
                roughness={0.1}
                metalness={0.9}
              />
            </Cylinder>
            
            {/* Holographic rings */}
            {[0.25, 0.5, 0.75, 1].map((ratio, ringIndex) => (
              <mesh
                key={ringIndex}
                position={[
                  Math.cos(angle) * radius,
                  height * ratio,
                  Math.sin(angle) * radius
                ]}
                rotation={[Math.PI / 2, 0, 0]}
              >
                <ringGeometry args={[0.4, 0.5, 16]} />
                <meshBasicMaterial
                  color={item.color}
                  transparent
                  opacity={0.3}
                  side={THREE.DoubleSide}
                />
              </mesh>
            ))}
            
            {/* Data label */}
            <Text
              position={[
                Math.cos(angle) * (radius + 1.5),
                height + 0.5,
                Math.sin(angle) * (radius + 1.5)
              ]}
              fontSize={0.2}
              color={isDarkMode ? '#00ffff' : '#0066cc'}
              anchorX="center"
              anchorY="middle"
            >
              {item.name}
              {'\n'}
              {item.value}
            </Text>
          </group>
        );
      })}
      
      {/* Central core */}
      <Box args={[0.5, 0.5, 0.5]}>
        <meshStandardMaterial
          color={isDarkMode ? '#00ffff' : '#0066cc'}
          emissive={isDarkMode ? '#004444' : '#002244'}
          emissiveIntensity={0.5}
        />
      </Box>
    </group>
  );
};

// Main Advanced 3D Charts Component
const Advanced3DCharts: React.FC<Advanced3DChartsProps> = ({ data, isDarkMode, type }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  const renderChart = () => {
    switch (type) {
      case '3d-pie':
        let currentAngle = 0;
        return data.map((item, index) => {
          const sliceAngle = (item.value / total) * Math.PI * 2;
          const startAngle = currentAngle;
          const endAngle = currentAngle + sliceAngle;
          currentAngle = endAngle;
          
          return (
            <PieSlice3D
              key={item.name}
              startAngle={startAngle}
              endAngle={endAngle}
              value={item.value}
              total={total}
              color={item.color}
              label={item.name}
              isDarkMode={isDarkMode}
            />
          );
        });
        
      case '3d-bar':
        const maxValue = Math.max(...data.map(d => d.value));
        return data.map((item, index) => (
          <Bar3D
            key={item.name}
            position={[(index - data.length / 2) * 2, 0, 0]}
            height={(item.value / maxValue) * 4}
            color={item.color}
            label={item.name}
            value={item.value}
            isDarkMode={isDarkMode}
          />
        ));
        
      case 'holographic':
        return <HolographicDisplay data={data} isDarkMode={isDarkMode} />;
        
      default:
        return null;
    }
  };

  return (
    <div className="w-full h-96 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 dark:from-black dark:via-gray-900 dark:to-purple-900">
      <Canvas
        camera={{ position: [8, 6, 8], fov: 60 }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.4} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#ffffff" />
        <pointLight position={[-10, 5, -10]} intensity={0.5} color="#ff6b6b" />
        <pointLight position={[0, -10, 0]} intensity={0.3} color="#4ecdc4" />
        
        {renderChart()}
        
        <OrbitControls
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          autoRotate={true}
          autoRotateSpeed={1}
          maxPolarAngle={Math.PI / 2}
          minDistance={5}
          maxDistance={15}
        />
        
        {/* Grid floor */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
          <planeGeometry args={[20, 20]} />
          <meshBasicMaterial
            color={isDarkMode ? '#111111' : '#f0f0f0'}
            transparent
            opacity={0.1}
            wireframe
          />
        </mesh>
      </Canvas>
    </div>
  );
};

export default Advanced3DCharts;
