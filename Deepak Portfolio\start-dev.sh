#!/bin/bash

# Development startup script for Deepak's Portfolio
# This script will start both frontend and backend in development mode

echo "🚀 Starting Deepak's Portfolio Development Environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is required but not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is required but not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please update it with your configuration."
fi

# Start services with Docker Compose
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for MySQL database to be ready..."
sleep 15

# Run database migrations and seed data
echo "🌱 Setting up database..."
docker-compose exec backend python -c "
from app.core.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)
print('Database tables created successfully!')
"

# Seed database with sample data
echo "🌱 Seeding database with sample data..."
docker-compose exec backend python seed_database.py

# Show running services
echo "✅ Development environment is ready!"
echo ""
echo "📊 Running Services:"
echo "   🌐 Frontend: http://localhost:3000"
echo "   🔧 Backend API: http://localhost:8000"
echo "   📚 API Documentation: http://localhost:8000/docs"
echo "   🗄️ Database: localhost:3307 (portfolio_db)"
echo ""
echo "🔧 Useful Commands:"
echo "   - Stop all services: docker-compose down"
echo "   - View logs: docker-compose logs -f"
echo "   - Restart services: docker-compose restart"
echo "   - Build and restart: docker-compose up --build"
echo ""
echo "Happy coding! 🎉"
