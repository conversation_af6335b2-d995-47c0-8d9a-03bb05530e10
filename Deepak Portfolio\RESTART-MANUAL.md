# Portfolio Clean Restart Manual
# Author: <PERSON><PERSON> Garg
# Last Updated: July 5, 2025

## Overview
This manual provides comprehensive instructions for cleaning and restarting your portfolio application. Use this when you encounter build issues, dependency conflicts, or need a fresh start.

## Quick Start
### Windows (PowerShell)
```powershell
# Make script executable and run
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\restart-clean.ps1
```

### Linux/Mac (Bash)
```bash
# Make script executable and run
chmod +x restart-clean.sh
./restart-clean.sh
```

## Manual Step-by-Step Process

### 1. Stop All Running Processes
**Windows:**
```powershell
# Stop processes on specific ports
Get-NetTCPConnection -LocalPort 3000 | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force }
Get-NetTCPConnection -LocalPort 8000 | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force }

# Kill Node.js processes
Get-Process -Name "node" | Stop-Process -Force
```

**Linux/Mac:**
```bash
# Kill processes on specific ports
lsof -t -i:3000 | xargs kill -9
lsof -t -i:8000 | xargs kill -9

# Kill Node.js processes
pkill -f "node"
pkill -f "npm"
```

### 2. Clean Project Files
**Frontend Directory:**
```bash
cd frontend
rm -rf node_modules
rm -f package-lock.json yarn.lock pnpm-lock.yaml
rm -rf dist build .next .vite .turbo out
rm -rf .cache node_modules/.cache .vite-cache
cd ..
```

**Backend Directory:**
```bash
cd backend
rm -rf __pycache__ .pytest_cache *.pyc .coverage htmlcov
rm -rf venv
cd ..
```

**Project Root:**
```bash
rm -rf node_modules
rm -f package-lock.json yarn.lock pnpm-lock.yaml
```

### 3. Clear System Caches
```bash
# npm cache
npm cache clean --force

# Yarn cache
yarn cache clean

# pnpm cache
pnpm store prune
```

### 4. Fresh Installation
**Frontend:**
```bash
cd frontend
npm install
cd ..
```

**Backend:**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# OR
.\venv\Scripts\Activate.ps1  # Windows
pip install -r requirements.txt
cd ..
```

### 5. Start Development Servers
**Backend (Terminal 1):**
```bash
cd backend
source venv/bin/activate  # Linux/Mac
# OR
.\venv\Scripts\Activate.ps1  # Windows
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend (Terminal 2):**
```bash
cd frontend
npm run dev
```

## Troubleshooting

### Common Issues and Solutions

**1. Port Already in Use**
```bash
# Find process using port
lsof -i :3000  # Linux/Mac
netstat -ano | findstr :3000  # Windows

# Kill process
kill -9 <PID>  # Linux/Mac
taskkill /PID <PID> /F  # Windows
```

**2. Permission Errors**
```bash
# Linux/Mac
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) node_modules

# Windows (Run as Administrator)
icacls node_modules /grant Everyone:F /T
```

**3. Python Virtual Environment Issues**
```bash
# Remove and recreate venv
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

**4. Node.js Version Conflicts**
```bash
# Check Node.js version
node --version

# Install Node Version Manager (nvm)
# Linux/Mac
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Windows
# Download and install from nodejs.org
```

## System Requirements
- **Node.js**: 18.x or higher
- **Python**: 3.8 or higher
- **npm**: 8.x or higher
- **Git**: Latest version

## Environment Variables
Create `.env` files in both frontend and backend directories:

**Frontend (.env):**
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=development
```

**Backend (.env):**
```env
DATABASE_URL=sqlite:///./portfolio.db
SECRET_KEY=your-secret-key-here
API_HOST=0.0.0.0
API_PORT=8000
```

## Project Structure
```
portfolio/
├── frontend/
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── .env
├── backend/
│   ├── app/
│   ├── requirements.txt
│   ├── venv/
│   └── .env
├── restart-clean.ps1
├── restart-clean.sh
├── stop-servers.ps1
├── stop-servers.sh
└── README.md
```

## Available Scripts

### Restart Scripts
- `restart-clean.ps1` - Full clean restart (Windows)
- `restart-clean.sh` - Full clean restart (Linux/Mac)
- `stop-servers.ps1` - Stop all servers (Windows)
- `stop-servers.sh` - Stop all servers (Linux/Mac)

### Development Commands
```bash
# Frontend
npm run dev        # Start development server
npm run build      # Build for production
npm run test       # Run tests
npm run lint       # Run linter

# Backend
python -m uvicorn app.main:app --reload  # Start development server
python -m pytest  # Run tests
python -m flake8   # Run linter
```

## Performance Optimization
1. **Enable caching**: Use npm ci instead of npm install in CI/CD
2. **Use .gitignore**: Exclude node_modules, venv, build folders
3. **Monitor resources**: Use Task Manager/Activity Monitor during development
4. **Regular cleanup**: Run restart scripts weekly

## Security Considerations
- Keep dependencies updated: `npm audit fix`
- Use environment variables for sensitive data
- Enable CORS properly in backend
- Use HTTPS in production

## Backup Strategy
Before major changes:
```bash
# Create backup
tar -czf portfolio-backup-$(date +%Y%m%d).tar.gz \
  --exclude=node_modules \
  --exclude=venv \
  --exclude=.git \
  .
```

## Support
If you encounter issues not covered in this manual:
1. Check log files (frontend.log, backend.log)
2. Review error messages in terminal
3. Verify system requirements
4. Check firewall/antivirus settings
5. Try running as administrator/sudo if needed

## Changelog
- **v1.0** (2025-07-05): Initial manual creation
- Added comprehensive restart scripts
- Included troubleshooting section
- Added environment setup instructions

---
*This manual is part of Deepak Garg's Portfolio Project*
*For updates, check: https://github.com/your-username/portfolio*
