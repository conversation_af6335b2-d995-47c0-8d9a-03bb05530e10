import React, { useState, useEffect } from 'react';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

interface Skill {
  name: string;
  level: number;
  category: string;
  icon: string;
}

const AnimatedSkills: React.FC = () => {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>('All');
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  const skillsData: Skill[] = [
    // Core Data Science
    { name: 'Machine Learning', level: 92, category: 'Data Science', icon: '🧠' },
    { name: 'Statistical Analysis', level: 89, category: 'Data Science', icon: '📊' },
    { name: 'Predictive Modeling', level: 88, category: 'Data Science', icon: '🔮' },
    { name: 'Feature Engineering', level: 85, category: 'Data Science', icon: '⚙️' },
    { name: 'Time Series Analysis', level: 83, category: 'Data Science', icon: '📈' },
    
    // Programming & Tools
    { name: 'Python', level: 94, category: 'Programming', icon: '🐍' },
    { name: 'SQL', level: 90, category: 'Programming', icon: '🗄️' },
    { name: 'R Programming', level: 78, category: 'Programming', icon: '📊' },
    { name: 'Git & Version Control', level: 86, category: 'Programming', icon: '🔧' },
    
    // Machine Learning Libraries
    { name: 'Scikit-learn', level: 91, category: 'ML Libraries', icon: '🤖' },
    { name: 'Pandas & NumPy', level: 93, category: 'ML Libraries', icon: '🐼' },
    { name: 'TensorFlow', level: 82, category: 'ML Libraries', icon: '🔥' },
    { name: 'XGBoost', level: 87, category: 'ML Libraries', icon: '🚀' },
    
    // Data Visualization
    { name: 'Matplotlib & Seaborn', level: 88, category: 'Visualization', icon: '📊' },
    { name: 'Plotly', level: 85, category: 'Visualization', icon: '📈' },
    { name: 'Tableau', level: 80, category: 'Visualization', icon: '📋' },
    { name: 'Power BI', level: 82, category: 'Visualization', icon: '💼' },
    
    // Specialized Areas
    { name: 'Natural Language Processing', level: 84, category: 'AI/ML', icon: '💬' },
    { name: 'Recommendation Systems', level: 89, category: 'AI/ML', icon: '⭐' },
    { name: 'Fraud Detection', level: 86, category: 'AI/ML', icon: '🛡️' },
    { name: 'A/B Testing', level: 81, category: 'Analytics', icon: '🧪' },
    
    // Data Engineering
    { name: 'ETL Pipelines', level: 83, category: 'Data Engineering', icon: '🔄' },
    { name: 'Apache Airflow', level: 79, category: 'Data Engineering', icon: '🌊' },
    { name: 'Data Warehousing', level: 77, category: 'Data Engineering', icon: '🏢' },
    
    // Cloud & Deployment
    { name: 'AWS', level: 81, category: 'Cloud', icon: '☁️' },
    { name: 'Docker', level: 78, category: 'DevOps', icon: '🐳' },
    { name: 'Model Deployment', level: 84, category: 'MLOps', icon: '🚀' }
  ];

  const categories = ['All', ...Array.from(new Set(skillsData.map(skill => skill.category)))];

  useEffect(() => {
    if (activeCategory === 'All') {
      setSkills(skillsData);
    } else {
      setSkills(skillsData.filter(skill => skill.category === activeCategory));
    }
  }, [activeCategory]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  };

  const progressVariants = {
    hidden: { width: 0 },
    visible: (level: number) => ({
      width: `${level}%`,
      transition: { duration: 1.5, ease: "easeOut" }
    })
  };

  return (
    <section className="section-padding bg-white dark:bg-gray-900" ref={ref}>
      <div className="container-custom">
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-12"
        >
          <motion.h2 
            variants={itemVariants}
            className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
          >
            Technical <span className="text-gradient">Expertise</span>
          </motion.h2>
          <motion.p 
            variants={itemVariants}
            className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto"
          >
            Comprehensive skill set spanning data science, machine learning, and modern development tools
          </motion.p>
        </motion.div>

        {/* Category Filter */}
        <motion.div 
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="flex flex-wrap justify-center gap-2 mb-8"
        >
          {categories.map((category) => (
            <motion.button
              key={category}
              variants={itemVariants}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>

        {/* Skills Grid */}
        <motion.div 
          layout
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {skills.map((skill, index) => (
            <motion.div
              key={skill.name}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300"
              whileHover={{ y: -5 }}
            >
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">{skill.icon}</span>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                    {skill.name}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {skill.category}
                  </p>
                </div>
                <span className="text-sm font-bold text-blue-500">
                  {skill.level}%
                </span>
              </div>
              
              <div className="relative">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                    initial="hidden"
                    animate={isInView ? "visible" : "hidden"}
                    variants={progressVariants}
                    custom={skill.level}
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Skills Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ delay: 0.5 }}
          className="mt-12 text-center"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-lg text-white">
              <div className="text-2xl font-bold">{skillsData.length}+</div>
              <div className="text-sm opacity-90">Technical Skills</div>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-blue-500 p-4 rounded-lg text-white">
              <div className="text-2xl font-bold">{categories.length - 1}</div>
              <div className="text-sm opacity-90">Skill Categories</div>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-4 rounded-lg text-white">
              <div className="text-2xl font-bold">3+</div>
              <div className="text-sm opacity-90">Years Experience</div>
            </div>
            <div className="bg-gradient-to-r from-orange-500 to-red-500 p-4 rounded-lg text-white">
              <div className="text-2xl font-bold">15+</div>
              <div className="text-sm opacity-90">Projects Completed</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AnimatedSkills;
