@echo off
REM Development startup script for Deepak's Portfolio (Windows)
REM This script will start both frontend and backend in development mode

echo 🚀 Starting Deepak's Portfolio Development Environment...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is required but not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is required but not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ✅ .env file created. Please update it with your configuration.
)

REM Start services with Docker Compose
echo 🐳 Starting Docker containers...
docker-compose up -d

REM Wait for database to be ready
echo ⏳ Waiting for database to be ready...
timeout /t 10 /nobreak >nul

REM Run database migrations and seed data
echo 🌱 Setting up database...
docker-compose exec backend python -c "from app.core.database import engine; from app.models import Base; Base.metadata.create_all(bind=engine); print('Database tables created successfully!')"

REM Seed database with sample data
echo 🌱 Seeding database with sample data...
docker-compose exec backend python seed_database.py

REM Show running services
echo ✅ Development environment is ready!
echo.
echo 📊 Running Services:
echo    🌐 Frontend: http://localhost:3000
echo    🔧 Backend API: http://localhost:8000
echo    📚 API Documentation: http://localhost:8000/docs
echo    🗄️ Database: localhost:5432 (portfolio_db)
echo.
echo 🔧 Useful Commands:
echo    - Stop all services: docker-compose down
echo    - View logs: docker-compose logs -f
echo    - Restart services: docker-compose restart
echo    - Build and restart: docker-compose up --build
echo.
echo Happy coding! 🎉
pause
