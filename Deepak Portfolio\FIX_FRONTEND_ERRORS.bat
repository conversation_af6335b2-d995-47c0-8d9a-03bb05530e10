@echo off
echo 🔧 FIXING FRONTEND ERRORS...
echo.

echo ✅ Step 1: Stopping any running containers...
docker-compose down

echo ✅ Step 2: Rebuilding frontend with fixes...
docker-compose build frontend --no-cache

echo ✅ Step 3: Starting everything fresh...
docker-compose up -d

echo ✅ Step 4: Waiting for services to start...
timeout /t 15 /nobreak >nul

echo ✅ Step 5: Loading professional content...
docker-compose exec -T backend python seed_database.py

echo.
echo 🎉 FRONTEND ERRORS FIXED!
echo.
echo 🌐 Your portfolio should now work at:
echo    http://localhost:3000
echo.

echo Opening portfolio...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ All fixed! Your portfolio is now running without errors.
pause
