"""
Database seeding script for Deepak Garg's AI/ML Portfolio
Populates the database with Deepak's real projects, skills, experience, and achievements.
Run this script to initialize the database with professional AI/Data Science portfolio data.
"""

import asyncio
from datetime import datetime, timedelta
from app.core.database import SessionLocal, engine
from app.models import (
    Base, Project, BlogPost, Skill, Experience, Education, 
    Service, Testimonial, User
)
from sqlalchemy.orm import Session

# Create all tables
Base.metadata.create_all(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        return db
    finally:
        pass

def seed_database():
    db = get_db()
    
    try:
        # Clear existing data
        db.query(Project).delete()
        db.query(BlogPost).delete()
        db.query(Skill).delete()
        db.query(Experience).delete()
        db.query(Education).delete()
        db.query(Service).delete()
        db.query(Testimonial).delete()
        db.commit()
        
        # Seed Projects - Deepak Garg's AI/ML Projects
        projects = [
            Project(
                title="WAVES Media Recommendation System",
                description="Built an intelligent content recommendation engine for government media platform WAVES using collaborative filtering and content-based algorithms. Implemented real-time personalization with 89% accuracy, increasing user engagement by 35% and content discovery by 42%.",
                technologies="Python, Scikit-learn, Pandas, Flask, Redis, Collaborative Filtering, Content-Based Filtering, Government Platform",
                github_url="https://github.com/mrgarg-g1/waves-recommendation",
                live_url="",
                image_url="https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&q=80",
                category="Data Science",
                featured=True
            ),
            Project(
                title="Fraud Detection System",
                description="Developed a machine learning model to detect fraudulent transactions using ensemble methods and anomaly detection. Achieved 94% accuracy with minimal false positives, helping reduce fraud losses by 40% for a fintech startup.",
                technologies="Python, XGBoost, Random Forest, Isolation Forest, Pandas, Scikit-learn, Feature Engineering",
                github_url="https://github.com/mrgarg-g1/fraud-detection",
                live_url="",
                image_url="https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&q=80",
                category="Data Science",
                featured=True
            ),
            Project(
                title="Real-time Facial Recognition Attendance System",
                description="Developed CNN-based facial recognition system with 95% accuracy using OpenCV and TensorFlow. Integrated with MySQL database and Flask web interface, reducing manual attendance errors by 98% and improving security protocols.",
                technologies="OpenCV, TensorFlow, CNN, Flask, MySQL, Computer Vision, Real-time Processing",
                github_url="https://github.com/mrgarg-g1/face-recognition-attendance",
                live_url="",
                image_url="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&q=80",
                category="AI/ML",
                featured=True
            ),
            Project(
                title="BERT-powered Document Classification Pipeline",
                description="Built intelligent document classification system using BERT transformers for legal and insurance documents. Implemented custom NER model with SpaCy for entity extraction, achieving 92% classification accuracy and reducing manual review time by 60%.",
                technologies="BERT, SpaCy, NER, Document Classification, Flask APIs, Legal Tech, Insurance AI",
                github_url="https://github.com/mrgarg-g1/document-classifier",
                live_url="",
                image_url="https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Intelligent Inventory Optimization Dashboard",
                description="Created ML-powered inventory forecasting system using ARIMA and Prophet models. Built interactive Streamlit dashboard with Power BI integration, improving procurement efficiency by 20% and reducing stock wastage through predictive analytics.",
                technologies="ARIMA, Prophet, Power BI, Streamlit, Time-series Forecasting, Predictive Analytics",
                github_url="https://github.com/mrgarg-g1/inventory-optimization",
                live_url="",
                image_url="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80",
                category="Data Science",
                featured=False
            ),
            Project(
                title="Automated ETL Pipeline with ML Validation",
                description="Architected robust ETL pipelines using Selenium, BeautifulSoup, and Airflow with automated data validation using Great Expectations. Reduced manual intervention by 90% and processing time by 40% through intelligent scheduling and monitoring.",
                technologies="Selenium, BeautifulSoup, Airflow, Great Expectations, Pandas, SQLAlchemy, ETL",
                github_url="https://github.com/mrgarg-g1/web-scraping-framework",
                live_url="",
                image_url="https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&q=80",
                category="Data Engineering",
                featured=False
            ),
            Project(
                title="Customer Churn Prediction Model",
                description="Built a predictive model to identify customers likely to churn using machine learning algorithms. Achieved 87% accuracy with feature engineering and hyperparameter tuning, helping the business reduce churn by 25% through targeted retention campaigns.",
                technologies="Python, Logistic Regression, Random Forest, XGBoost, Feature Engineering, Hyperparameter Tuning",
                github_url="https://github.com/mrgarg-g1/churn-prediction",
                live_url="",
                image_url="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80",
                category="Data Science",
                featured=True
            ),
            Project(
                title="Sales Forecasting Dashboard",
                description="Created an interactive dashboard for sales forecasting using time series analysis and machine learning. Implemented ARIMA and Prophet models achieving 91% accuracy, helping the sales team make data-driven decisions and improve planning.",
                technologies="Python, ARIMA, Prophet, Streamlit, Plotly, Time Series Analysis, Statistical Modeling",
                github_url="https://github.com/mrgarg-g1/sales-forecasting",
                live_url="",
                image_url="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80",
                category="Data Science",
                featured=False
            ),
            Project(
                title="Sentiment Analysis for Social Media",
                description="Built a sentiment analysis system for social media monitoring using NLP techniques. Processed thousands of social media posts daily with 88% accuracy, providing valuable insights for brand reputation management and marketing strategies.",
                technologies="Python, NLTK, TextBlob, Scikit-learn, Twitter API, NLP, Sentiment Analysis",
                github_url="https://github.com/mrgarg-g1/sentiment-analysis",
                live_url="",
                image_url="https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&q=80",
                category="Data Science",
                featured=False
            ),
            Project(
                title="Advanced OCR Document Processing System",
                description="Developed an intelligent OCR system using Tesseract and OpenCV for automated document digitization. Implemented preprocessing algorithms and post-processing validation achieving 96% accuracy on complex documents including handwritten text recognition.",
                technologies="Python, Tesseract, OpenCV, PIL, Image Processing, OCR, Document AI, Text Recognition",
                github_url="https://github.com/mrgarg-g1/advanced-ocr",
                live_url="",
                image_url="https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80",
                category="AI/ML",
                featured=True
            ),
            Project(
                title="Real-time Anomaly Detection System",
                description="Built a machine learning system for real-time anomaly detection in network traffic and system logs. Used isolation forests and statistical methods to identify suspicious patterns with 92% accuracy, reducing security incident response time by 60%.",
                technologies="Python, Isolation Forest, Statistical Analysis, Real-time Processing, Kafka, Elasticsearch, Security Analytics",
                github_url="https://github.com/mrgarg-g1/anomaly-detection",
                live_url="",
                image_url="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&q=80",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Intelligent Autofill & Suggestion Engine",
                description="Created an AI-powered autofill system using NLP and machine learning for form completion and smart suggestions. Implemented context-aware predictions reducing user input time by 45% and improving data quality through intelligent validation.",
                technologies="Python, NLP, Machine Learning, Context Analysis, Fuzzy Matching, Auto-completion, Smart Suggestions",
                github_url="https://github.com/mrgarg-g1/autofill-engine",
                live_url="",
                image_url="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&q=80",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Automated Content Categorization System",
                description="Developed an ML-based content categorization system using BERT and custom classification models. Automatically categorizes documents, emails, and content with 91% accuracy, processing 10K+ items daily for content management systems.",
                technologies="Python, BERT, Transformers, Text Classification, NLP, Content Management, Automated Categorization",
                github_url="https://github.com/mrgarg-g1/auto-categorization",
                live_url="",
                image_url="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&q=80",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Speech Analysis & Emotion Recognition",
                description="Built a comprehensive speech analysis system for emotion recognition and speaker identification. Implemented audio feature extraction and deep learning models achieving 87% accuracy in emotion detection for customer service analytics.",
                technologies="Python, Librosa, TensorFlow, Audio Processing, Emotion Recognition, Speaker Identification, Deep Learning",
                github_url="https://github.com/mrgarg-g1/speech-analysis",
                live_url="",
                image_url="https://images.unsplash.com/photo-1589254065878-42c9da997008?w=800&q=80",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Bhashini TTS Integration System",
                description="Integrated Bhashini Text-to-Speech API for multilingual voice synthesis supporting 12+ Indian languages. Built scalable TTS service with custom voice models and real-time audio generation for accessibility and language learning applications.",
                technologies="Python, Bhashini API, Text-to-Speech, Multilingual TTS, Audio Processing, API Integration, Voice Synthesis",
                github_url="https://github.com/mrgarg-g1/bhashini-tts",
                live_url="",
                image_url="https://images.unsplash.com/photo-1590736969955-71cc94901144?w=800&q=80",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Automated Data Pipeline",
                description="Designed and implemented an automated ETL pipeline for data processing and analysis. Reduced manual data processing time by 80% and improved data quality through automated validation and error handling mechanisms.",
                technologies="Python, Apache Airflow, Pandas, SQL, Data Validation, ETL, Automation",
                github_url="https://github.com/mrgarg-g1/data-pipeline",
                live_url="",
                image_url="https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&q=80",
                category="Data Engineering",
                featured=False
            )
        ]
        
        for project in projects:
            db.add(project)
        
        # Seed Blog Posts - AI/ML & Data Science Insights
        blog_posts = [
            BlogPost(
                title="Fine-tuning LLaMA 3.1 for Government Policy Q&A: A Complete Guide",
                content="<p>Large Language Models have revolutionized how we interact with complex information, but getting them to understand specialized domains requires careful fine-tuning...</p>",
                excerpt="Deep dive into fine-tuning Meta's LLaMA 3.1-8B model for government policy understanding. Learn about LoRA techniques, RAG pipelines, and achieving 92% accuracy in policy Q&A systems.",
                slug="fine-tuning-llama-government-policy-qa",
                image_url="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80",
                published=True
            ),
            BlogPost(
                title="Building Semantic Video Search with AWS: From Transcription to Discovery",
                content="<p>Video content is exploding, but finding specific moments within hours of footage remains challenging. Here's how I built a semantic video search engine using AWS services...</p>",
                excerpt="Learn how to architect a scalable semantic video search system using AWS Transcribe, SageMaker embeddings, and OpenSearch. Achieve 95% accuracy in timestamp-based video discovery.",
                slug="semantic-video-search-aws-architecture",
                image_url="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80",
                published=True
            ),
            BlogPost(
                title="Multi-Cloud ML: Deploying Models Across AWS and Azure for Maximum Reliability",
                content="<p>In enterprise environments, vendor lock-in is a real concern. Here's how I architected a multi-cloud ML system that leverages both AWS SageMaker and Azure ML...</p>",
                excerpt="Explore strategies for deploying machine learning models across multiple cloud platforms. Real-world case study of a churn prediction system that saves $2M+ annually.",
                slug="multi-cloud-ml-deployment-strategy",
                image_url="https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&q=80",
                published=True
            ),
            BlogPost(
                title="From ETL to Intelligence: Building Self-Healing Data Pipelines",
                content="<p>Data pipelines break. It's not a matter of if, but when. Here's how I built intelligent ETL systems that detect, diagnose, and often fix themselves...</p>",
                excerpt="Discover how to build robust ETL pipelines with automated validation using Great Expectations and Airflow. Reduce manual intervention by 90% and processing time by 40%.",
                slug="self-healing-data-pipelines-airflow",
                image_url="https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=800&q=80",
                published=True
            )
        ]
        
        for post in blog_posts:
            db.add(post)
        
        # Seed Skills - Data Science & AI/ML Expertise
        skills = [
            # Core Data Science
            Skill(name="Machine Learning", level=92, category="Data Science", icon="brain"),
            Skill(name="Statistical Analysis", level=89, category="Data Science", icon="statistics"),
            Skill(name="Predictive Modeling", level=88, category="Data Science", icon="model"),
            Skill(name="Feature Engineering", level=85, category="Data Science", icon="engineering"),
            Skill(name="Time Series Analysis", level=83, category="Data Science", icon="timeseries"),

            # Programming & Tools
            Skill(name="Python", level=94, category="Programming", icon="python"),
            Skill(name="SQL", level=90, category="Programming", icon="database"),
            Skill(name="R Programming", level=78, category="Programming", icon="r"),
            Skill(name="Git & Version Control", level=86, category="Programming", icon="git"),

            # Machine Learning Libraries
            Skill(name="Scikit-learn", level=91, category="ML Libraries", icon="sklearn"),
            Skill(name="Pandas & NumPy", level=93, category="ML Libraries", icon="pandas"),
            Skill(name="TensorFlow", level=82, category="ML Libraries", icon="tensorflow"),
            Skill(name="XGBoost", level=87, category="ML Libraries", icon="xgboost"),

            # Data Visualization
            Skill(name="Matplotlib & Seaborn", level=88, category="Visualization", icon="matplotlib"),
            Skill(name="Plotly", level=85, category="Visualization", icon="plotly"),
            Skill(name="Tableau", level=80, category="Visualization", icon="tableau"),
            Skill(name="Power BI", level=82, category="Visualization", icon="powerbi"),

            # Specialized Areas
            Skill(name="Natural Language Processing", level=84, category="AI/ML", icon="nlp"),
            Skill(name="Recommendation Systems", level=89, category="AI/ML", icon="recommend"),
            Skill(name="Fraud Detection", level=86, category="AI/ML", icon="security"),
            Skill(name="A/B Testing", level=81, category="Analytics", icon="testing"),

            # Data Engineering
            Skill(name="ETL Pipelines", level=83, category="Data Engineering", icon="pipeline"),
            Skill(name="Apache Airflow", level=79, category="Data Engineering", icon="airflow"),
            Skill(name="Data Warehousing", level=77, category="Data Engineering", icon="warehouse"),

            # Cloud & Deployment
            Skill(name="AWS", level=81, category="Cloud", icon="aws"),
            Skill(name="Docker", level=78, category="DevOps", icon="docker"),
            Skill(name="Model Deployment", level=84, category="MLOps", icon="deploy")
        ]
        
        for skill in skills:
            db.add(skill)
        
        # Seed Experience - Deepak Garg's Career Journey
        experiences = [
            Experience(
                company="Appsquadz Technologies",
                position="Data Scientist & AI Engineer",
                description="Developing machine learning models and data analytics solutions for business optimization. Built recommendation systems, fraud detection models, and automated data pipelines. Working on predictive analytics projects that improve business decision-making and operational efficiency.",
                start_date=datetime(2025, 1, 1),
                end_date=None,
                current=True,
                location="Noida, Uttar Pradesh"
            ),
            Experience(
                company="Areness",
                position="Data Analyst",
                description="Analyzed large datasets to extract business insights and built automated reporting systems. Developed data visualization dashboards and implemented ETL processes. Created predictive models for customer behavior analysis and business forecasting.",
                start_date=datetime(2024, 6, 1),
                end_date=datetime(2024, 12, 31),
                current=False,
                location="Gurugram, Haryana"
            ),
            Experience(
                company="Freelance Data Science Projects",
                position="Data Science Consultant",
                description="Worked on various data science projects including recommendation systems, sentiment analysis, and predictive modeling. Helped small businesses implement data-driven solutions and automated their data processing workflows.",
                start_date=datetime(2023, 7, 1),
                end_date=datetime(2024, 5, 31),
                current=False,
                location="Remote"
            ),
            Experience(
                company="D.S. Projects Pvt. Ltd.",
                position="Junior Data Analyst",
                description="Started my data science journey by working on data collection, cleaning, and basic analysis projects. Learned machine learning fundamentals and built my first predictive models. Gained experience in data visualization and statistical analysis.",
                start_date=datetime(2022, 6, 1),
                end_date=datetime(2023, 6, 30),
                current=False,
                location="On-site"
            )
        ]
        
        for exp in experiences:
            db.add(exp)
        
        # Seed Education - Deepak Garg's Academic Background
        education = [
            Education(
                institution="Chandigarh University",
                degree="Bachelor of Technology (B.Tech)",
                field="Computer Science Engineering",
                start_date=datetime(2017, 8, 1),
                end_date=datetime(2021, 6, 30),
                current=False,
                description="Specialized in Data Structures, Algorithms, Machine Learning, and Software Engineering. Graduated with Distinction. Focused on AI/ML coursework and research projects in computer vision and natural language processing."
            ),
            Education(
                institution="Government Senior Secondary School",
                degree="Senior Secondary (12th Grade)",
                field="Science (PCM - Physics, Chemistry, Mathematics)",
                start_date=datetime(2015, 4, 1),
                end_date=datetime(2017, 3, 31),
                current=False,
                description="Science stream with Mathematics, Physics, and Chemistry. Strong foundation in analytical thinking and problem-solving. Scored 88% with distinction in Mathematics and Physics."
            )
        ]
        
        for edu in education:
            db.add(edu)
        
        # Seed Services - Data Science & AI/ML Solutions
        services = [
            Service(
                title="Machine Learning Solutions",
                description="Custom machine learning models for classification, regression, and clustering problems. From data preprocessing to model deployment, delivering accurate and scalable ML solutions.",
                icon="brain",
                featured=True
            ),
            Service(
                title="Data Analytics & Insights",
                description="Transform raw data into actionable business insights through statistical analysis, data visualization, and predictive modeling. Help businesses make data-driven decisions.",
                icon="analytics",
                featured=True
            ),
            Service(
                title="Recommendation Systems",
                description="Build personalized recommendation engines for e-commerce, content platforms, and marketing. Increase user engagement and sales through intelligent product suggestions.",
                icon="recommend",
                featured=True
            ),
            Service(
                title="Fraud Detection Systems",
                description="Develop advanced fraud detection models using machine learning and anomaly detection techniques. Protect businesses from financial losses with real-time monitoring.",
                icon="security",
                featured=True
            ),
            Service(
                title="Predictive Analytics",
                description="Forecast future trends, customer behavior, and business outcomes using time series analysis and predictive modeling. Enable proactive business planning and strategy.",
                icon="forecast",
                featured=True
            ),
            Service(
                title="Data Pipeline Automation",
                description="Design and implement automated ETL pipelines for data processing, cleaning, and transformation. Ensure data quality and reduce manual processing time.",
                icon="pipeline",
                featured=True
            ),
            Service(
                title="Business Intelligence Dashboards",
                description="Create interactive dashboards and reports using tools like Tableau, Power BI, and Streamlit. Visualize complex data in an easy-to-understand format.",
                icon="dashboard",
                featured=False
            ),
            Service(
                title="Natural Language Processing",
                description="Extract insights from text data through sentiment analysis, topic modeling, and text classification. Process and analyze large volumes of textual information.",
                icon="nlp",
                featured=False
            )
        ]
        
        for service in services:
            db.add(service)
        
        # Seed Testimonials - Client Success Stories
        testimonials = [
            Testimonial(
                name="Rajesh Kumar",
                position="Operations Manager",
                company="Local E-commerce Startup",
                content="Deepak built an excellent recommendation system for our e-commerce platform. The personalized product suggestions increased our sales by 22% and improved customer engagement significantly. Great work and professional approach!",
                image_url="https://ui-avatars.com/api/?name=Rajesh+Kumar&background=0D8ABC&color=fff&size=128&font-size=0.6",
                rating=5,
                featured=True
            ),
            Testimonial(
                name="Priya Sharma",
                position="Data Manager",
                company="Fintech Solutions",
                content="The fraud detection model Deepak developed has been incredibly effective. We've seen a 40% reduction in fraudulent transactions with minimal false positives. His analytical skills and attention to detail are impressive.",
                image_url="https://ui-avatars.com/api/?name=Priya+Sharma&background=7C3AED&color=fff&size=128&font-size=0.6",
                rating=5,
                featured=True
            ),
            Testimonial(
                name="Michael Anderson",
                position="Team Lead",
                company="Areness",
                content="Deepak's data analysis and automation skills helped streamline our reporting processes. His ETL pipelines and dashboards saved us hours of manual work every week. Reliable and skilled professional.",
                image_url="https://ui-avatars.com/api/?name=Michael+Anderson&background=059669&color=fff&size=128&font-size=0.6",
                rating=5,
                featured=True
            ),
            Testimonial(
                name="Dr. Anita Gupta",
                position="Project Supervisor",
                company="D.S. Projects Pvt. Ltd.",
                content="Deepak showed great learning ability and dedication during his time with us. His data analysis projects and machine learning models demonstrated strong technical foundation and problem-solving skills.",
                image_url="https://ui-avatars.com/api/?name=Anita+Gupta&background=DC2626&color=fff&size=128&font-size=0.6",
                rating=5,
                featured=False
            ),
            Testimonial(
                name="Development Team",
                position="Colleagues",
                company="Appsquadz Technologies",
                content="Deepak is a dedicated data scientist who brings fresh perspectives to our projects. His machine learning expertise and collaborative approach make him a valuable team member.",
                image_url="https://ui-avatars.com/api/?name=Appsquadz+Team&background=1F2937&color=fff&size=128&font-size=0.6",
                rating=5,
                featured=True
            )
        ]
        
        for testimonial in testimonials:
            db.add(testimonial)
        
        # Commit all changes
        db.commit()
        print("Database seeded successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"Error seeding database: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()
