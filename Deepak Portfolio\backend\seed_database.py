"""
Database seeding script for Deepak Garg's AI/ML Portfolio
Populates the database with Deepak's real projects, skills, experience, and achievements.
Run this script to initialize the database with professional AI/Data Science portfolio data.
"""

import asyncio
from datetime import datetime, timedelta
from app.core.database import SessionLocal, engine
from app.models import (
    Base, Project, BlogPost, Skill, Experience, Education, 
    Service, Testimonial, User
)
from sqlalchemy.orm import Session

# Create all tables
Base.metadata.create_all(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        return db
    finally:
        pass

def seed_database():
    db = get_db()
    
    try:
        # Clear existing data
        db.query(Project).delete()
        db.query(BlogPost).delete()
        db.query(Skill).delete()
        db.query(Experience).delete()
        db.query(Education).delete()
        db.query(Service).delete()
        db.query(Testimonial).delete()
        db.commit()
        
        # Seed Projects - Deepak Garg's AI/ML Projects
        projects = [
            Project(
                title="Semantic Video Search Engine",
                description="Engineered a scalable semantic video search system combining AWS Transcribe, SageMaker embeddings, and OpenSearch for transcript-based navigation and speaker-aware video discovery. Enables precise timestamp-based search with 95% accuracy.",
                technologies="AWS Transcribe, SageMaker, OpenSearch, Bedrock, Python, Vector Embeddings, Semantic Search",
                github_url="https://github.com/mrgarg-g1/semantic-video-search",
                live_url="",
                image_url="/images/projects/semantic-video-search.jpg",
                category="AI/ML",
                featured=True
            ),
            Project(
                title="Mentor Policy Chatbot (Fine-tuned LLaMA)",
                description="Fine-tuned Meta's LLaMA 3.1-8B model using LoRA on government schemes and policy corpus. Built RAG pipeline with LangChain for long-context queries, achieving 92% policy accuracy and instant scheme eligibility responses.",
                technologies="Meta LLaMA 3.1-8B, LoRA, LangChain, RAG, Transformers, Hugging Face, Policy NLP",
                github_url="https://github.com/mrgarg-g1/policy-chatbot",
                live_url="",
                image_url="/images/projects/policy-chatbot.jpg",
                category="AI/ML",
                featured=True
            ),
            Project(
                title="Real-time Facial Recognition Attendance System",
                description="Developed CNN-based facial recognition system with 95% accuracy using OpenCV and TensorFlow. Integrated with MySQL database and Flask web interface, reducing manual attendance errors by 98% and improving security protocols.",
                technologies="OpenCV, TensorFlow, CNN, Flask, MySQL, Computer Vision, Real-time Processing",
                github_url="https://github.com/mrgarg-g1/face-recognition-attendance",
                live_url="",
                image_url="/images/projects/facial-recognition.jpg",
                category="AI/ML",
                featured=True
            ),
            Project(
                title="BERT-powered Document Classification Pipeline",
                description="Built intelligent document classification system using BERT transformers for legal and insurance documents. Implemented custom NER model with SpaCy for entity extraction, achieving 92% classification accuracy and reducing manual review time by 60%.",
                technologies="BERT, SpaCy, NER, Document Classification, Flask APIs, Legal Tech, Insurance AI",
                github_url="https://github.com/mrgarg-g1/document-classifier",
                live_url="",
                image_url="/images/projects/document-classification.jpg",
                category="AI/ML",
                featured=False
            ),
            Project(
                title="Intelligent Inventory Optimization Dashboard",
                description="Created ML-powered inventory forecasting system using ARIMA and Prophet models. Built interactive Streamlit dashboard with Power BI integration, improving procurement efficiency by 20% and reducing stock wastage through predictive analytics.",
                technologies="ARIMA, Prophet, Power BI, Streamlit, Time-series Forecasting, Predictive Analytics",
                github_url="https://github.com/mrgarg-g1/inventory-optimization",
                live_url="",
                image_url="/images/projects/inventory-dashboard.jpg",
                category="Data Science",
                featured=False
            ),
            Project(
                title="Automated ETL Pipeline with ML Validation",
                description="Architected robust ETL pipelines using Selenium, BeautifulSoup, and Airflow with automated data validation using Great Expectations. Reduced manual intervention by 90% and processing time by 40% through intelligent scheduling and monitoring.",
                technologies="Selenium, BeautifulSoup, Airflow, Great Expectations, Pandas, SQLAlchemy, ETL",
                github_url="https://github.com/mrgarg-g1/web-scraping-framework",
                live_url="",
                image_url="/images/projects/web-scraping.jpg",
                category="Data Engineering",
                featured=False
            ),
            Project(
                title="Multi-Cloud Churn Prediction System",
                description="Deployed ensemble ML models across AWS SageMaker and Azure ML for customer churn prediction with 85% accuracy. Implemented real-time inference pipelines and A/B testing framework, enabling proactive retention strategies saving $2M+ annually.",
                technologies="AWS SageMaker, Azure ML, Ensemble Methods, XGBoost, Real-time ML, A/B Testing",
                github_url="https://github.com/mrgarg-g1/churn-prediction",
                live_url="",
                image_url="/images/projects/churn-prediction.jpg",
                category="Data Science",
                featured=True
            ),
            Project(
                title="OCR-powered Document Intelligence System",
                description="Integrated Google Cloud Vision OCR with custom post-processing algorithms for large-scale document digitization. Built automated data extraction pipelines processing 10K+ documents daily with 94% accuracy using advanced text recognition and NLP.",
                technologies="Google Cloud Vision, OCR, Document AI, Text Processing, NLP, Automated Extraction",
                github_url="https://github.com/mrgarg-g1/document-intelligence",
                live_url="",
                image_url="/images/projects/document-ocr.jpg",
                category="AI/ML",
                featured=False
            )
        ]
        
        for project in projects:
            db.add(project)
        
        # Seed Blog Posts - AI/ML & Data Science Insights
        blog_posts = [
            BlogPost(
                title="Fine-tuning LLaMA 3.1 for Government Policy Q&A: A Complete Guide",
                content="<p>Large Language Models have revolutionized how we interact with complex information, but getting them to understand specialized domains requires careful fine-tuning...</p>",
                excerpt="Deep dive into fine-tuning Meta's LLaMA 3.1-8B model for government policy understanding. Learn about LoRA techniques, RAG pipelines, and achieving 92% accuracy in policy Q&A systems.",
                slug="fine-tuning-llama-government-policy-qa",
                image_url="/images/blog/llama-fine-tuning.jpg",
                published=True
            ),
            BlogPost(
                title="Building Semantic Video Search with AWS: From Transcription to Discovery",
                content="<p>Video content is exploding, but finding specific moments within hours of footage remains challenging. Here's how I built a semantic video search engine using AWS services...</p>",
                excerpt="Learn how to architect a scalable semantic video search system using AWS Transcribe, SageMaker embeddings, and OpenSearch. Achieve 95% accuracy in timestamp-based video discovery.",
                slug="semantic-video-search-aws-architecture",
                image_url="/images/blog/semantic-video-search.jpg",
                published=True
            ),
            BlogPost(
                title="Multi-Cloud ML: Deploying Models Across AWS and Azure for Maximum Reliability",
                content="<p>In enterprise environments, vendor lock-in is a real concern. Here's how I architected a multi-cloud ML system that leverages both AWS SageMaker and Azure ML...</p>",
                excerpt="Explore strategies for deploying machine learning models across multiple cloud platforms. Real-world case study of a churn prediction system that saves $2M+ annually.",
                slug="multi-cloud-ml-deployment-strategy",
                image_url="/images/blog/multi-cloud-ml.jpg",
                published=True
            ),
            BlogPost(
                title="From ETL to Intelligence: Building Self-Healing Data Pipelines",
                content="<p>Data pipelines break. It's not a matter of if, but when. Here's how I built intelligent ETL systems that detect, diagnose, and often fix themselves...</p>",
                excerpt="Discover how to build robust ETL pipelines with automated validation using Great Expectations and Airflow. Reduce manual intervention by 90% and processing time by 40%.",
                slug="self-healing-data-pipelines-airflow",
                image_url="/images/blog/etl-pipelines.jpg",
                published=True
            )
        ]
        
        for post in blog_posts:
            db.add(post)
        
        # Seed Skills - AI/ML & Data Science Expertise
        skills = [
            # AI/ML Core
            Skill(name="Machine Learning & Deep Learning", level=95, category="AI/ML", icon="brain"),
            Skill(name="Large Language Models (LLMs)", level=93, category="AI/ML", icon="language"),
            Skill(name="Natural Language Processing", level=92, category="AI/ML", icon="nlp"),
            Skill(name="Computer Vision & OCR", level=90, category="AI/ML", icon="vision"),
            Skill(name="Generative AI & RAG Systems", level=91, category="AI/ML", icon="generate"),
            
            # Programming & Data Science
            Skill(name="Python (Pandas, NumPy, Scikit-learn)", level=96, category="Programming", icon="python"),
            Skill(name="SQL & Database Optimization", level=92, category="Data", icon="database"),
            Skill(name="Statistical Analysis & Modeling", level=89, category="Data Science", icon="statistics"),
            Skill(name="Time-Series Forecasting", level=87, category="Data Science", icon="timeseries"),
            
            # Multi-Cloud Platforms
            Skill(name="AWS (SageMaker, Bedrock, Lambda)", level=91, category="Cloud", icon="aws"),
            Skill(name="Azure (ML Studio, Cognitive Services)", level=85, category="Cloud", icon="azure"),
            Skill(name="Google Cloud (Vision AI, AutoML)", level=83, category="Cloud", icon="gcp"),
            
            # MLOps & Engineering
            Skill(name="Docker & Containerization", level=88, category="DevOps", icon="docker"),
            Skill(name="Airflow & ETL Pipelines", level=89, category="Data Engineering", icon="pipeline"),
            Skill(name="Model Deployment & Monitoring", level=87, category="MLOps", icon="deploy"),
            
            # AI Frameworks & Tools
            Skill(name="TensorFlow & PyTorch", level=90, category="Frameworks", icon="tensorflow"),
            Skill(name="Hugging Face Transformers", level=93, category="Frameworks", icon="huggingface"),
            Skill(name="LangChain & Vector Databases", level=89, category="Frameworks", icon="langchain"),
            
            # Visualization & Analytics
            Skill(name="Power BI & Advanced Analytics", level=91, category="Visualization", icon="powerbi"),
            Skill(name="Tableau & Data Storytelling", level=87, category="Visualization", icon="tableau"),
            Skill(name="Streamlit & Interactive Dashboards", level=90, category="Visualization", icon="streamlit")
        ]
        
        for skill in skills:
            db.add(skill)
        
        # Seed Experience - Deepak Garg's Career Journey
        experiences = [
            Experience(
                company="Appsquadz Technologies (AWS Partner)",
                position="Data Scientist & AI Engineer",
                description="Architecting enterprise AI solutions including semantic video search systems, LLM fine-tuning, and cloud-based ML pipelines. Led development of auto-categorization models reducing manual effort by 40% and churn prediction systems with 85% accuracy.",
                start_date=datetime(2025, 1, 1),
                end_date=None,
                current=True,
                location="Noida, Uttar Pradesh"
            ),
            Experience(
                company="Areness",
                position="Data Analyst & ML Engineer",
                description="Developed automated web scraping pipelines and ETL frameworks, reducing manual intervention by 90%. Implemented end-to-end data validation with Great Expectations and optimized processing pipelines using Airflow, achieving 40% runtime reduction.",
                start_date=datetime(2024, 6, 1),
                end_date=datetime(2024, 12, 31),
                current=False,
                location="Gurugram, Haryana"
            ),
            Experience(
                company="Freelance AI/ML Consultant",
                position="AI Engineer & Data Analyst",
                description="Delivered AI solutions for Golden Metal Corporation (inventory optimization with 20% efficiency gain) and S.O Infotech (BERT-based document classification with 92% accuracy). Built custom NER models and fraud detection systems.",
                start_date=datetime(2023, 7, 1),
                end_date=datetime(2024, 5, 31),
                current=False,
                location="Remote & On-site"
            ),
            Experience(
                company="D.S. Projects Pvt. Ltd.",
                position="AI Engineer",
                description="Pioneered real-time facial recognition systems using OpenCV and TensorFlow with 95% accuracy. Created Power BI dashboards for live monitoring and automated reporting systems, saving 5+ hours weekly of manual effort.",
                start_date=datetime(2022, 6, 1),
                end_date=datetime(2023, 6, 30),
                current=False,
                location="Hybrid"
            )
        ]
        
        for exp in experiences:
            db.add(exp)
        
        # Seed Education - Deepak Garg's Academic Background
        education = [
            Education(
                institution="Chandigarh University",
                degree="Bachelor of Technology (B.Tech)",
                field="Computer Science Engineering",
                start_date=datetime(2017, 8, 1),
                end_date=datetime(2021, 6, 30),
                current=False,
                description="Specialized in Data Structures, Algorithms, Machine Learning, and Software Engineering. Graduated with Distinction. Focused on AI/ML coursework and research projects in computer vision and natural language processing."
            ),
            Education(
                institution="Government Senior Secondary School",
                degree="Senior Secondary (12th Grade)",
                field="Science (PCM - Physics, Chemistry, Mathematics)",
                start_date=datetime(2015, 4, 1),
                end_date=datetime(2017, 3, 31),
                current=False,
                description="Science stream with Mathematics, Physics, and Chemistry. Strong foundation in analytical thinking and problem-solving. Scored 88% with distinction in Mathematics and Physics."
            )
        ]
        
        for edu in education:
            db.add(edu)
        
        # Seed Services - AI/ML & Data Science Solutions
        services = [
            Service(
                title="AI/ML Model Development",
                description="Custom machine learning solutions from concept to deployment. Specializing in LLM fine-tuning, computer vision, NLP, and predictive analytics with enterprise-grade scalability.",
                icon="brain",
                featured=True
            ),
            Service(
                title="Multi-Cloud ML Architecture",
                description="Vendor-agnostic ML systems across AWS, Azure, and Google Cloud. Build resilient, scalable AI infrastructure with automated failover and cost optimization.",
                icon="cloud",
                featured=True
            ),
            Service(
                title="Intelligent Data Engineering",
                description="End-to-end data pipelines with automated validation, monitoring, and self-healing capabilities. Transform raw data into actionable insights with 99.9% reliability.",
                icon="database",
                featured=True
            ),
            Service(
                title="Document Intelligence & OCR",
                description="Extract, classify, and analyze documents at scale using advanced OCR, NLP, and computer vision. Perfect for legal, insurance, and compliance use cases.",
                icon="document",
                featured=False
            ),
            Service(
                title="Predictive Analytics & Forecasting",
                description="Time-series forecasting, churn prediction, and demand planning using advanced statistical models and ML techniques. Turn data into competitive advantage.",
                icon="analytics",
                featured=False
            ),
            Service(
                title="AI Strategy & Consulting",
                description="Strategic AI roadmap development, technology assessment, and implementation planning. From POC to production-ready AI systems that deliver measurable ROI.",
                icon="consulting",
                featured=False
            )
        ]
        
        for service in services:
            db.add(service)
        
        # Seed Testimonials - Real Client Success Stories
        testimonials = [
            Testimonial(
                name="Rajesh Kumar",
                position="CEO",
                company="Golden Metal Corporation",
                content="Deepak's inventory optimization system transformed our operations. The ML-powered forecasting reduced our stock wastage by 20% and improved procurement efficiency significantly. His attention to detail and technical expertise is exceptional.",
                image_url="/images/testimonials/rajesh-kumar.jpg",
                rating=5,
                featured=True
            ),
            Testimonial(
                name="Priya Sharma",
                position="CTO",
                company="S.O. Infotech",
                content="The document classification system Deepak built using BERT has been a game-changer for our legal department. 92% accuracy in document classification and 60% reduction in manual review time. Outstanding work!",
                image_url="/images/testimonials/priya-sharma.jpg",
                rating=5,
                featured=True
            ),
            Testimonial(
                name="Michael Anderson",
                position="Data Science Manager",
                company="Areness",
                content="Deepak's expertise in web scraping and ETL automation saved us countless hours. His pipelines reduced manual intervention by 90% and improved data quality dramatically. A true professional with deep technical skills.",
                image_url="/images/testimonials/michael-anderson.jpg",
                rating=5,
                featured=True
            ),
            Testimonial(
                name="Dr. Anita Gupta",
                position="Project Director",
                company="D.S. Projects Pvt. Ltd.",
                content="The facial recognition attendance system Deepak developed achieved 95% accuracy and revolutionized our security protocols. His innovative approach to computer vision problems is remarkable.",
                image_url="/images/testimonials/anita-gupta.jpg",
                rating=5,
                featured=False
            ),
            Testimonial(
                name="Tech Team",
                position="Engineering Manager",
                company="Appsquadz Technologies",
                content="Deepak's semantic video search system and LLM fine-tuning projects showcase his deep understanding of cutting-edge AI technologies. His multi-cloud expertise and scalable solutions are impressive.",
                image_url="/images/testimonials/appsquadz-team.jpg",
                rating=5,
                featured=True
            )
        ]
        
        for testimonial in testimonials:
            db.add(testimonial)
        
        # Commit all changes
        db.commit()
        print("Database seeded successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"Error seeding database: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()
