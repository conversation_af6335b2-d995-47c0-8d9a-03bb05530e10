from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import Experience
from app.schemas import ExperienceResponse
from typing import List

router = APIRouter()

@router.get("/", response_model=List[ExperienceResponse])
async def get_experiences(db: Session = Depends(get_db)):
    """Get all work experiences"""
    experiences = db.query(Experience).order_by(Experience.start_date.desc()).all()
    return experiences
