from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import Testimonial
from app.schemas import TestimonialResponse
from typing import List

router = APIRouter()

@router.get("/", response_model=List[TestimonialResponse])
async def get_testimonials(db: Session = Depends(get_db)):
    """Get all testimonials"""
    testimonials = db.query(Testimonial).all()
    return testimonials
